{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\InfoEscalation\\Wechat.vue", "mtime": 1753859552953}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYXBpIGZyb20gIkAvYXBpL1NjaWVuY2VBcGkvaW5kZXguanMiOw0KaW1wb3J0IHsNCiAgbGlzdFdvcmssDQogIGdldFdvcmssDQogIGRlbFdvcmssDQogIGFkZFdvcmssDQogIHVwZGF0ZVdvcmssDQp9IGZyb20gIkAvYXBpL2FydGljbGUvd29yayI7DQppbXBvcnQgeyBsaXN0S2V5d29yZHMgfSBmcm9tICJAL2FwaS9hcnRpY2xlL2tleXdvcmRzIjsNCmltcG9ydCBBUEkgZnJvbSAiQC9hcGkvU2NpZW5jZUFwaS9pbmRleC5qcyI7DQppbXBvcnQgew0KICBsaXN0QXJ0aWNsZUhpc3RvcnksDQogIGRlbEFydGljbGVIaXN0b3J5LA0KICBhZGRBcnRpY2xlSGlzdG9yeSwNCiAgY2xlYW5BcnRpY2xlSGlzdG9yeSwNCiAgZ2V0TGlzdEJ5SWRzLA0KfSBmcm9tICJAL2FwaS9hcnRpY2xlL2FydGljbGVIaXN0b3J5IjsNCmltcG9ydCB7IFNwbGl0cGFuZXMsIFBhbmUgfSBmcm9tICJzcGxpdHBhbmVzIjsNCmltcG9ydCAic3BsaXRwYW5lcy9kaXN0L3NwbGl0cGFuZXMuY3NzIjsNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICJ2dWV4IjsNCmltcG9ydCBUcmVlVGFibGUgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVUYWJsZS9pbmRleC52dWUiOw0KaW1wb3J0IHsgZGVlcHNlZWtBaVFhLCBkaWZ5QWlRYSwgb2xsYW1hQWlRYSB9IGZyb20gIkAvYXBpL2luZm9Fc2NhbGF0aW9uL2FpIjsNCmltcG9ydCB7IG1hcmtlZCB9IGZyb20gIm1hcmtlZCI7DQppbXBvcnQgeyBnZXRDb25maWdLZXkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29uZmlnIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IFNwbGl0cGFuZXMsIFBhbmUsIFRyZWVUYWJsZSB9LA0KICBkaWN0czogWyJpc190ZWNobm9sb2d5Il0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgdGFibGVMb2FkaW5nOiBmYWxzZSwgLy8g6KGo5qC8bG9hZGluZ+eKtuaAgQ0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgaWQ6IDEwMCwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDUwLA0KICAgICAgICBkYXRlVHlwZTogNCwNCiAgICAgICAgdGFnczogIiIsDQogICAgICAgIHRhZ3NTdWJzZXQ6IFtdLA0KICAgICAgICBrZXl3b3JkczogIiIsDQogICAgICAgIGlzVGVjaG5vbG9neTogIjEiLA0KICAgICAgICBzb3J0TW9kZTogIjQiLA0KICAgICAgICBlbW90aW9uOiAiMCIsDQogICAgICAgIGhhc0NhY2hlOiAiMCIsDQogICAgICB9LA0KICAgICAgdG90YWw6IDAsDQogICAgICB0cmVlRGF0YVRyYW5zZmVyOiBbXSwgLy8g5Y6f5aeL5qCR5b2i5pWw5o2uDQogICAgICBmaWx0ZXJUZXh0OiAiIiwgLy8g5bem5L6n5qCR5pCc57Si5qCPDQogICAgICBjaGVja0xpc3Q6IFtdLCAvLyDlt6bkvqfli77pgInmlbDmja4NCiAgICAgIEFydGljbGVMaXN0OiBbXSwgLy8g5YiX6KGo5pWw5o2uDQogICAgICBjaGVja2VkOiBmYWxzZSwgLy8g5YWo6YCJDQogICAgICBpZHM6IFtdLCAvLyDpgInkuK3nmoTmlbDmja4NCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwgLy8g5re75Yqg5Yiw5oql5ZGK5by55qGGDQogICAgICByZXBvcnRPcHRpb25zOiBbXSwgLy8g5oql5ZGK5YiX6KGoDQogICAgICByZXBvcnRJZDogIiIsIC8vIOW3sumAieaLqeeahOaKpeWRig0KICAgICAgdGFnc0xpc3Q6IFtdLCAvLyDmo4DntKLor43lupPkuoznuqfliJfooagNCiAgICAgIHRhZ3NMaXN0MTogW10sIC8vIOajgOe0ouivjeW6k+S4gOe6p+WIl+ihqA0KICAgICAgY2hlY2tBbGw6IGZhbHNlLCAvLyDmo4DntKLor43lupPlhajpgIkNCiAgICAgIGlzSW5kZXRlcm1pbmF0ZTogdHJ1ZSwgLy8g5qOA57Si6K+N5bqT6YCJ5LqG5YC8DQogICAgICBzaG93SGlzdG9yeTogZmFsc2UsDQogICAgICBoaXN0b3J5TGlzdDogW10sDQogICAgICBoaXN0b3J5VGltZW91dDogbnVsbCwNCiAgICAgIGRpYWxvZ1Zpc2libGUxOiBmYWxzZSwNCiAgICAgIGhpc3RvcnlMb2FkaW5nOiBmYWxzZSwNCiAgICAgIHF1ZXJ5UGFyYW1zMTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9LA0KICAgICAgdG90YWwxOiAwLA0KICAgICAgaGlzdG9yeUxpc3QxOiBbXSwNCiAgICAgIHNob3dTdW1tYXJ5OiB0cnVlLA0KICAgICAgLyog5qCR5b2i5YiG6aG15pWw5o2uICovDQogICAgICB0cmVlQ3VycmVudFBhZ2U6IDEsDQogICAgICB0cmVlUGFnZVNpemU6IDEwMCwNCiAgICAgIHRyZWVUb3RhbDogMCwNCiAgICAgIC8qIOWIneWni+WMluWujOaIkOagh+iusCAqLw0KICAgICAgaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgLyog5pCc57Si6Ziy5oqWICovDQogICAgICBzZWFyY2hEZWJvdW5jZVRpbWVyOiBudWxsLA0KICAgICAgLyog5p+l6K+i6Ziy5oqWICovDQogICAgICBxdWVyeURlYm91bmNlVGltZXI6IG51bGwsDQogICAgICAvKiDpmLLmraLph43lpI3mn6Xor6IgKi8NCiAgICAgIGlzUXVlcnlpbmc6IGZhbHNlLA0KICAgICAgLyog5qCH6K6w5Y+z5L6n562b6YCJ5p2h5Lu25piv5ZCm5Y+R55Sf5Y+Y5YyWICovDQogICAgICBpc1JpZ2h0RmlsdGVyOiBmYWxzZSwNCiAgICAgIC8qIOagh+iusOW3puS+p+agkeaYr+WQpumHjee9riAqLw0KICAgICAgaXNMZWZ0UmVzZXQ6IGZhbHNlLA0KICAgICAgLyog6YCJ5Lit55qE5pWw5o2u5rqQ5YiG57G7ICovDQogICAgICBzZWxlY3RlZENsYXNzaWZ5OiBudWxsLA0KICAgICAgLyog5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yI5rC45LmF5L+d5a2Y77yM5Y+q5pyJ54m55a6a5pON5L2c5omN5pu05paw77yJICovDQogICAgICBzYXZlZENoZWNrYm94RGF0YTogW10sDQogICAgICAvLyBhaeebuOWFsw0KICAgICAgYWlEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoYXRNZXNzYWdlczogW10sDQogICAgICBpc1RoaW5raW5nOiBmYWxzZSwNCiAgICAgIHVzZXJBdmF0YXI6ICIiLCAvLyDnlKjmiLflpLTlg48NCiAgICAgIHN0cmVhbWluZ01lc3NhZ2U6ICIiLCAvLyDmt7vliqDnlKjkuo7lrZjlgqjmraPlnKjmtYHlvI/ovpPlh7rnmoTmtojmga8NCiAgICAgIG1hcmtkb3duT3B0aW9uczogew0KICAgICAgICBnZm06IHRydWUsDQogICAgICAgIGJyZWFrczogdHJ1ZSwNCiAgICAgICAgaGVhZGVySWRzOiB0cnVlLA0KICAgICAgICBtYW5nbGU6IGZhbHNlLA0KICAgICAgICBoZWFkZXJQcmVmaXg6ICIiLA0KICAgICAgICBwZWRhbnRpYzogZmFsc2UsDQogICAgICAgIHNhbml0aXplOiBmYWxzZSwNCiAgICAgICAgc21hcnRMaXN0czogdHJ1ZSwNCiAgICAgICAgc21hcnR5cGFudHM6IHRydWUsDQogICAgICAgIHhodG1sOiB0cnVlLA0KICAgICAgfSwNCiAgICAgIGlzUmVxdWVzdGluZzogZmFsc2UsIC8vIOagh+iusOaYr+WQpuato+WcqOivt+axguS4rQ0KICAgICAgaXNBYm9ydGVkOiBmYWxzZSwgLy8g5qCH6K6w5piv5ZCm5bey5Lit5patDQogICAgICBjdXJyZW50UmVhZGVyOiBudWxsLCAvLyDlvZPliY3nmoQgcmVhZGVyDQogICAgICBhaVBsYXRmb3JtOiAiIiwNCiAgICAgIGFydGljbGVBaVByb21wdDogIiIsDQogICAgICBub2RlQ2hlY2tMaXN0OiBbXSwNCiAgICAgIGNoYXJ0RGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBjaGFydEh0bWw6ICIiLA0KICAgICAgY2hhcnRMb2FkaW5nOiB0cnVlLA0KICAgICAgY3VycmVudENoYXJ0SWZyYW1lOiBudWxsLCAvLyDmt7vliqDlj5jph4/ot5/ouKrlvZPliY1pZnJhbWUNCiAgICAgIGRpZnlBcGlrZXk6IHsNCiAgICAgICAgYXJ0aWNsZTogIiIsDQogICAgICAgIGNoYXJ0OiAiIiwNCiAgICAgIH0sDQogICAgICBjaGFydFByb21wdDogIiIsDQogICAgICBnbG9iYWxMb2FkaW5nOiBmYWxzZSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIOebkeWQrOetm+mAieadoeS7tuWPmOWMlg0KICAgICJxdWVyeVBhcmFtcy5kYXRlVHlwZSI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8IG5ld1ZhbCA9PT0gb2xkVmFsKSByZXR1cm47DQogICAgICAgIHRoaXMuaGFuZGxlUmlnaHRGaWx0ZXJDaGFuZ2UoKTsNCiAgICAgIH0sDQogICAgfSwNCiAgICAicXVlcnlQYXJhbXMuaXNUZWNobm9sb2d5Ijogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgICJxdWVyeVBhcmFtcy5lbW90aW9uIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5oYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgICJxdWVyeVBhcmFtcy50YWdzIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCg0KICAgICAgICAvLyB0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3NTdWJzZXQgPSBbXTsNCiAgICAgICAgdGhpcy5jaGVja0FsbCA9IHRydWU7DQogICAgICAgIHRoaXMuaXNJbmRldGVybWluYXRlID0gZmFsc2U7DQoNCiAgICAgICAgaWYgKG5ld1ZhbCAhPSAiIikgew0KICAgICAgICAgIC8vIOS4jeWcqOi/memHjOiuvue9rnRhYmxlTG9hZGluZ++8jOiuqeWQjue7reeahHF1ZXJ5QXJ0aWNsZUxpc3TmnaXlpITnkIYNCiAgICAgICAgICBsaXN0S2V5d29yZHMoeyBwYXJlbnRJZDogbmV3VmFsLCBwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAgfSkNCiAgICAgICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy50YWdzTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICB0aGlzLmhhbmRsZUNoZWNrQWxsVGFnc1N1YnNldCh0cnVlKTsNCiAgICAgICAgICAgICAgLy8gdGhpcy5oYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpOw0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5qOA57Si6K+N5bqT5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W5qOA57Si6K+N5bqT5aSx6LSlIik7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgfSwNCiAgICAicXVlcnlQYXJhbXMudGFnc1N1YnNldCI6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsNCiAgICAgICAgaWYgKA0KICAgICAgICAgICF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkIHx8DQogICAgICAgICAgSlNPTi5zdHJpbmdpZnkobmV3VmFsKSA9PT0gSlNPTi5zdHJpbmdpZnkob2xkVmFsKQ0KICAgICAgICApDQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB0aGlzLmhhbmRsZVJpZ2h0RmlsdGVyQ2hhbmdlKCk7DQogICAgICB9LA0KICAgIH0sDQogICAgInF1ZXJ5UGFyYW1zLnNvcnRNb2RlIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICBpZiAoIXRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgfHwgbmV3VmFsID09PSBvbGRWYWwpIHJldHVybjsNCiAgICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgfSwNCiAgICB9LA0KICAgIGRpYWxvZ1Zpc2libGUodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIGFwaS5nZXROZXdCdWlsdCh7IHNvdXJjZVR5cGU6ICIxIiB9KS50aGVuKChkYXRhKSA9PiB7DQogICAgICAgICAgaWYgKGRhdGEuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMucmVwb3J0T3B0aW9ucyA9IGRhdGEuZGF0YTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLmiqXlkYrliJfooajojrflj5blpLHotKXkuoYiLCB0eXBlOiAiZXJyb3IiIH0pOw0KICAgICAgICAgICAgdGhpcy5jbG9zZVJlcG9ydCgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBHZXR0ZXJzKFsicm9sZXMiXSksDQogIH0sDQogIGFzeW5jIGNyZWF0ZWQoKSB7DQogICAgZ2V0Q29uZmlnS2V5KCJzeXMuYWkucGxhdGZvcm0iKS50aGVuKChyZXMpID0+IHsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5haVBsYXRmb3JtID0gcmVzLm1zZzsNCiAgICAgIH0NCiAgICB9KTsNCiAgICBnZXRDb25maWdLZXkoIndlY2hhdC5haS5hcnRpY2xlUHJvbXB0IikudGhlbigocmVzKSA9PiB7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0ID0gcmVzLm1zZzsNCiAgICAgIH0NCiAgICB9KTsNCiAgICBnZXRDb25maWdLZXkoIndlY2hhdC5haS5jaGFydFByb21wdCIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLmNoYXJ0UHJvbXB0ID0gcmVzLm1zZzsNCiAgICAgIH0NCiAgICB9KTsNCiAgICAvLyDojrflj5bnlKjmiLflpLTlg48NCiAgICB0aGlzLnVzZXJBdmF0YXIgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmF2YXRhcjsNCiAgICB0cnkgew0KICAgICAgLy8g5YWI5Yqg6L295Z+656GA5pWw5o2uDQogICAgICBQcm9taXNlLmFsbChbDQogICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKSwNCiAgICAgICAgbGlzdEtleXdvcmRzKHsgcGFyZW50SWQ6IDAsIHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLnRhZ3NMaXN0MSA9IHJlcy5kYXRhLmZpbHRlcigoaXRlbSkgPT4gaXRlbS5wYXJlbnRJZCA9PSAwKTsNCiAgICAgICAgfSksDQogICAgICBdKTsNCg0KICAgICAgLy8g5Yqg6L295qCR5pWw5o2u5ZKM5YaF5a655pWw5o2uDQogICAgICBhd2FpdCB0aGlzLmluaXRpYWxpemVEYXRhKCk7DQoNCiAgICAgIGlmICh0aGlzLnJvbGVzLmluY2x1ZGVzKCJpbmZvcm1hdGlvbiIpKSB7DQogICAgICAgIHRoaXMuc2hvd1N1bW1hcnkgPSBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5qCH6K6w5Yid5aeL5YyW5a6M5oiQ77yM6L+Z5qC3d2F0Y2jnm5HlkKzlmajmiY3kvJrlvIDlp4vlt6XkvZwNCiAgICAgIHRoaXMuaW5pdGlhbGl6YXRpb25Db21wbGV0ZWQgPSB0cnVlOw0KICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICBjb25zb2xlLmVycm9yKCLnu4Tku7bliJ3lp4vljJblpLHotKU6IiwgZXJyb3IpOw0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yid5aeL5YyW5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VIik7DQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7fSwNCiAgbWV0aG9kczogew0KICAgIC8vIOWIneWni+WMluaVsOaNrg0KICAgIGFzeW5jIGluaXRpYWxpemVEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5Yqg6L295paH56ug5YiX6KGo77yI5YaF6YOo5bey57uP5aSE55CG5LqGIHRhYmxlTG9hZGluZ++8iQ0KICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgICAgLy8g5Yqg6L295qCR5pWw5o2uDQogICAgICAgIGF3YWl0IHRoaXMucXVlcnlUcmVlRGF0YSgpOw0KICAgICAgICAvLyDnrYnlvoXmoJHnu4Tku7blrozlhajmuLLmn5MNCiAgICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIneWni+WMluaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIneWni+WMluWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlSIpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblj7PkvqfnrZvpgInmnaHku7blj5jljJYNCiAgICBoYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpIHsNCiAgICAgIHRoaXMuaXNSaWdodEZpbHRlciA9IHRydWU7IC8vIOagh+iusOWPs+S+p+etm+mAieadoeS7tuWPkeeUn+WPmOWMlg0KDQogICAgICAvLyDkuI3lho3kv53lrZjlvZPliY3pgInkuK3nirbmgIHvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4NCiAgICAgIC8vIOawuOS5heS/neWtmOeahOWLvumAieaVsOaNruS8muWcqOafpeivouWQjuiHquWKqOaBouWkjQ0KDQogICAgICAvLyDph43nva7liIbpobXliLDnrKzkuIDpobUNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhc0NhY2hlID0gIjAiOw0KDQogICAgICAvLyDmu5rliqjliLDpobbpg6gNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KDQogICAgICAvLyDlkIzml7bmn6Xor6LmoJHlkozliJfooagNCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDlkIzml7bmn6Xor6LmoJHlkozliJfooagNCiAgICBhc3luYyBxdWVyeVRyZWVBbmRMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5L+d5a2Y5b2T5YmN55qE5rC45LmF5Yu+6YCJ5pWw5o2u77yM6YG/5YWN5Zyo5p+l6K+i6L+H56iL5Lit5Lii5aSxDQogICAgICAgIGNvbnN0IHNhdmVkRGF0YSA9IFsuLi50aGlzLnNhdmVkQ2hlY2tib3hEYXRhXTsNCg0KICAgICAgICAvLyDlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzlhYjkuLTml7bmgaLlpI0gY2hlY2tMaXN0IOS7peS+v+afpeivouaXtuW4puS4iuWPguaVsA0KICAgICAgICBpZiAoc2F2ZWREYXRhICYmIHNhdmVkRGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbLi4uc2F2ZWREYXRhXTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDlpoLmnpzmsqHmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzmuIXnqbrpgInkuK3nirbmgIENCiAgICAgICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5ZCM5pe25p+l6K+i5qCR5pWw5o2u5ZKM5Y+z5L6n5YiX6KGo77yI5L+d5oyB5oCn6IO95LyY5Yq/77yJDQogICAgICAgIGF3YWl0IFByb21pc2UuYWxsKFsNCiAgICAgICAgICB0aGlzLnF1ZXJ5VHJlZURhdGEoKSwNCiAgICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKSwgLy8gcXVlcnlBcnRpY2xlTGlzdCDlhoXpg6jlt7Lnu4/lpITnkIbkuoYgdGFibGVMb2FkaW5nDQogICAgICAgIF0pOw0KDQogICAgICAgIC8vIOehruS/neawuOS5heS/neWtmOeahOWLvumAieaVsOaNruS4jeS8muS4ouWksQ0KICAgICAgICB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhID0gc2F2ZWREYXRhOw0KDQogICAgICAgIC8vIOafpeivouWujOaIkOWQju+8jOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOmdmem7mOaBouWkjeeVjOmdoumAieS4reeKtuaAgQ0KICAgICAgICBpZiAodGhpcy5zYXZlZENoZWNrYm94RGF0YSAmJiB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnJlc3RvcmVGcm9tU2F2ZWRDaGVja2JveERhdGEoKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOafpeivouWujOaIkOWQjumHjee9ruWPs+S+p+etm+mAieagh+iusA0KICAgICAgICB0aGlzLmlzUmlnaHRGaWx0ZXIgPSBmYWxzZTsNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5pc0xlZnRSZXNldCA9IGZhbHNlOw0KICAgICAgICB9LCAzMDApOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5ZCM5pe25p+l6K+i5qCR5ZKM5YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5p+l6K+i5aSx6LSl77yM6K+36YeN6K+VIik7DQogICAgICAgIC8vIOWNs+S9v+WHuumUmeS5n+imgemHjee9ruagh+iusA0KICAgICAgICB0aGlzLmlzUmlnaHRGaWx0ZXIgPSBmYWxzZTsNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5pc0xlZnRSZXNldCA9IGZhbHNlOw0KICAgICAgICB9LCAzMDApOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmgaLlpI3pgInkuK3mlbDmja7mupDnmoTmlrnms5Xlt7LliKDpmaTvvIzkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja4NCg0KICAgIC8vIOS7juawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaBouWkjemAieS4reeKtuaAge+8iOS7heWkhOeQhueVjOmdoumAieS4reeKtuaAge+8iQ0KICAgIHJlc3RvcmVGcm9tU2F2ZWRDaGVja2JveERhdGEoKSB7DQogICAgICBpZiAoIXRoaXMuc2F2ZWRDaGVja2JveERhdGEgfHwgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlnKjlvZPliY3moJHmlbDmja7kuK3mn6Xmib7ljLnphY3nmoTpobkNCiAgICAgIGNvbnN0IG1hdGNoZWRJdGVtcyA9IFtdOw0KICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5mb3JFYWNoKChzYXZlZEl0ZW0pID0+IHsNCiAgICAgICAgY29uc3QgZm91bmRJdGVtID0gdGhpcy50cmVlRGF0YVRyYW5zZmVyLmZpbmQoDQogICAgICAgICAgKHRyZWVJdGVtKSA9PiB0cmVlSXRlbS5zb3VyY2VTbiA9PT0gc2F2ZWRJdGVtLnNvdXJjZVNuDQogICAgICAgICk7DQogICAgICAgIGlmIChmb3VuZEl0ZW0pIHsNCiAgICAgICAgICBtYXRjaGVkSXRlbXMucHVzaChmb3VuZEl0ZW0pOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgaWYgKG1hdGNoZWRJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgIC8vIOabtOaWsOmAieS4reWIl+ihqO+8iOatpOaXtiBjaGVja0xpc3Qg5bey57uP5Zyo5p+l6K+i5YmN5oGi5aSN6L+H5LqG77yJDQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gbWF0Y2hlZEl0ZW1zOw0KICAgICAgICAvLyDpgJrnn6UgVHJlZVRhYmxlIOe7hOS7tuaBouWkjeeVjOmdoumAieS4reeKtuaAge+8iOS4jeinpuWPkeS6i+S7tu+8iQ0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuJHJlZnMudHJlZVRhYmxlKSB7DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWVUYWJsZS5yZXN0b3JlU2VsZWN0aW9uU2lsZW50bHkobWF0Y2hlZEl0ZW1zKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5rKh5pyJ5Yy56YWN6aG577yM5riF56m66YCJ5Lit54q25oCBDQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gW107DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOS/neWtmOW9k+WJjemAieS4reeKtuaAgeeahOaWueazleW3suWIoOmZpO+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KDQogICAgLy8g5p+l6K+i5qCR5pWw5o2u5bm25LuO5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u5oGi5aSN6YCJ5Lit54q25oCB77yI55So5LqO5YWz6ZSu5a2X6L+H5ruk77yJDQogICAgYXN5bmMgcXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5aaC5p6c5pyJ5rC45LmF5L+d5a2Y55qE5Yu+6YCJ5pWw5o2u77yM5YWI5Li05pe25oGi5aSNIGNoZWNrTGlzdCDku6Xkvr/mn6Xor6Lml7bluKbkuIrlj4LmlbANCiAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbLi4udGhpcy5zYXZlZENoZWNrYm94RGF0YV07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbXTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOafpeivouagkeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLnF1ZXJ5VHJlZURhdGEoKTsNCg0KICAgICAgICAvLyDmn6Xor6LlrozmiJDlkI7vvIzlpoLmnpzmnInmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIzpnZnpu5jmgaLlpI3nlYzpnaLpgInkuK3nirbmgIENCiAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5yZXN0b3JlRnJvbVNhdmVkQ2hlY2tib3hEYXRhKCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoDQogICAgICAgICAgIuafpeivouagkeaVsOaNruW5tuS7juawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaBouWkjemAieS4reeKtuaAgeWksei0pToiLA0KICAgICAgICAgIGVycm9yDQogICAgICAgICk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWIhumhteWkhOeQhg0KICAgIGhhbmRsZVBhZ2luYXRpb24oKSB7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDljoblj7LorrDlvZXliIbpobXlpITnkIYNCiAgICBoYW5kbGVIaXN0b3J5UGFnaW5hdGlvbigpIHsNCiAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkxKCk7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IGRpYWxvZ0NvbnRlbnQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIuZWwtZGlhbG9nX19ib2R5Iik7DQogICAgICAgIGlmIChkaWFsb2dDb250ZW50KSB7DQogICAgICAgICAgZGlhbG9nQ29udGVudC5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5p+l6K+i5qCR5pWw5o2uDQogICAgYXN5bmMgcXVlcnlUcmVlRGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgcGxhdGZvcm1UeXBlOiAwLA0KICAgICAgICAgIGlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmlkLA0KICAgICAgICAgIHBhZ2VOdW06IHRoaXMudHJlZUN1cnJlbnRQYWdlLA0KICAgICAgICAgIHBhZ2VTaXplOiB0aGlzLnRyZWVQYWdlU2l6ZSwNCiAgICAgICAgICBtOiAxLA0KICAgICAgICAgIGRhdGVUeXBlOg0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kYXRlVHlwZSAhPSA2ID8gdGhpcy5xdWVyeVBhcmFtcy5kYXRlVHlwZSA6ICIiLA0KICAgICAgICAgIHRhZ3M6IHRoaXMucXVlcnlQYXJhbXMudGFncywNCiAgICAgICAgICB0YWdzU3Vic2V0OiB0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3NTdWJzZXQsDQogICAgICAgICAga2V5d29yZHM6IHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMsDQogICAgICAgICAgaXNUZWNobm9sb2d5OiB0aGlzLnF1ZXJ5UGFyYW1zLmlzVGVjaG5vbG9neSwNCiAgICAgICAgICBlbW90aW9uOiB0aGlzLnF1ZXJ5UGFyYW1zLmVtb3Rpb24sDQogICAgICAgICAgbGFiZWw6IHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldC5qb2luKCIsIiksDQogICAgICAgICAgLy8g5re75Yqg5YWz6ZSu5a2X6L+H5ruk5Y+C5pWwDQogICAgICAgICAgZmlsdGVyd29yZHM6IHRoaXMuZmlsdGVyVGV4dCB8fCAiIiwNCiAgICAgICAgICAvLyDmt7vliqDmlbDmja7mupDliIbnsbvlj4LmlbANCiAgICAgICAgICB0aGlua1RhbmtDbGFzc2lmaWNhdGlvbjogdGhpcy5zZWxlY3RlZENsYXNzaWZ5LA0KICAgICAgICAgIGhhc0NhY2hlOiB0aGlzLnF1ZXJ5UGFyYW1zLmhhc0NhY2hlLA0KICAgICAgICB9Ow0KDQogICAgICAgIGlmICghdGhpcy5xdWVyeVBhcmFtcy50YWdzKSB7DQogICAgICAgICAgcGFyYW1zLnRhZ3NTdWJzZXQgPSBbXTsNCiAgICAgICAgICBwYXJhbXMubGFiZWwgPSAiIjsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGFwaS5tb25pdG9yaW5nTWVkaXVtKHBhcmFtcyk7DQoNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICBjb25zdCBkYXRhTGlzdCA9IHJlcy5yb3dzIHx8IFtdOw0KICAgICAgICAgIGNvbnN0IHRvdGFsID0gcmVzLnRvdGFsIHx8IDA7DQoNCiAgICAgICAgICBjb25zdCBtYXBEYXRhID0gKGRhdGEpID0+DQogICAgICAgICAgICBkYXRhLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgICAgIGlkOiBgJHsNCiAgICAgICAgICAgICAgICBpdGVtLnNvdXJjZVNuIHx8ICJ1bmtub3duIg0KICAgICAgICAgICAgICB9XyR7aW5kZXh9XyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpDQogICAgICAgICAgICAgICAgLnRvU3RyaW5nKDM2KQ0KICAgICAgICAgICAgICAgIC5zdWJzdHJpbmcoMiwgMTEpfWAsIC8vIOehruS/nee7neWvueWUr+S4gOaApw0KICAgICAgICAgICAgICBsYWJlbDogaXRlbS5jbk5hbWUsDQogICAgICAgICAgICAgIGNvdW50OiBpdGVtLmFydGljbGVDb3VudCB8fCAwLA0KICAgICAgICAgICAgICBvcmRlck51bTogaXRlbS5vcmRlck51bSwNCiAgICAgICAgICAgICAgY291bnRyeTogaXRlbS5jb3VudHJ5T2ZPcmlnaW4gfHwgbnVsbCwNCiAgICAgICAgICAgICAgc291cmNlU246IGl0ZW0uc291cmNlU24sDQogICAgICAgICAgICAgIHVybDogaXRlbS51cmwgfHwgbnVsbCwNCiAgICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIHRoaXMudHJlZURhdGFUcmFuc2ZlciA9IG1hcERhdGEoZGF0YUxpc3QpOw0KICAgICAgICAgIHRoaXMudHJlZVRvdGFsID0gdG90YWw7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuafpeivouagkeaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluaVsOaNrua6kOWksei0pSIpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOafpeivouaWh+eroOWIl+ihqO+8iOW4pumYsuaKlu+8iQ0KICAgIGFzeW5jIHF1ZXJ5QXJ0aWNsZUxpc3QoZmxhZykgew0KICAgICAgLy8g6Ziy5q2i6YeN5aSN5p+l6K+iDQogICAgICBpZiAodGhpcy5pc1F1ZXJ5aW5nKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgaWYgKCFmbGFnKSB7DQogICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIH0NCg0KICAgICAgLy8g5riF6Zmk5LmL5YmN55qE6Ziy5oqW5a6a5pe25ZmoDQogICAgICBpZiAodGhpcy5xdWVyeURlYm91bmNlVGltZXIpIHsNCiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u6Ziy5oqW77yMMzAwbXPlkI7miafooYzmn6Xor6INCiAgICAgIHRoaXMucXVlcnlEZWJvdW5jZVRpbWVyID0gc2V0VGltZW91dChhc3luYyAoKSA9PiB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgaWYgKGZsYWcgPT09ICJzb3VyY2VJdGVtQ2hhbmdlZCIpIHsNCiAgICAgICAgICAgIHRoaXMuZ2xvYmFsTG9hZGluZyA9IHRydWU7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy5pc1F1ZXJ5aW5nID0gdHJ1ZTsNCg0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIG06IDEsDQogICAgICAgICAgICBwYWdlTnVtOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sDQogICAgICAgICAgICBwYWdlU2l6ZTogdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSwNCiAgICAgICAgICAgIGlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmlkLA0KICAgICAgICAgICAgaXNTb3J0OiB0aGlzLnF1ZXJ5UGFyYW1zLnNvcnRNb2RlLA0KICAgICAgICAgICAgZGF0ZVR5cGU6DQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGF0ZVR5cGUgIT0gNiA/IHRoaXMucXVlcnlQYXJhbXMuZGF0ZVR5cGUgOiAiIiwNCiAgICAgICAgICAgIHRhZ3M6IHRoaXMucXVlcnlQYXJhbXMudGFncywNCiAgICAgICAgICAgIHRhZ3NTdWJzZXQ6IHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCwNCiAgICAgICAgICAgIGtleXdvcmRzOiB0aGlzLnF1ZXJ5UGFyYW1zLmtleXdvcmRzLA0KICAgICAgICAgICAgaXNUZWNobm9sb2d5OiB0aGlzLnF1ZXJ5UGFyYW1zLmlzVGVjaG5vbG9neSwNCiAgICAgICAgICAgIGVtb3Rpb246IHRoaXMucXVlcnlQYXJhbXMuZW1vdGlvbiwNCiAgICAgICAgICAgIGxhYmVsOiB0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3NTdWJzZXQuam9pbigiLCIpLA0KICAgICAgICAgICAgcGxhdGZvcm1UeXBlOiAwLA0KICAgICAgICAgIH07DQoNCiAgICAgICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMudGFncykgew0KICAgICAgICAgICAgcGFyYW1zLnRhZ3NTdWJzZXQgPSBbXTsNCiAgICAgICAgICAgIHBhcmFtcy5sYWJlbCA9ICIiOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNruaehOW7uuafpeivouWPguaVsA0KICAgICAgICAgIGlmICh0aGlzLnNhdmVkQ2hlY2tib3hEYXRhICYmIHRoaXMuc2F2ZWRDaGVja2JveERhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY29uc3QgZGF0YSA9IHRoaXMuc2F2ZWRDaGVja2JveERhdGEubWFwKChpdGVtKSA9PiBpdGVtLmxhYmVsKTsNCiAgICAgICAgICAgIGNvbnN0IHNvdXJjZVNuID0gdGhpcy5zYXZlZENoZWNrYm94RGF0YS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiBpdGVtLnNvdXJjZVNuDQogICAgICAgICAgICApOw0KDQogICAgICAgICAgICBwYXJhbXMud2VDaGF0TmFtZSA9IFN0cmluZyhkYXRhKTsNCiAgICAgICAgICAgIHBhcmFtcy5zb3VyY2VTbiA9IFN0cmluZyhzb3VyY2VTbik7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6K6w5b2V5YWz6ZSu6K+N5Y6G5Y+yDQogICAgICAgICAgaWYgKHBhcmFtcy5rZXl3b3Jkcykgew0KICAgICAgICAgICAgYWRkQXJ0aWNsZUhpc3RvcnkoeyBrZXl3b3JkOiBwYXJhbXMua2V5d29yZHMsIHR5cGU6IDEgfSkudGhlbigNCiAgICAgICAgICAgICAgKCkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZ2V0QXJ0aWNsZUhpc3RvcnkoKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBhcGkuZXNSZXRyaWV2YWwocGFyYW1zKTsNCg0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIGxldCBhcnRpY2xlTGlzdCA9IHJlcy5kYXRhLmxpc3QNCiAgICAgICAgICAgICAgPyByZXMuZGF0YS5saXN0Lm1hcCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICAgICAgaXRlbS5jblRpdGxlID0gaXRlbS5jblRpdGxlDQogICAgICAgICAgICAgICAgICAgID8gdGhpcy5jaGFuZ2VDb2xvcihpdGVtLmNuVGl0bGUpDQogICAgICAgICAgICAgICAgICAgIDogbnVsbDsNCiAgICAgICAgICAgICAgICAgIGl0ZW0udGl0bGUgPSB0aGlzLmNoYW5nZUNvbG9yKGl0ZW0udGl0bGUpOw0KICAgICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW07DQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgOiBbXTsNCg0KICAgICAgICAgICAgLy8g5Y676YeN6YC76L6R77ya5Y+q5pyJ5Zyo5rKh5pyJ5YWz6ZSu6K+N5pCc57Si5pe25omN6L+b6KGM5Y676YeNDQogICAgICAgICAgICBpZiAoDQogICAgICAgICAgICAgICF0aGlzLnF1ZXJ5UGFyYW1zLmtleXdvcmRzIHx8DQogICAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMudHJpbSgpID09PSAiIg0KICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgIGFydGljbGVMaXN0ID0gdGhpcy5kZWR1cGxpY2F0ZUFydGljbGVzKGFydGljbGVMaXN0KTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdCA9IGFydGljbGVMaXN0Ow0KICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy5kYXRhLnRvdGFsIHx8IDA7DQoNCiAgICAgICAgICAgIC8vIOWmguaenOacieawuOS5heS/neWtmOeahOWLvumAieaVsOaNru+8jOaBouWkjemAieS4reeKtuaAge+8iOmdmem7mOaBouWkje+8jOS4jeinpuWPkeWPs+S+p+afpeivou+8iQ0KICAgICAgICAgICAgaWYgKHRoaXMuc2F2ZWRDaGVja2JveERhdGEgJiYgdGhpcy5zYXZlZENoZWNrYm94RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgIHRoaXMucmVzdG9yZUZyb21TYXZlZENoZWNrYm94RGF0YSgpOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDlpITnkIbliIbpobXkuLrnqbrnmoTmg4XlhrUNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgdGhpcy5BcnRpY2xlTGlzdC5sZW5ndGggPT0gMCAmJg0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplICogKHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSAtIDEpID49DQogICAgICAgICAgICAgICAgdGhpcy50b3RhbCAmJg0KICAgICAgICAgICAgICB0aGlzLnRvdGFsICE9IDANCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSBNYXRoLm1heCgNCiAgICAgICAgICAgICAgICAxLA0KICAgICAgICAgICAgICAgIE1hdGguY2VpbCh0aGlzLnRvdGFsIC8gdGhpcy5xdWVyeVBhcmFtcy5wYWdlU2l6ZSkNCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgICAgLy8g6YeN5paw5p+l6K+iDQogICAgICAgICAgICAgIGF3YWl0IHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICAgICAgICByZXR1cm47IC8vIOmHjeaWsOafpeivouaXtuS4jeimgeWFs+mXrWxvYWRpbmcNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICLojrflj5bmlbDmja7lpLHotKUiKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5p+l6K+i5paH56ug5YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmn6Xor6LlpLHotKXvvIzor7fph43or5UiKTsNCiAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICB0aGlzLmlzUXVlcnlpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmdsb2JhbExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IGZhbHNlOyAvLyDmn6Xor6LlrozmiJDlkI7lhbPpl61sb2FkaW5nDQogICAgICAgIH0NCiAgICAgIH0sIDEwMDApOw0KICAgIH0sDQoNCiAgICAvLyBUcmVlVGFibGUg57uE5Lu25LqL5Lu25aSE55CG5pa55rOVDQoNCiAgICAvLyDlpITnkIbpgInmi6nlj5jljJYNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0ZWREYXRhLCBvcGVyYXRpb25UeXBlKSB7DQogICAgICBpZiAob3BlcmF0aW9uVHlwZSA9PT0gInJvdy1jbGljayIgfHwgb3BlcmF0aW9uVHlwZSA9PT0gImNsZWFyLWFsbCIpIHsNCiAgICAgICAgLy8g54K55Ye76KGM77yI5Y2V6YCJ77yJ5oiW5Y+W5raI5omA5pyJ6YCJ5Lit77ya55u05o6l5pu/5o2i77yM5LiN6ZyA6KaB6L+95Yqg5Y676YeNDQogICAgICAgIHRoaXMuY2hlY2tMaXN0ID0gWy4uLnNlbGVjdGVkRGF0YV07DQogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBbLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgIH0gZWxzZSBpZiAoDQogICAgICAgIG9wZXJhdGlvblR5cGUgPT09ICJjaGVja2JveC1jaGFuZ2UiIHx8DQogICAgICAgIG9wZXJhdGlvblR5cGUgPT09ICJzZWxlY3QtYWxsIg0KICAgICAgKSB7DQogICAgICAgIC8vIOeCueWHu+WLvumAieahhu+8iOWkmumAie+8ieaIluWFqOmAie+8mumcgOimgeato+ehruWkhOeQhumAieS4reWSjOWPlua2iOmAieS4rQ0KICAgICAgICAvLyDlhYjku47kv53lrZjnmoTmlbDmja7kuK3np7vpmaTlvZPliY3pobXpnaLnmoTmiYDmnInmlbDmja4NCiAgICAgICAgY29uc3QgY3VycmVudFBhZ2VJZHMgPSB0aGlzLnRyZWVEYXRhVHJhbnNmZXIubWFwKA0KICAgICAgICAgIChpdGVtKSA9PiBpdGVtLnNvdXJjZVNuDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IGZpbHRlcmVkQ2hlY2tMaXN0ID0gdGhpcy5jaGVja0xpc3QuZmlsdGVyKA0KICAgICAgICAgIChpdGVtKSA9PiAhY3VycmVudFBhZ2VJZHMuaW5jbHVkZXMoaXRlbS5zb3VyY2VTbikNCiAgICAgICAgKTsNCiAgICAgICAgY29uc3QgZmlsdGVyZWRTYXZlZERhdGEgPSB0aGlzLnNhdmVkQ2hlY2tib3hEYXRhLmZpbHRlcigNCiAgICAgICAgICAoaXRlbSkgPT4gIWN1cnJlbnRQYWdlSWRzLmluY2x1ZGVzKGl0ZW0uc291cmNlU24pDQogICAgICAgICk7DQoNCiAgICAgICAgLy8g54S25ZCO5re75Yqg5b2T5YmN6aG16Z2i5paw6YCJ5Lit55qE5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbWJpbmVkQ2hlY2tMaXN0ID0gWy4uLmZpbHRlcmVkQ2hlY2tMaXN0LCAuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgICBjb25zdCBjb21iaW5lZFNhdmVkRGF0YSA9IFsuLi5maWx0ZXJlZFNhdmVkRGF0YSwgLi4uc2VsZWN0ZWREYXRhXTsNCg0KICAgICAgICAvLyDlr7nlkIjlubblkI7nmoTmlbDmja7ov5vooYzljrvph43lpITnkIYNCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSB0aGlzLmRlZHVwbGljYXRlQnlTb3VyY2VTbihjb21iaW5lZENoZWNrTGlzdCk7DQogICAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSB0aGlzLmRlZHVwbGljYXRlQnlTb3VyY2VTbihjb21iaW5lZFNhdmVkRGF0YSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDpu5jorqTmg4XlhrXvvJrnm7TmjqXmm7/mjaLvvIjlhbzlrrnmgKflpITnkIbvvIkNCiAgICAgICAgdGhpcy5jaGVja0xpc3QgPSBbLi4uc2VsZWN0ZWREYXRhXTsNCiAgICAgICAgdGhpcy5zYXZlZENoZWNrYm94RGF0YSA9IFsuLi5zZWxlY3RlZERhdGFdOw0KICAgICAgfQ0KDQogICAgICAvLyDph43nva7pobXnoIHlubbmn6Xor6LlhoXlrrkNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLnNjcm9sbFRvVG9wSW1tZWRpYXRlbHkoKTsNCiAgICAgIGlmICghdGhpcy5pc1JpZ2h0RmlsdGVyKSB7DQogICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgic291cmNlSXRlbUNoYW5nZWQiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qC55o2uc291cmNlU27ljrvph43nmoTovoXliqnmlrnms5UNCiAgICBkZWR1cGxpY2F0ZUJ5U291cmNlU24oZGF0YUFycmF5KSB7DQogICAgICBjb25zdCBzZWVuID0gbmV3IFNldCgpOw0KICAgICAgcmV0dXJuIGRhdGFBcnJheS5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKHNlZW4uaGFzKGl0ZW0uc291cmNlU24pKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICAgIHNlZW4uYWRkKGl0ZW0uc291cmNlU24pOw0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbph43nva4NCiAgICBoYW5kbGVSZXNldCgpIHsNCiAgICAgIC8vIOWFiOa4heepuui/h+a7pOaWh+acrO+8jOmBv+WFjeinpuWPkSBoYW5kbGVGaWx0ZXJTZWFyY2gNCiAgICAgIHRoaXMuZmlsdGVyVGV4dCA9ICIiOw0KICAgICAgdGhpcy5zZWxlY3RlZENsYXNzaWZ5ID0gbnVsbDsNCg0KICAgICAgLy8g54S25ZCO6K6+572u6YeN572u5qCH6K6wDQogICAgICB0aGlzLmlzTGVmdFJlc2V0ID0gdHJ1ZTsNCg0KICAgICAgLy8g5riF56m66YCJ5Lit54q25oCBDQogICAgICB0aGlzLmNoZWNrTGlzdCA9IFtdOw0KDQogICAgICAvLyDmuIXnqbrkv53lrZjnmoTli77pgInmlbDmja7vvIjmsLjkuYXkv53lrZjvvIkNCiAgICAgIHRoaXMuc2F2ZWRDaGVja2JveERhdGEgPSBbXTsNCg0KICAgICAgLy8g6YeN572u6aG156CB5bm25p+l6K+i5YiX6KGo5pWw5o2uDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KDQogICAgICAvLyDph43mlrDmn6Xor6LmoJHlkozliJfooagNCiAgICAgIHRoaXMucXVlcnlUcmVlQW5kTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDph43nva7moJHpgInmi6nvvIjkv53nlZnljp/mlrnms5XlkI3ku6XlhbzlrrnvvIkNCiAgICB0cmVlQ2xlYXIoKSB7DQogICAgICB0aGlzLmhhbmRsZVJlc2V0KCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuagkeWIhumhtQ0KICAgIGhhbmRsZVRyZWVDdXJyZW50Q2hhbmdlKHBhZ2UpIHsNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gcGFnZTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFzQ2FjaGUgPSAiMSI7DQogICAgICB0aGlzLnF1ZXJ5VHJlZURhdGFXaXRoUmVzdG9yZUZyb21TYXZlZCgpOw0KICAgIH0sDQoNCiAgICBoYW5kbGVUcmVlUGFnZVNpemVDaGFuZ2Uoc2l6ZSkgew0KICAgICAgdGhpcy50cmVlUGFnZVNpemUgPSBzaXplOw0KICAgICAgdGhpcy50cmVlQ3VycmVudFBhZ2UgPSAxOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5oYXNDYWNoZSA9ICIxIjsNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIC8vIOajgOe0ouivjeW6k+WFqOmAieWkhOeQhg0KICAgIGhhbmRsZUNoZWNrQWxsVGFnc1N1YnNldCh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCA9IHZhbA0KICAgICAgICA/IHRoaXMudGFnc0xpc3QubWFwKChpdGVtKSA9PiBpdGVtLm5hbWUpDQogICAgICAgIDogW107DQogICAgICB0aGlzLmlzSW5kZXRlcm1pbmF0ZSA9IGZhbHNlOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCg0KICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkKSByZXR1cm47DQoNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbov4fmu6TmkJzntKLvvIjmnaXoh6ogVHJlZVRhYmxlIOe7hOS7tu+8iQ0KICAgIGhhbmRsZUZpbHRlclNlYXJjaChrZXl3b3JkKSB7DQogICAgICBpZiAodGhpcy5pc0xlZnRSZXNldCkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOabtOaWsOi/h+a7pOaWh+acrA0KICAgICAgdGhpcy5maWx0ZXJUZXh0ID0ga2V5d29yZCB8fCAiIjsNCg0KICAgICAgLy8g6YeN572u5Yiw56ys5LiA6aG1DQogICAgICB0aGlzLnRyZWVDdXJyZW50UGFnZSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhc0NhY2hlID0gIjEiOw0KDQogICAgICAvLyDosIPnlKjmoJHmlbDmja7mn6Xor6LmjqXlj6PlubbmgaLlpI3pgInkuK3nirbmgIHvvIjkvb/nlKjmsLjkuYXkv53lrZjnmoTli77pgInmlbDmja7vvIkNCiAgICAgIHRoaXMucXVlcnlUcmVlRGF0YVdpdGhSZXN0b3JlRnJvbVNhdmVkKCk7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaVsOaNrua6kOWIhuexu+WPmOWMlu+8iOadpeiHqiBUcmVlVGFibGUg57uE5Lu277yJDQogICAgaGFuZGxlQ2xhc3NpZnlDaGFuZ2UoY2xhc3NpZnlWYWx1ZSkgew0KICAgICAgLy8g5pu05paw6YCJ5Lit55qE5YiG57G7DQogICAgICB0aGlzLnNlbGVjdGVkQ2xhc3NpZnkgPSBjbGFzc2lmeVZhbHVlOw0KDQogICAgICAvLyDph43nva7liLDnrKzkuIDpobUNCiAgICAgIHRoaXMudHJlZUN1cnJlbnRQYWdlID0gMTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaGFzQ2FjaGUgPSAiMSI7DQoNCiAgICAgIC8vIOWPquiwg+eUqOagkeaVsOaNruafpeivouaOpeWPo+W5tuaBouWkjemAieS4reeKtuaAge+8jOS9v+eUqOawuOS5heS/neWtmOeahOWLvumAieaVsOaNrg0KICAgICAgdGhpcy5xdWVyeVRyZWVEYXRhV2l0aFJlc3RvcmVGcm9tU2F2ZWQoKTsNCiAgICB9LA0KDQogICAgLy8g5qOA57Si6K+N5bqT5aSa6YCJ5aSE55CGDQogICAgaGFuZGxlQ2hlY2tlZENoYW5nZSh2YWx1ZSkgew0KICAgICAgbGV0IGNoZWNrZWRDb3VudCA9IHZhbHVlLmxlbmd0aDsNCiAgICAgIHRoaXMuY2hlY2tBbGwgPSBjaGVja2VkQ291bnQgPT09IHRoaXMudGFnc0xpc3QubGVuZ3RoOw0KICAgICAgdGhpcy5pc0luZGV0ZXJtaW5hdGUgPQ0KICAgICAgICBjaGVja2VkQ291bnQgPiAwICYmIGNoZWNrZWRDb3VudCA8IHRoaXMudGFnc0xpc3QubGVuZ3RoOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCg0KICAgICAgaWYgKCF0aGlzLmluaXRpYWxpemF0aW9uQ29tcGxldGVkKSByZXR1cm47DQoNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpOw0KICAgICAgdGhpcy5oYW5kbGVSaWdodEZpbHRlckNoYW5nZSgpOw0KICAgIH0sDQoNCiAgICAvLyDmkJzntKLlpITnkIYNCiAgICBoYW5kbGVTZWFyY2goZmxhZykgew0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICBpZiAoIWZsYWcpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgIH0sDQoNCiAgICAvLyDlhbPplK7or43ljoblj7LpgInmi6kNCiAgICBrZXl3b3Jkc0NoYW5nZShpdGVtKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmtleXdvcmRzID0gaXRlbS5rZXl3b3JkOw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IGZhbHNlOw0KICAgICAgdGhpcy5zY3JvbGxUb1RvcEltbWVkaWF0ZWx5KCk7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgLy8gdGhpcy5xdWVyeVRyZWVBbmRMaXN0KCk7DQogICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICB9LA0KDQogICAgLy8g5Y+z5L6n6KGo5qC85aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlVGFibGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgICAgaWYgKHNlbGVjdGlvbi5sZW5ndGggPT0gdGhpcy5BcnRpY2xlTGlzdC5sZW5ndGgpIHsNCiAgICAgICAgdGhpcy5jaGVja2VkID0gdHJ1ZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2hlY2tlZCA9IGZhbHNlOw0KICAgICAgfQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLy8g5YWo6YCJDQogICAgaGFuZGxlQ2hlY2tBbGxDaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsKSB7DQogICAgICAgIHRoaXMuJHJlZnNbInRhYmxlIl0udG9nZ2xlQWxsU2VsZWN0aW9uKCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRyZWZzWyJ0YWJsZSJdLmNsZWFyU2VsZWN0aW9uKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmiZPlvIDmt7vliqDliLDmiqXlkYoNCiAgICBvcGVuUmVwb3J0KCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35Yu+6YCJ6KaB5re75Yqg55qE5pWw5o2uIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOehruWumua3u+WKoOWIsOaKpeWRig0KICAgIGFzeW5jIHJlcG9ydFN1Ym1pdCgpIHsNCiAgICAgIGlmICghdGhpcy5yZXBvcnRJZCkNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nopoHmt7vliqDliLDnmoTmiqXlkYoiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICBsZXQga2V5V29yZExpc3QgPSB0aGlzLmlkcy5tYXAoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIHsgcmVwb3J0SWQ6IHRoaXMucmVwb3J0SWQsIGxpc3RJZDogaXRlbSB9Ow0KICAgICAgfSk7DQogICAgICBsZXQgcmVzID0gYXdhaXQgYXBpLkFkZFJlcG9ydChrZXlXb3JkTGlzdCk7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5bey5re75Yqg5Yiw5oql5ZGKIiwgdHlwZTogInN1Y2Nlc3MiIH0pOw0KICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLmt7vliqDliLDmiqXlkYrlpLHotKUs6K+36IGU57O7566h55CG5ZGYIiwNCiAgICAgICAgICB0eXBlOiAiZXJyb3IiLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHRoaXMuJHJlZnNbInRhYmxlIl0uY2xlYXJTZWxlY3Rpb24oKTsNCiAgICAgIHRoaXMuY2hlY2tlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5jbG9zZVJlcG9ydCgpOw0KICAgIH0sDQogICAgLy8g5YWz6Zet5re75Yqg5Yiw5oql5ZGKDQogICAgY2xvc2VSZXBvcnQoKSB7DQogICAgICB0aGlzLnJlcG9ydElkID0gIiI7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOaJuemHj+WIoOmZpA0KICAgIGJhdGNoRGVsZXRlKCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35Yu+6YCJ6KaB5Yig6Zmk55qE5pWw5o2uIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk5bey5Yu+6YCJ55qE5pWw5o2u6aG5PyIpDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBBUEkuYmF0Y2hSZW1vdmUodGhpcy5pZHMuam9pbigiLCIpKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLliKDpmaTmiJDlip8iLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8vIOa3u+WKoOWIsOWPsOi0pg0KICAgIG9wZW5UYWl6aGFuZygpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT0gMCkgew0KICAgICAgICByZXR1cm4gdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgbWVzc2FnZTogIuivt+WLvumAieimgea3u+WKoOeahOaVsOaNriIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOa3u+WKoOW3suWLvumAieeahOaVsOaNrumhueWIsOWPsOi0pue7n+iuoT8iKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgYWRkV29yayh0aGlzLmlkcykudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgdHlwZTogInN1Y2Nlc3MiLCBtZXNzYWdlOiAi5re75Yqg5oiQ5YqfISIgfSk7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5QXJ0aWNsZUxpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8vIOWPkeW4g+WIsOavj+aXpeacgOaWsOeDreeCuQ0KICAgIHB1Ymxpc2hIb3QoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7fli77pgInopoHlj5HluIPliLDmr4/ml6XmnIDmlrDng63ngrnnmoTmlbDmja4iLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKCLmmK/lkKbnoa7orqTlj5HluIPlt7Lli77pgInnmoTmlbDmja7pobnliLDmr4/ml6XmnIDmlrDng63ngrk/IikNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIEFQSS5wdWJsaXNoRXZlcnlkYXlIb3QodGhpcy5pZHMuam9pbigiLCIpKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoeyB0eXBlOiAic3VjY2VzcyIsIG1lc3NhZ2U6ICLlj5HluIPmiJDlip8hIiB9KTsNCiAgICAgICAgICAgIHRoaXMucXVlcnlBcnRpY2xlTGlzdCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLy8g5Y6G5Y+y6K6w5b2V55u45YWz5pa55rOVDQogICAgYXN5bmMgcmVtb3ZlSGlzdG9yeShpdGVtLCB0eXBlKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICBhd2FpdCBkZWxBcnRpY2xlSGlzdG9yeShbaXRlbS5pZF0pOw0KICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICB0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0uZm9jdXMoKTsNCiAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5nZXRBcnRpY2xlSGlzdG9yeSgpOw0KICAgICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5MSgpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBzaG93SGlzdG9yeUxpc3QoKSB7DQogICAgICB0aGlzLnNob3dIaXN0b3J5ID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgaGlkZUhpc3RvcnlMaXN0KCkgew0KICAgICAgdGhpcy5oaXN0b3J5VGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0aGlzLnNob3dIaXN0b3J5ID0gZmFsc2U7DQogICAgICB9LCA1MDApOw0KICAgIH0sDQoNCiAgICBnZXRBcnRpY2xlSGlzdG9yeSgpIHsNCiAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdEFydGljbGVIaXN0b3J5KHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDUsIHR5cGU6IDEgfSkudGhlbigNCiAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy5oaXN0b3J5TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQoNCiAgICBhc3luYyBjbGVhckhpc3RvcnkoKSB7DQogICAgICBjbGVhclRpbWVvdXQodGhpcy5oaXN0b3J5VGltZW91dCk7DQogICAgICB0aGlzLiRyZWZzWyJrZXl3b3JkUmVmIl0uZm9jdXMoKTsNCiAgICAgIGF3YWl0IGNsZWFuQXJ0aWNsZUhpc3RvcnkoMSk7DQogICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5KCk7DQogICAgfSwNCg0KICAgIG1vcmVIaXN0b3J5KCkgew0KICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuaGlzdG9yeVRpbWVvdXQpOw0KICAgICAgdGhpcy5zaG93SGlzdG9yeSA9IGZhbHNlOw0KICAgICAgdGhpcy5oaXN0b3J5TG9hZGluZyA9IHRydWU7DQogICAgICB0aGlzLmdldEFydGljbGVIaXN0b3J5MSgpOw0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlMSA9IHRydWU7DQogICAgfSwNCg0KICAgIGdldEFydGljbGVIaXN0b3J5MSgpIHsNCiAgICAgIHRoaXMuaGlzdG9yeUxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdEFydGljbGVIaXN0b3J5KHsgLi4udGhpcy5xdWVyeVBhcmFtczEsIHR5cGU6IDEgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5oaXN0b3J5TGlzdDEgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsMSA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmhpc3RvcnlMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5paH56ug6K+m5oOFDQogICAgb3Blbk5ld1ZpZXcoaXRlbSkgew0KICAgICAgd2luZG93Lm9wZW4oDQogICAgICAgIGAvZXhwcmVzc0RldGFpbHM/aWQ9JHtpdGVtLmlkfSZkb2NJZD0ke2l0ZW0uZG9jSWR9JnNvdXJjZVR5cGU9JHtpdGVtLnNvdXJjZVR5cGV9YCwNCiAgICAgICAgIl9ibGFuayINCiAgICAgICk7DQogICAgfSwNCg0KICAgIC8vIOajgOafpeaWh+acrOaYr+WQpuacieWunumZheWGheWuuQ0KICAgIGhhc0FjdHVhbENvbnRlbnQodGV4dCkgew0KICAgICAgaWYgKCF0ZXh0KSByZXR1cm4gZmFsc2U7DQogICAgICBjb25zdCBjb250ZW50V2l0aG91dFRhZ3MgPSB0ZXh0LnJlcGxhY2UoLzxbXj5dKj4vZywgIiIpOw0KICAgICAgcmV0dXJuIC9bXHU0ZTAwLVx1OWZhNWEtekEtWjAtOV0vLnRlc3QoY29udGVudFdpdGhvdXRUYWdzKTsNCiAgICB9LA0KDQogICAgLy8g5rua5Yqo5Yiw6aG26YOoDQogICAgc2Nyb2xsVG9Ub3BJbW1lZGlhdGVseSgpIHsNCiAgICAgIGlmICh0aGlzLiRyZWZzLnJpZ2h0TWFpbikgew0KICAgICAgICB0aGlzLiRyZWZzLnJpZ2h0TWFpbi5zY3JvbGxUb3AgPSAwOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy4kcmVmcy50YWJsZSkgew0KICAgICAgICBjb25zdCBib2R5V3JhcHBlciA9IHRoaXMuJHJlZnMudGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3IoDQogICAgICAgICAgIi5lbC10YWJsZV9fYm9keS13cmFwcGVyIg0KICAgICAgICApOw0KICAgICAgICBpZiAoYm9keVdyYXBwZXIpIHsNCiAgICAgICAgICBib2R5V3JhcHBlci5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWFs+mUruWtl+mrmOS6rg0KICAgIGNoYW5nZUNvbG9yKHN0cikgew0KICAgICAgY29uc3QgcmVnZXggPSAvPGltZ1xiW14+XSo+L2dpOw0KICAgICAgbGV0IFN0ciA9IHN0ciAmJiBzdHIucmVwbGFjZShyZWdleCwgIiIpOw0KICAgICAgaWYgKA0KICAgICAgICBTdHIgJiYNCiAgICAgICAgKCh0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3MgJiYNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3NTdWJzZXQgJiYNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnRhZ3NTdWJzZXQubGVuZ3RoKSB8fA0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMua2V5d29yZHMpDQogICAgICApIHsNCiAgICAgICAgbGV0IGtleXdvcmRzID0gWw0KICAgICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMudGFnc1N1YnNldCwNCiAgICAgICAgICAuLi4odGhpcy5xdWVyeVBhcmFtcy5rZXl3b3Jkcw0KICAgICAgICAgICAgPyB0aGlzLnF1ZXJ5UGFyYW1zLmtleXdvcmRzLnNwbGl0KCIsIikNCiAgICAgICAgICAgIDogW10pLA0KICAgICAgICBdOw0KICAgICAgICBrZXl3b3Jkcy5mb3JFYWNoKChrZXlpdGVtKSA9PiB7DQogICAgICAgICAgaWYgKGtleWl0ZW0gJiYga2V5aXRlbS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBsZXQgcmVwbGFjZVJlZyA9IG5ldyBSZWdFeHAoa2V5aXRlbSwgImciKTsNCiAgICAgICAgICAgIGxldCByZXBsYWNlU3RyaW5nID0NCiAgICAgICAgICAgICAgJzxzcGFuIGNsYXNzPSJoaWdobGlnaHQiIHN0eWxlPSJjb2xvcjogcmVkOyI+JyArDQogICAgICAgICAgICAgIGtleWl0ZW0gKw0KICAgICAgICAgICAgICAiPC9zcGFuPiI7DQogICAgICAgICAgICBTdHIgPSBTdHIucmVwbGFjZShyZXBsYWNlUmVnLCByZXBsYWNlU3RyaW5nKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIFN0cjsNCiAgICB9LA0KDQogICAgLy8g5b+r54Wn55Sf5oiQDQogICAgcmVzdWx0RXZlbnQoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5paH56ugIik7DQogICAgICB9DQogICAgICBsZXQgaWRzID0gdGhpcy5pZHM7DQogICAgICBsZXQgemh1YW5ndGFpID0gIueUn+aIkCI7DQogICAgICBsZXQgdXJsID0gIiI7DQogICAgICBpZiAoaWRzLmxlbmd0aCA9PSAxKSB7DQogICAgICAgIGxldCByb3cgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoaXRlbSkgPT4gaXRlbS5pZCA9PSBpZHNbMF0pOw0KICAgICAgICBpZiAocm93ICYmIHJvdy5zbmFwc2hvdFVybCkgemh1YW5ndGFpID0gIuafpeeciyI7DQogICAgICAgIHVybCA9IHJvdy5zbmFwc2hvdFVybDsNCiAgICAgIH0NCiAgICAgIGlmICh6aHVhbmd0YWkgPT0gIueUn+aIkCIpIHsNCiAgICAgICAgdGhpcy4kbXNnYm94KHsNCiAgICAgICAgICB0aXRsZTogIuaPkOekuiIsDQogICAgICAgICAgbWVzc2FnZTogIuW/q+eFp+ato+WcqOeUn+aIkOS4re+8jOivt+eojeWQjuafpeeciyIsDQogICAgICAgICAgc2hvd0NhbmNlbEJ1dHRvbjogZmFsc2UsDQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLlhbPpl60iLA0KICAgICAgICAgIGJlZm9yZUNsb3NlOiAoXywgX18sIGRvbmUpID0+IHsNCiAgICAgICAgICAgIGRvbmUoKTsNCiAgICAgICAgICB9LA0KICAgICAgICB9KTsNCiAgICAgICAgQVBJLmRvd25Mb2FkRXhwb3J0S2UoaWRzKQ0KICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgIT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7flpLHotKXvvIzor7fogZTns7vnrqHnkIblkZjvvIznoa7orqTph4fpm4blmajmmK/lkKbmraPluLgiLA0KICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHVybCA9IHVybC5yZXBsYWNlKG5ldyBSZWdFeHAoIi9ob21lL2xvY2FsL2RweC9zZXJ2ZXItYXBpLyIsICJnIiksICIvIik7DQogICAgICAgIHVybCA9IHVybC5yZXBsYWNlKG5ldyBSZWdFeHAoIi9ob21lL2xvY2FsL2RweC8iLCAiZyIpLCAiLyIpOw0KICAgICAgICB3aW5kb3cub3Blbih3aW5kb3cubG9jYXRpb24ub3JpZ2luICsgdXJsLCAiX2JsYW5rIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIG9wZW5VcmwodXJsKSB7DQogICAgICB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICB9LA0KDQogICAgLy8gYWnnm7jlhbMNCiAgICAvLyBkaWZ5DQogICAgYXN5bmMgZGlmeUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSB0cnVlOw0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzID0gW107DQogICAgICB0aGlzLmlzVGhpbmtpbmcgPSB0cnVlOw0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDojrflj5bpgInkuK3nmoTmlofnq6ANCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRBcnRpY2xlcyA9IHRoaXMuQXJ0aWNsZUxpc3QuZmlsdGVyKChhcnRpY2xlKSA9Pg0KICAgICAgICAgIHRoaXMuaWRzLmluY2x1ZGVzKGFydGljbGUuaWQpDQogICAgICAgICk7DQogICAgICAgIGNvbnN0IHRpdGxlcyA9IHNlbGVjdGVkQXJ0aWNsZXMNCiAgICAgICAgICAubWFwKChhcnRpY2xlKSA9PiBg44CKJHthcnRpY2xlLmNuVGl0bGUgfHwgYXJ0aWNsZS50aXRsZX3jgItgKQ0KICAgICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICAgIC8vIOiOt+WPluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKHRoaXMuaWRzLmpvaW4oIiwiKSk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhPy5sZW5ndGgpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIuiOt+WPluaWh+eroOWGheWuueWksei0pSIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qC85byP5YyW5paH56ug5YaF5a65DQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmlkcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBrw0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCg0KICAgICAgICAvLyDmnoTlu7rmj5DnpLror40NCiAgICAgICAgY29uc3QgcHJvbXB0ID0NCiAgICAgICAgICB0aGlzLmFydGljbGVBaVByb21wdA0KICAgICAgICAgICAgLnJlcGxhY2UoImFydGljbGVMZW5ndGgiLCB0aGlzLmlkcy5sZW5ndGgpDQogICAgICAgICAgICAucmVwbGFjZSgvXCZndDsvZywgIj4iKSArDQogICAgICAgICAgYCoq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICAvLyDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRpZnlBaVFhKA0KICAgICAgICAgIGFydGljbGVzQ29udGVudCwNCiAgICAgICAgICAic3RyZWFtaW5nIiwNCiAgICAgICAgICAiZGlmeS5hcnRpY2xlLmFwaWtleSINCiAgICAgICAgKTsNCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhua1geW8j+WTjeW6lA0KICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LmdldFJlYWRlcigpOw0KICAgICAgICB0aGlzLmN1cnJlbnRSZWFkZXIgPSByZWFkZXI7DQogICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTsNCiAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICBsZXQgcGVuZGluZ0J1ZmZlciA9ICIiOyAvLyDnlKjkuo7lrZjlgqjlvoXlpITnkIbnmoTkuI3lrozmlbTmlbDmja4NCiAgICAgICAgbGV0IGlzSW5UaGlua1RhZyA9IGZhbHNlOyAvLyDmlrDlop7vvJrmoIforrDmmK/lkKblnKh0aGlua+agh+etvuWGhQ0KDQogICAgICAgIC8vIOWwhlVuaWNvZGXovazkuYnlrZfnrKYoXHVYWFhYKei9rOaNouS4uuWunumZheWtl+espg0KICAgICAgICBjb25zdCBkZWNvZGVVbmljb2RlID0gKHN0cikgPT4gew0KICAgICAgICAgIHJldHVybiBzdHIucmVwbGFjZSgvXFx1W1xkQS1GYS1mXXs0fS9nLCAobWF0Y2gpID0+IHsNCiAgICAgICAgICAgIHJldHVybiBTdHJpbmcuZnJvbUNoYXJDb2RlKHBhcnNlSW50KG1hdGNoLnJlcGxhY2UoL1xcdS9nLCAiIiksIDE2KSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5pu05paw5YaF5a6555qE5Ye95pWwDQogICAgICAgIGNvbnN0IHVwZGF0ZUNvbnRlbnQgPSAobmV3Q29udGVudCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBjb25zdCByZW5kZXJlZENvbnRlbnQgPSBtYXJrZWQobmV3Q29udGVudCwgdGhpcy5tYXJrZG93bk9wdGlvbnMpOw0KICAgICAgICAgICAgYWlNZXNzYWdlLmNvbnRlbnQgPSByZW5kZXJlZENvbnRlbnQ7DQoNCiAgICAgICAgICAgIC8vIOehruS/nea2iOaBr+WuueWZqOa7muWKqOWIsOW6lemDqA0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSB0aGlzLiRyZWZzLmNoYXRNZXNzYWdlczsNCiAgICAgICAgICAgICAgaWYgKGNoYXRNZXNzYWdlcykgew0KICAgICAgICAgICAgICAgIGNoYXRNZXNzYWdlcy5zY3JvbGxUb3AgPSBjaGF0TWVzc2FnZXMuc2Nyb2xsSGVpZ2h0Ow0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5riy5p+T5YaF5a655pe25Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgICB9DQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5aSE55CG5rWB5byP5ZON5bqUDQogICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgaWYgKHRoaXMuaXNBYm9ydGVkKSB7DQogICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpOw0KDQogICAgICAgICAgaWYgKGRvbmUpIHsNCiAgICAgICAgICAgIC8vIOWkhOeQhuacgOWQjuWPr+iDveWJqeS9meeahOaVsOaNrg0KICAgICAgICAgICAgaWYgKHBlbmRpbmdCdWZmZXIpIHsNCiAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICBjb25zdCBsYXN0RGF0YSA9IEpTT04ucGFyc2UocGVuZGluZ0J1ZmZlcik7DQogICAgICAgICAgICAgICAgaWYgKGxhc3REYXRhLmFuc3dlcikgew0KICAgICAgICAgICAgICAgICAgLy8g6Kej56CBVW5pY29kZei9rOS5ieWtl+espg0KICAgICAgICAgICAgICAgICAgY29uc3QgZGVjb2RlZEFuc3dlciA9IGRlY29kZVVuaWNvZGUobGFzdERhdGEuYW5zd2VyKTsNCiAgICAgICAgICAgICAgICAgIGJ1ZmZlciArPSBkZWNvZGVkQW5zd2VyOw0KICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybigi5aSE55CG5pyA5ZCO55qE5pWw5o2u5pe25Ye66ZSZOiIsIGUpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlKTsNCiAgICAgICAgICBwZW5kaW5nQnVmZmVyICs9IGNodW5rOw0KDQogICAgICAgICAgLy8g5aSE55CG5a6M5pW055qE5pWw5o2u6KGMDQogICAgICAgICAgd2hpbGUgKHBlbmRpbmdCdWZmZXIuaW5jbHVkZXMoIlxuIikpIHsNCiAgICAgICAgICAgIGNvbnN0IG5ld2xpbmVJbmRleCA9IHBlbmRpbmdCdWZmZXIuaW5kZXhPZigiXG4iKTsNCiAgICAgICAgICAgIGNvbnN0IGxpbmUgPSBwZW5kaW5nQnVmZmVyLnNsaWNlKDAsIG5ld2xpbmVJbmRleCkudHJpbSgpOw0KICAgICAgICAgICAgcGVuZGluZ0J1ZmZlciA9IHBlbmRpbmdCdWZmZXIuc2xpY2UobmV3bGluZUluZGV4ICsgMSk7DQoNCiAgICAgICAgICAgIGlmICghbGluZSB8fCBsaW5lID09PSAiZGF0YToiIHx8ICFsaW5lLnN0YXJ0c1dpdGgoImRhdGE6IikpIHsNCiAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBsaW5lLnNsaWNlKDUpLnRyaW0oKTsNCiAgICAgICAgICAgICAgaWYgKGRhdGEgPT09ICJbRE9ORV0iKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IEpTT04ucGFyc2UoZGF0YSk7DQogICAgICAgICAgICAgIGlmICghanNvbkRhdGEuYW5zd2VyKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDot7Pov4fnibnmrorlrZfnrKYNCiAgICAgICAgICAgICAgaWYgKGpzb25EYXRhLmFuc3dlciA9PT0gImBgYCIgfHwganNvbkRhdGEuYW5zd2VyID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDop6PnoIFVbmljb2Rl6L2s5LmJ5a2X56ymDQogICAgICAgICAgICAgIGxldCBhbnN3ZXIgPSBkZWNvZGVVbmljb2RlKGpzb25EYXRhLmFuc3dlcik7DQoNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCrPHRoaW5rPuW8gOWni+agh+etvg0KICAgICAgICAgICAgICBpZiAoYW5zd2VyLmluY2x1ZGVzKCI8dGhpbms+IikpIHsNCiAgICAgICAgICAgICAgICBpc0luVGhpbmtUYWcgPSB0cnVlOw0KICAgICAgICAgICAgICAgIGNvbnRpbnVlOyAvLyDot7Pov4fljIXlkKs8dGhpbms+55qE6YOo5YiGDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbljIXlkKs8L3RoaW5rPue7k+adn+agh+etvg0KICAgICAgICAgICAgICBpZiAoYW5zd2VyLmluY2x1ZGVzKCI8L3RoaW5rPiIpKSB7DQogICAgICAgICAgICAgICAgaXNJblRoaW5rVGFnID0gZmFsc2U7DQogICAgICAgICAgICAgICAgY29udGludWU7IC8vIOi3s+i/h+WMheWQqzwvdGhpbms+55qE6YOo5YiGDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDlj6rmnInkuI3lnKh0aGlua+agh+etvuWGheeahOWGheWuueaJjeS8muiiq+a3u+WKoOWIsGJ1ZmZlcuS4rQ0KICAgICAgICAgICAgICBpZiAoIWlzSW5UaGlua1RhZyAmJiBhbnN3ZXIpIHsNCiAgICAgICAgICAgICAgICBidWZmZXIgKz0gYW5zd2VyOw0KICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikgew0KICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuino+aekOaVsOaNruihjOaXtuWHuumUmToiLCB7DQogICAgICAgICAgICAgICAgbGluZSwNCiAgICAgICAgICAgICAgICBlcnJvcjogcGFyc2VFcnJvci5tZXNzYWdlLA0KICAgICAgICAgICAgICAgIHBlbmRpbmdCdWZmZXIsDQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIkFJ6Kej6K+75Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICJBSeino+ivu+Wksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICBpZiAodGhpcy5jaGF0TWVzc2FnZXNbMV0pIHsNCiAgICAgICAgICB0aGlzLmNoYXRNZXNzYWdlc1sxXS5jb250ZW50ID0gIuaKseatie+8jOacjeWKoeWZqOe5geW/me+8jOivt+eojeWQjuWGjeivlSI7DQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7DQogICAgICAgIGlmICh0aGlzLmFpRGlhbG9nVmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIE9sbGFtYQ0KICAgIGFzeW5jIG9sbGFtYUFpQ2hhdCgpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgfSk7DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOacieato+WcqOi/m+ihjOeahOivt+axgu+8jOS4reaWreWugw0KICAgICAgaWYgKHRoaXMuaXNSZXF1ZXN0aW5nKSB7DQogICAgICAgIHRoaXMuaXNBYm9ydGVkID0gdHJ1ZTsNCiAgICAgICAgaWYgKHRoaXMuY3VycmVudFJlYWRlcikgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBhd2FpdCB0aGlzLmN1cnJlbnRSZWFkZXIuY2FuY2VsKCk7DQogICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coIuS4reaWreS5i+WJjeeahOivt+axguWksei0pSIsIGUpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvLyDnrYnlvoXkuYvliY3nmoTor7fmsYLnirbmgIHmuIXnkIblrozmiJANCiAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gdHJ1ZTsNCiAgICAgIHRoaXMuaXNBYm9ydGVkID0gZmFsc2U7DQogICAgICB0aGlzLmFpRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6I635Y+W6YCJ5Lit55qE5paH56ugDQogICAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZXMgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoYXJ0aWNsZSkgPT4NCiAgICAgICAgICB0aGlzLmlkcy5pbmNsdWRlcyhhcnRpY2xlLmlkKQ0KICAgICAgICApOw0KICAgICAgICBjb25zdCB0aXRsZXMgPSBzZWxlY3RlZEFydGljbGVzDQogICAgICAgICAgLm1hcCgoYXJ0aWNsZSkgPT4gYOOAiiR7YXJ0aWNsZS5jblRpdGxlIHx8IGFydGljbGUudGl0bGV944CLYCkNCiAgICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgICAvLyDojrflj5bmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZXNSZXNwb25zZSA9IGF3YWl0IGdldExpc3RCeUlkcyh0aGlzLmlkcy5qb2luKCIsIikpOw0KICAgICAgICBpZiAoIWFydGljbGVzUmVzcG9uc2UuZGF0YT8ubGVuZ3RoKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCLojrflj5bmlofnq6DlhoXlrrnlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagvOW8j+WMluaWh+eroOWGheWuuQ0KICAgICAgICBjb25zdCBhcnRpY2xlc0NvbnRlbnQgPSBhcnRpY2xlc1Jlc3BvbnNlLmRhdGENCiAgICAgICAgICAubWFwKChhcnRpY2xlLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdGl0bGUgPQ0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8uY25UaXRsZSB8fA0KICAgICAgICAgICAgICBzZWxlY3RlZEFydGljbGVzW2luZGV4XT8udGl0bGUgfHwNCiAgICAgICAgICAgICAgIiI7DQogICAgICAgICAgICBjb25zdCBjb250ZW50ID0gYXJ0aWNsZS5jb250ZW50IHx8ICIiOw0KICAgICAgICAgICAgcmV0dXJuIGDjgJDnrKwgJHtpbmRleCArIDF9IOevh+aWh+eroOOAkeOAiiR7dGl0bGV944CLXG5cbiR7Y29udGVudH1gOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmpvaW4oIlxuXG4tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG5cbiIpOw0KDQogICAgICAgIC8vIOa3u+WKoOeUqOaIt+a2iOaBrw0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKHsNCiAgICAgICAgICByb2xlOiAidXNlciIsDQogICAgICAgICAgY29udGVudDogYOW4ruaIkea3seW6puino+ivu+S7peS4iyR7dGhpcy5pZHMubGVuZ3Rofeevh+aWh+eroO+8mlxuJHt0aXRsZXN9YCwNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5Yib5bu6QUnmtojmga8NCiAgICAgICAgY29uc3QgYWlNZXNzYWdlID0gew0KICAgICAgICAgIHJvbGU6ICJhc3Npc3RhbnQiLA0KICAgICAgICAgIGNvbnRlbnQ6ICIiLA0KICAgICAgICB9Ow0KICAgICAgICB0aGlzLmNoYXRNZXNzYWdlcy5wdXNoKGFpTWVzc2FnZSk7DQoNCiAgICAgICAgLy8g5p6E5bu65o+Q56S66K+NDQogICAgICAgIGNvbnN0IHByb21wdCA9DQogICAgICAgICAgdGhpcy5hcnRpY2xlQWlQcm9tcHQNCiAgICAgICAgICAgIC5yZXBsYWNlKCJhcnRpY2xlTGVuZ3RoIiwgdGhpcy5pZHMubGVuZ3RoKQ0KICAgICAgICAgICAgLnJlcGxhY2UoL1wmZ3Q7L2csICI+IikgKw0KICAgICAgICAgIGAqKuS7peS4i+aYr+W+heWkhOeQhueahOaWh+eroO+8mioqXG5cbiR7YXJ0aWNsZXNDb250ZW50fWA7DQoNCiAgICAgICAgLy8g6LCD55SoQUnmjqXlj6MNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBvbGxhbWFBaVFhKHByb21wdCwgdHJ1ZSk7DQogICAgICAgIGlmICghcmVzcG9uc2Uub2spIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ5o6l5Y+j6LCD55So5aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgY29uc3QgcmVhZGVyID0gcmVzcG9uc2UuYm9keS5nZXRSZWFkZXIoKTsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gcmVhZGVyOyAvLyDkv53lrZjlvZPliY3nmoQgcmVhZGVyDQogICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTsNCiAgICAgICAgbGV0IGJ1ZmZlciA9ICIiOw0KICAgICAgICBsZXQgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpOw0KICAgICAgICBsZXQgaXNUaGlua0NvbnRlbnQgPSBmYWxzZTsNCiAgICAgICAgbGV0IHRlbXBCdWZmZXIgPSAiIjsNCg0KICAgICAgICAvLyDmm7TmlrDlhoXlrrnnmoTlh73mlbANCiAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgY29uc3QgY3VycmVudFRpbWUgPSBEYXRlLm5vdygpOw0KICAgICAgICAgIC8vIOaOp+WItuabtOaWsOmikeeOh++8jOmBv+WFjei/h+S6jumikee5geeahERPTeabtOaWsA0KICAgICAgICAgIGlmIChjdXJyZW50VGltZSAtIGxhc3RVcGRhdGVUaW1lID49IDUwKSB7DQogICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IG5ld0NvbnRlbnQ7DQogICAgICAgICAgICBsYXN0VXBkYXRlVGltZSA9IGN1cnJlbnRUaW1lOw0KICAgICAgICAgICAgLy8g56Gu5L+d5raI5oGv5a655Zmo5rua5Yqo5Yiw5bqV6YOoDQogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGNoYXRNZXNzYWdlcyA9IHRoaXMuJHJlZnMuY2hhdE1lc3NhZ2VzOw0KICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgY2hhdE1lc3NhZ2VzLnNjcm9sbFRvcCA9IGNoYXRNZXNzYWdlcy5zY3JvbGxIZWlnaHQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDlpITnkIbmtYHlvI/lk43lupQNCiAgICAgICAgY29uc3QgcHJvY2Vzc1N0cmVhbSA9IGFzeW5jICgpID0+IHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgd2hpbGUgKHRydWUpIHsNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgICAgIGlmICh0aGlzLmlzQWJvcnRlZCkgew0KICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQWJvcnRFcnJvciIpOw0KICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgY29uc3QgeyBkb25lLCB2YWx1ZSB9ID0gYXdhaXQgcmVhZGVyLnJlYWQoKTsNCiAgICAgICAgICAgICAgaWYgKGRvbmUpIHsNCiAgICAgICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvbnRlbnQoYnVmZmVyKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlKTsNCiAgICAgICAgICAgICAgY29uc3QgbGluZXMgPSBjaHVuay5zcGxpdCgiXG4iKS5maWx0ZXIoKGxpbmUpID0+IGxpbmUudHJpbSgpKTsNCg0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHsNCiAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnBhcnNlKGxpbmUpOw0KICAgICAgICAgICAgICAgICAgaWYgKCFqc29uRGF0YS5yZXNwb25zZSkgY29udGludWU7DQoNCiAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0ganNvbkRhdGEucmVzcG9uc2U7DQoNCiAgICAgICAgICAgICAgICAgIC8vIOi3s+i/h+eJueauiuWtl+espg0KICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlID09PSAiYGBgIiB8fCByZXNwb25zZSA9PT0gIm1hcmtkb3duIikgew0KICAgICAgICAgICAgICAgICAgICBjb250aW51ZTsNCiAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgdGVtcEJ1ZmZlciArPSByZXNwb25zZTsNCg0KICAgICAgICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5YyF5ZCr5a6M5pW055qEdGhpbmvmoIfnrb7lr7kNCiAgICAgICAgICAgICAgICAgIHdoaWxlICh0cnVlKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IHRoaW5rU3RhcnRJbmRleCA9IHRlbXBCdWZmZXIuaW5kZXhPZigiPHRoaW5rPiIpOw0KICAgICAgICAgICAgICAgICAgICBjb25zdCB0aGlua0VuZEluZGV4ID0gdGVtcEJ1ZmZlci5pbmRleE9mKCI8L3RoaW5rPiIpOw0KDQogICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPT09IC0xICYmIHRoaW5rRW5kSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5rKh5pyJdGhpbmvmoIfnrb7vvIznm7TmjqXmt7vliqDliLBidWZmZXINCiAgICAgICAgICAgICAgICAgICAgICBpZiAoIWlzVGhpbmtDb250ZW50KSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlcjsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSAiIjsNCiAgICAgICAgICAgICAgICAgICAgICBicmVhazsNCiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlua1N0YXJ0SW5kZXggIT09IC0xICYmIHRoaW5rRW5kSW5kZXggPT09IC0xKSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5Y+q5pyJ5byA5aeL5qCH562+77yM562J5b6F57uT5p2f5qCH562+DQogICAgICAgICAgICAgICAgICAgICAgaXNUaGlua0NvbnRlbnQgPSB0cnVlOw0KICAgICAgICAgICAgICAgICAgICAgIGlmICh0aGlua1N0YXJ0SW5kZXggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcoMCwgdGhpbmtTdGFydEluZGV4KTsNCiAgICAgICAgICAgICAgICAgICAgICAgIC8vIOS9v+eUqG1hcmtlZOa4suafk21hcmtkb3du5YaF5a65DQogICAgICAgICAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KG1hcmtlZChidWZmZXIsIHRoaXMubWFya2Rvd25PcHRpb25zKSk7DQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua1N0YXJ0SW5kZXgpOw0KICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHRoaW5rU3RhcnRJbmRleCA9PT0gLTEgJiYgdGhpbmtFbmRJbmRleCAhPT0gLTEpIHsNCiAgICAgICAgICAgICAgICAgICAgICAvLyDlj6rmnInnu5PmnZ/moIfnrb7vvIznp7vpmaTkuYvliY3nmoTlhoXlrrkNCiAgICAgICAgICAgICAgICAgICAgICBpc1RoaW5rQ29udGVudCA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICAgIHRlbXBCdWZmZXIgPSB0ZW1wQnVmZmVyLnN1YnN0cmluZyh0aGlua0VuZEluZGV4ICsgOCk7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgICAgLy8g5pyJ5a6M5pW055qEdGhpbmvmoIfnrb7lr7kNCiAgICAgICAgICAgICAgICAgICAgICBpZiAodGhpbmtTdGFydEluZGV4ID4gMCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgYnVmZmVyICs9IHRlbXBCdWZmZXIuc3Vic3RyaW5nKDAsIHRoaW5rU3RhcnRJbmRleCk7DQogICAgICAgICAgICAgICAgICAgICAgICAvLyDkvb/nlKhtYXJrZWTmuLLmn5NtYXJrZG93buWGheWuuQ0KICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChtYXJrZWQoYnVmZmVyLCB0aGlzLm1hcmtkb3duT3B0aW9ucykpOw0KICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICB0ZW1wQnVmZmVyID0gdGVtcEJ1ZmZlci5zdWJzdHJpbmcodGhpbmtFbmRJbmRleCArIDgpOw0KICAgICAgICAgICAgICAgICAgICAgIGlzVGhpbmtDb250ZW50ID0gZmFsc2U7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9IGNhdGNoIChwYXJzZUVycm9yKSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuaXoOaViOeahEpTT07ooYzvvIzlt7Lot7Pov4ciLCB7DQogICAgICAgICAgICAgICAgICAgIGxpbmUsDQogICAgICAgICAgICAgICAgICAgIGVycm9yOiBwYXJzZUVycm9yLm1lc3NhZ2UsDQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChzdHJlYW1FcnJvcikgew0KICAgICAgICAgICAgaWYgKHN0cmVhbUVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFib3J0RXJyb3IiKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWkhOeQhua1geW8j+WTjeW6lOaXtuWHuumUmToiLCBzdHJlYW1FcnJvcik7DQogICAgICAgICAgICB0aHJvdyBzdHJlYW1FcnJvcjsNCiAgICAgICAgICB9DQogICAgICAgIH07DQoNCiAgICAgICAgYXdhaXQgcHJvY2Vzc1N0cmVhbSgpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgLy8g5Yik5pat5piv5ZCm5piv5Lit5pat5a+86Ie055qE6ZSZ6K+vDQogICAgICAgIGlmIChlcnJvci5tZXNzYWdlID09PSAiQWJvcnRFcnJvciIpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygi6K+35rGC5bey6KKr5Lit5patIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIkFJ6Kej6K+75Ye66ZSZOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICJBSeino+ivu+Wksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICBpZiAodGhpcy5jaGF0TWVzc2FnZXNbMV0pIHsNCiAgICAgICAgICB0aGlzLmNoYXRNZXNzYWdlc1sxXS5jb250ZW50ID0gIuaKseatie+8jOacjeWKoeWZqOe5geW/me+8jOivt+eojeWQjuWGjeivlSI7DQogICAgICAgIH0NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY3VycmVudFJlYWRlciA9IG51bGw7IC8vIOa4heeQhuW9k+WJjeeahCByZWFkZXINCiAgICAgICAgLy8g5Y+q5pyJ5Zyo5rKh5pyJ6KKr5Lit5pat55qE5oOF5Ya15LiL5omN6YeN572u54q25oCBDQogICAgICAgIGlmICh0aGlzLmFpRGlhbG9nVmlzaWJsZSkgew0KICAgICAgICAgIHRoaXMuaXNUaGlua2luZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuaXNSZXF1ZXN0aW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGRlZXBzZWVrDQogICAgYXN5bmMgZGVlcHNlZWtBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+35YWI6YCJ5oup6KaB6Kej6K+755qE5paH56ugIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmnInmraPlnKjov5vooYznmoTor7fmsYLvvIzkuK3mlq3lroMNCiAgICAgIGlmICh0aGlzLmlzUmVxdWVzdGluZykgew0KICAgICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7DQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgYXdhaXQgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOw0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCLkuK3mlq3kuYvliY3nmoTor7fmsYLlpLHotKUiLCBlKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g562J5b6F5LmL5YmN55qE6K+35rGC54q25oCB5riF55CG5a6M5oiQDQogICAgICAgIGF3YWl0IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IHRydWU7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IGZhbHNlOw0KICAgICAgdGhpcy5haURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5jaGF0TWVzc2FnZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNUaGlua2luZyA9IHRydWU7DQoNCiAgICAgIGNvbnN0IHNlbGVjdGVkQXJ0aWNsZXMgPSB0aGlzLkFydGljbGVMaXN0LmZpbHRlcigoYXJ0aWNsZSkgPT4NCiAgICAgICAgdGhpcy5pZHMuaW5jbHVkZXMoYXJ0aWNsZS5pZCkNCiAgICAgICk7DQogICAgICBjb25zdCB0aXRsZXMgPSBzZWxlY3RlZEFydGljbGVzDQogICAgICAgIC5tYXAoKGFydGljbGUpID0+IGDjgIoke2FydGljbGUuY25UaXRsZSB8fCBhcnRpY2xlLnRpdGxlfeOAi2ApDQogICAgICAgIC5qb2luKCJcbiIpOw0KDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBhcnRpY2xlc1Jlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdEJ5SWRzKHRoaXMuaWRzLmpvaW4oIiwiKSk7DQogICAgICAgIGlmICghYXJ0aWNsZXNSZXNwb25zZS5kYXRhIHx8ICFhcnRpY2xlc1Jlc3BvbnNlLmRhdGEubGVuZ3RoKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJGYWlsZWQgdG8gZ2V0IGFydGljbGUgY29udGVudHMiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGFydGljbGVzQ29udGVudCA9IGFydGljbGVzUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC5tYXAoKGFydGljbGUsIGluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCB0aXRsZSA9DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy5jblRpdGxlIHx8DQogICAgICAgICAgICAgIHNlbGVjdGVkQXJ0aWNsZXNbaW5kZXhdPy50aXRsZSB8fA0KICAgICAgICAgICAgICAiIjsNCiAgICAgICAgICAgIGNvbnN0IGNvbnRlbnQgPSBhcnRpY2xlLmNvbnRlbnQgfHwgIiI7DQogICAgICAgICAgICByZXR1cm4gYOOAkOesrCAke2luZGV4ICsgMX0g56+H5paH56ug44CR44CKJHt0aXRsZX3jgItcblxuJHtjb250ZW50fWA7DQogICAgICAgICAgfSkNCiAgICAgICAgICAuam9pbigiXG5cbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cblxuIik7DQoNCiAgICAgICAgLy8g5re75Yqg55So5oi35raI5oGvDQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goew0KICAgICAgICAgIHJvbGU6ICJ1c2VyIiwNCiAgICAgICAgICBjb250ZW50OiBg5biu5oiR5rex5bqm6Kej6K+75Lul5LiLJHt0aGlzLmlkcy5sZW5ndGh956+H5paH56ug77yaXG4ke3RpdGxlc31gLA0KICAgICAgICB9KTsNCg0KICAgICAgICAvLyDliJvlu7pBSea2iOaBr+W5tua3u+WKoOWIsOWvueivneS4rQ0KICAgICAgICBjb25zdCBhaU1lc3NhZ2UgPSB7DQogICAgICAgICAgcm9sZTogImFzc2lzdGFudCIsDQogICAgICAgICAgY29udGVudDogIiIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzLnB1c2goYWlNZXNzYWdlKTsNCiAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gdHJ1ZTsNCg0KICAgICAgICBjb25zdCBwcm9tcHQgPQ0KICAgICAgICAgIHRoaXMuYXJ0aWNsZUFpUHJvbXB0DQogICAgICAgICAgICAucmVwbGFjZSgiYXJ0aWNsZUxlbmd0aCIsIHRoaXMuaWRzLmxlbmd0aCkNCiAgICAgICAgICAgIC5yZXBsYWNlKC9cJmd0Oy9nLCAiPiIpICsNCiAgICAgICAgICBgXG5cbioq5Lul5LiL5piv5b6F5aSE55CG55qE5paH56ug77yaKipcblxuJHthcnRpY2xlc0NvbnRlbnR9YDsNCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRlZXBzZWVrQWlRYShwcm9tcHQsIHRydWUpOw0KDQogICAgICAgIGlmIChyZXNwb25zZS5vaykgew0KICAgICAgICAgIGNvbnN0IHJlYWRlciA9IHJlc3BvbnNlLmJvZHkuZ2V0UmVhZGVyKCk7DQogICAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gcmVhZGVyOyAvLyDkv53lrZjlvZPliY3nmoQgcmVhZGVyDQogICAgICAgICAgY29uc3QgZGVjb2RlciA9IG5ldyBUZXh0RGVjb2RlcigpOw0KICAgICAgICAgIGxldCBidWZmZXIgPSAiIjsNCiAgICAgICAgICBsZXQgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpOw0KDQogICAgICAgICAgY29uc3QgdXBkYXRlQ29udGVudCA9IChuZXdDb250ZW50KSA9PiB7DQogICAgICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IERhdGUubm93KCk7DQogICAgICAgICAgICBpZiAoY3VycmVudFRpbWUgLSBsYXN0VXBkYXRlVGltZSA+PSA1MCkgew0KICAgICAgICAgICAgICBhaU1lc3NhZ2UuY29udGVudCA9IG5ld0NvbnRlbnQ7DQogICAgICAgICAgICAgIGxhc3RVcGRhdGVUaW1lID0gY3VycmVudFRpbWU7DQogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSB0aGlzLiRyZWZzLmNoYXRNZXNzYWdlczsNCiAgICAgICAgICAgICAgICBpZiAoY2hhdE1lc3NhZ2VzKSB7DQogICAgICAgICAgICAgICAgICBjaGF0TWVzc2FnZXMuc2Nyb2xsVG9wID0gY2hhdE1lc3NhZ2VzLnNjcm9sbEhlaWdodDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH07DQoNCiAgICAgICAgICB3aGlsZSAodHJ1ZSkgew0KICAgICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5bey5Lit5patDQogICAgICAgICAgICBpZiAodGhpcy5pc0Fib3J0ZWQpIHsNCiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBYm9ydEVycm9yIik7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7DQogICAgICAgICAgICBpZiAoZG9uZSkgew0KICAgICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICB1cGRhdGVDb250ZW50KGJ1ZmZlcik7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnN0IGNodW5rID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpOw0KICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgY29uc3QgbGluZXMgPSBjaHVuay5zcGxpdCgiXG4iKTsNCg0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHsNCiAgICAgICAgICAgICAgICBpZiAoIWxpbmUudHJpbSgpIHx8ICFsaW5lLnN0YXJ0c1dpdGgoImRhdGE6ICIpKSBjb250aW51ZTsNCg0KICAgICAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBsaW5lLnNsaWNlKDUpOw0KICAgICAgICAgICAgICAgIGlmIChkYXRhID09PSAiW0RPTkVdIikgYnJlYWs7DQoNCiAgICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgICAgY29uc3QganNvbkRhdGEgPSBKU09OLnBhcnNlKGRhdGEpOw0KICAgICAgICAgICAgICAgICAgaWYgKGpzb25EYXRhLmNob2ljZXM/LlswXT8uZGVsdGE/LmNvbnRlbnQpIHsNCiAgICAgICAgICAgICAgICAgICAgbGV0IGNvbnRlbnQgPSBqc29uRGF0YS5jaG9pY2VzWzBdLmRlbHRhLmNvbnRlbnQ7DQoNCiAgICAgICAgICAgICAgICAgICAgLy8g6Lez6L+H54m55q6K5a2X56ymDQogICAgICAgICAgICAgICAgICAgIGlmIChjb250ZW50ID09PSAiYGBgIiB8fCBjb250ZW50ID09PSAibWFya2Rvd24iKSB7DQogICAgICAgICAgICAgICAgICAgICAgY29udGludWU7DQogICAgICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgICAgICBidWZmZXIgKz0gY29udGVudDsNCiAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ29udGVudChidWZmZXIpOw0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIHBhcnNpbmcgSlNPTjoiLCBwYXJzZUVycm9yKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgcHJvY2Vzc2luZyBjaHVuazoiLCBlKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJSZXF1ZXN0IGZhaWxlZCIpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAvLyDliKTmlq3mmK/lkKbmmK/kuK3mlq3lr7zoh7TnmoTplJnor68NCiAgICAgICAgaWYgKGVycm9yLm1lc3NhZ2UgPT09ICJBYm9ydEVycm9yIikgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCLor7fmsYLlt7LooqvkuK3mlq0iKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc29sZS5lcnJvcigiQUkgQ2hhdCBFcnJvcjoiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIkFJ6Kej6K+75aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgIGlmICh0aGlzLmNoYXRNZXNzYWdlc1sxXSkgew0KICAgICAgICAgIHRoaXMuY2hhdE1lc3NhZ2VzWzFdLmNvbnRlbnQgPSAi5oqx5q2J77yM5pyN5Yqh5Zmo57mB5b+Z77yM6K+356iN5ZCO5YaN6K+VIjsNCiAgICAgICAgfQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsgLy8g5riF55CG5b2T5YmN55qEIHJlYWRlcg0KICAgICAgICAvLyDlj6rmnInlnKjmsqHmnInooqvkuK3mlq3nmoTmg4XlhrXkuIvmiY3ph43nva7nirbmgIENCiAgICAgICAgaWYgKHRoaXMuYWlEaWFsb2dWaXNpYmxlKSB7DQogICAgICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5pc1JlcXVlc3RpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5YWz6ZetQUnlr7nor50NCiAgICBjbG9zZUFpRGlhbG9nKCkgew0KICAgICAgdGhpcy5pc0Fib3J0ZWQgPSB0cnVlOyAvLyDorr7nva7kuK3mlq3moIflv5cNCiAgICAgIGlmICh0aGlzLmN1cnJlbnRSZWFkZXIpIHsNCiAgICAgICAgdGhpcy5jdXJyZW50UmVhZGVyLmNhbmNlbCgpOyAvLyDkuK3mlq3lvZPliY3nmoTor7vlj5YNCiAgICAgIH0NCiAgICAgIHRoaXMuYWlEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmNoYXRNZXNzYWdlcyA9IFtdOw0KICAgICAgdGhpcy5pc1RoaW5raW5nID0gZmFsc2U7DQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50UmVhZGVyID0gbnVsbDsNCiAgICB9LA0KICAgIGFydGljbGVBaUNoYXQoKSB7DQogICAgICBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGlmeSIpIHsNCiAgICAgICAgdGhpcy5kaWZ5QWlDaGF0KCk7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gIm9sbGFtYSIpIHsNCiAgICAgICAgdGhpcy5vbGxhbWFBaUNoYXQoKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5haVBsYXRmb3JtID09PSAiZGVlcHNlZWsiKSB7DQogICAgICAgIHRoaXMuZGVlcHNlZWtBaUNoYXQoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYXJ0QWlDaGF0KCkgew0KICAgICAgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gImRpZnkiKSB7DQogICAgICAgIHRoaXMuZGlmeUNoYXJ0QWlDaGF0KCk7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWlQbGF0Zm9ybSA9PT0gImRlZXBzZWVrIikgew0KICAgICAgICB0aGlzLmRlZXBzZWVrQ2hhcnRBaUNoYXQoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIGRpZnnlm77ooajnnIvmnb8NCiAgICBhc3luYyBkaWZ5Q2hhcnRBaUNoYXQoKSB7DQogICAgICAvLyDlj4LmlbDmo4Dmn6UNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID4gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIueUn+aIkERlZXBzZWVr5Zu+6KGo55yL5p2/5Y+q6IO96YCJ5oup5LiA56+H5YaF5a65Iik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5pi+56S65a+56K+d5qGG5LiO5Yqg6L2954q25oCBDQogICAgICB0aGlzLmNoYXJ0RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IHRydWU7DQoNCiAgICAgIC8vIOehruS/nea4heepuuS4iuasoeeahOWGheWuuQ0KICAgICAgaWYgKHRoaXMuJHJlZnMuY2hhcnRDb250ZW50KSB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmlubmVySFRNTCA9ICIiOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICAvLyAxLiDojrflj5bmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZVJlc3VsdCA9IGF3YWl0IEFQSS5BcmVhSW5mbyh0aGlzLmlkc1swXSk7DQogICAgICAgIGlmICghYXJ0aWNsZVJlc3VsdC5kYXRhIHx8ICFhcnRpY2xlUmVzdWx0LmRhdGEuY29udGVudCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6I635Y+W5paH56ug5YaF5a655aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyAyLiDosIPnlKhBSeaOpeWPow0KICAgICAgICBjb25zdCBhaVJlc3VsdCA9IGF3YWl0IGRpZnlBaVFhKA0KICAgICAgICAgIGFydGljbGVSZXN1bHQuZGF0YS5jb250ZW50LA0KICAgICAgICAgICJibG9ja2luZyIsDQogICAgICAgICAgImRpZnkuY2hhcnQuYXBpa2V5Ig0KICAgICAgICApOw0KDQogICAgICAgIGlmICghYWlSZXN1bHQub2spIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIkFJ5o6l5Y+j6LCD55So5aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBhaURhdGEgPSBhd2FpdCBhaVJlc3VsdC5qc29uKCk7DQogICAgICAgIGlmICghYWlEYXRhIHx8ICFhaURhdGEuYW5zd2VyKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSei/lOWbnuaVsOaNruagvOW8j+mUmeivryIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8gMy4g5aSE55CGSFRNTOWGheWuuQ0KICAgICAgICBsZXQgY29udGVudDIgPSAiIjsNCg0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT07moLzlvI/vvIjmnInkupvov5Tlm57lj6/og73mmK9KU09O5a2X56ym5Liy77yJDQogICAgICAgICAgY29uc3QgcGFyc2VkRGF0YSA9IEpTT04ucGFyc2UoYWlEYXRhLmFuc3dlcik7DQogICAgICAgICAgY29udGVudDIgPQ0KICAgICAgICAgICAgcGFyc2VkRGF0YS5hbnN3ZXIgfHwNCiAgICAgICAgICAgIHBhcnNlZERhdGEuaHRtbCB8fA0KICAgICAgICAgICAgcGFyc2VkRGF0YS5jb250ZW50IHx8DQogICAgICAgICAgICBhaURhdGEuYW5zd2VyOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgLy8g5aaC5p6c5LiN5pivSlNPTuagvOW8j++8jOebtOaOpeS9v+eUqOWOn+Wni+WGheWuuQ0KICAgICAgICAgIGNvbnRlbnQyID0gYWlEYXRhLmFuc3dlcjsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhuaAneiAg+agh+iusA0KICAgICAgICBjb25zdCB0aGlua1N0YXJ0SW5kZXggPSBjb250ZW50Mi5pbmRleE9mKCI8dGhpbms+Iik7DQogICAgICAgIGNvbnN0IHRoaW5rRW5kSW5kZXggPSBjb250ZW50Mi5pbmRleE9mKCI8L3RoaW5rPiIpOw0KDQogICAgICAgIC8vIOaPkOWPluacieaViOWGheWuuQ0KICAgICAgICBpZiAodGhpbmtTdGFydEluZGV4ICE9PSAtMSAmJiB0aGlua0VuZEluZGV4ICE9PSAtMSkgew0KICAgICAgICAgIC8vIOWmguaenOWtmOWcqOaAneiAg+agh+iusO+8jOWPquWPljwvdGhpbms+5ZCO6Z2i55qE5YaF5a65DQogICAgICAgICAgY29udGVudDIgPSBjb250ZW50Mi5zdWJzdHJpbmcodGhpbmtFbmRJbmRleCArIDgpLnRyaW0oKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa4heeQhmh0bWzmoIforrDlkozlhbbku5bnibnmrorlrZfnrKYNCiAgICAgICAgY29udGVudDIgPSBjb250ZW50Mg0KICAgICAgICAgIC8vIOenu+mZpGh0bWzku6PnoIHlnZfmoIforrANCiAgICAgICAgICAucmVwbGFjZSgvYGBgaHRtbFxzKnxgYGBccyovZywgIiIpDQogICAgICAgICAgLy8g56e76Zmk5Y+v6IO95a2Y5Zyo55qE5YW25LuWaHRtbOivreiogOagh+iusO+8jOWmgmBgYGpzb27nrYkNCiAgICAgICAgICAucmVwbGFjZSgvYGBgW2EtekEtWl0qXHMqL2csICIiKQ0KICAgICAgICAgIC8vIOenu+mZpOWkmuS9meeahOepuuihjA0KICAgICAgICAgIC5yZXBsYWNlKC9cblxzKlxuXHMqXG4vZywgIlxuXG4iKQ0KICAgICAgICAgIC8vIOenu+mZpOihjOmmluihjOWwvuepuueZveWtl+espg0KICAgICAgICAgIC50cmltKCk7DQoNCiAgICAgICAgLy8g56Gu5L+d5YaF5a656Z2e56m6DQogICAgICAgIGlmICghY29udGVudDIgfHwgY29udGVudDIubGVuZ3RoIDwgMTApIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIui/lOWbnueahOWbvuihqOWGheWuueaXoOaViCIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+lSFRNTOe7k+aehOeahOWujOaVtOaAp++8jOWmguaenOS4jeWujOaVtOWImea3u+WKoOW/heimgeeahOagh+etvg0KICAgICAgICAvLyDov5nmmK/kuLrkuoblhbzlrrnlj6/og73kuI3ov5Tlm57lrozmlbRIVE1M55qE5oOF5Ya1DQogICAgICAgIGxldCBmaW5hbEh0bWwgPSBjb250ZW50MjsNCg0KICAgICAgICAvLyDlsIblkITnp43lvaLlvI/nmoTlpJbpg6hDRE7lvJXnlKjmm7/mjaLkuLrmnKzlnLDmlofku7YNCiAgICAgICAgLy8g5pu/5o2i5Y+M5byV5Y+354mI5pysDQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9odHRwczpcL1wvY2RuXC5qc2RlbGl2clwubmV0XC9ucG1cL2NoYXJ0XC5qcy9nLA0KICAgICAgICAgICIvY2hhcnQuanMiDQogICAgICAgICk7DQogICAgICAgIC8vIOabv+aNouWNleW8leWPt+eJiOacrA0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvXCdodHRwczpcL1wvY2RuXC5qc2RlbGl2clwubmV0XC9ucG1cL2NoYXJ0XC5qc1wnL2csDQogICAgICAgICAgIicvY2hhcnQuanMnIg0KICAgICAgICApOw0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvXCJodHRwczpcL1wvY2RuXC5qc2RlbGl2clwubmV0XC9ucG1cL2NoYXJ0XC5qc1wiL2csDQogICAgICAgICAgJyIvY2hhcnQuanMiJw0KICAgICAgICApOw0KICAgICAgICAvLyDmm7/mjaLlj6/og73luKbmnInniYjmnKzlj7fnmoTlvJXnlKgNCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL2h0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzQFxkK1wuXGQrXC5cZCsvZywNCiAgICAgICAgICAiL2NoYXJ0LmpzIg0KICAgICAgICApOw0KICAgICAgICAvLyDmm7/mjaLlhbbku5blj6/og73nmoRDRE4NCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL2h0dHBzOlwvXC9jZG5qc1wuY2xvdWRmbGFyZVwuY29tXC9hamF4XC9saWJzXC9DaGFydFwuanNcL1xkK1wuXGQrXC5cZCtcL2NoYXJ0KFwubWluKT9cLmpzL2csDQogICAgICAgICAgIi9jaGFydC5qcyINCiAgICAgICAgKTsNCg0KICAgICAgICBpZiAoIWZpbmFsSHRtbC5pbmNsdWRlcygiPCFET0NUWVBFIikgJiYgIWZpbmFsSHRtbC5pbmNsdWRlcygiPGh0bWwiKSkgew0KICAgICAgICAgIC8vIOWGheWuueWPquaYr0hUTUzniYfmrrXvvIzpnIDopoHmt7vliqDlrozmlbTnu5PmnoQNCiAgICAgICAgICBmaW5hbEh0bWwgPQ0KICAgICAgICAgICAgIjwhRE9DVFlQRSBodG1sPiIgKw0KICAgICAgICAgICAgIjxodG1sPiIgKw0KICAgICAgICAgICAgIjxoZWFkPiIgKw0KICAgICAgICAgICAgJyAgPG1ldGEgY2hhcnNldD0iVVRGLTgiPicgKw0KICAgICAgICAgICAgJyAgPG1ldGEgbmFtZT0idmlld3BvcnQiIGNvbnRlbnQ9IndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xLjAiPicgKw0KICAgICAgICAgICAgJyAgPHNjcmlwdCBzcmM9Ii9jaGFydC5qcyI+PFwvc2NyaXB0PicgKw0KICAgICAgICAgICAgIjwvaGVhZD4iICsNCiAgICAgICAgICAgICI8Ym9keT4iICsNCiAgICAgICAgICAgICIgICIgKw0KICAgICAgICAgICAgY29udGVudDIgKw0KICAgICAgICAgICAgIjwvYm9keT4iICsNCiAgICAgICAgICAgICI8L2h0bWw+IjsNCiAgICAgICAgfSBlbHNlIGlmICgNCiAgICAgICAgICAhZmluYWxIdG1sLmluY2x1ZGVzKCI8c2NyaXB0IikgJiYNCiAgICAgICAgICBmaW5hbEh0bWwuaW5jbHVkZXMoIjxjYW52YXMiKQ0KICAgICAgICApIHsNCiAgICAgICAgICAvLyDmnIljYW52YXPkvYbmsqHmnIlzY3JpcHTmoIfnrb7vvIzlj6/og73nvLrlsJFDaGFydC5qc+W8leeUqA0KICAgICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgICAgIjxoZWFkPiIsDQogICAgICAgICAgICAiPGhlYWQ+IiArICcgIDxzY3JpcHQgc3JjPSIvY2hhcnQuanMiPjxcL3NjcmlwdD4nDQogICAgICAgICAgKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIDQuIOWIm+W7umlmcmFtZeW5tua4suafkw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuJHJlZnMuY2hhcnRDb250ZW50KSB7DQogICAgICAgICAgICAvLyDmuIXnkIbkuYvliY3nmoRpZnJhbWUNCiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRDaGFydElmcmFtZSkgew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLm9ubG9hZCA9IG51bGw7DQogICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUub25lcnJvciA9IG51bGw7DQogICAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLmuIXnkIbkuYvliY3nmoRpZnJhbWXlpLHotKU6IiwgZSk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5Yib5bu6aWZyYW1lDQogICAgICAgICAgICBjb25zdCBpZnJhbWUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJpZnJhbWUiKTsNCiAgICAgICAgICAgIGlmcmFtZS5zdHlsZS53aWR0aCA9ICIxMDAlIjsNCiAgICAgICAgICAgIGlmcmFtZS5zdHlsZS5oZWlnaHQgPSAiNjAwcHgiOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLmJvcmRlciA9ICJub25lIjsNCiAgICAgICAgICAgIGlmcmFtZS5zdHlsZS5kaXNwbGF5ID0gImJsb2NrIjsNCiAgICAgICAgICAgIGlmcmFtZS5zdHlsZS5vdmVyZmxvdyA9ICJhdXRvIjsNCg0KICAgICAgICAgICAgLy8g5L+d5a2YaWZyYW1l5byV55SoDQogICAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZSA9IGlmcmFtZTsNCg0KICAgICAgICAgICAgLy8g5riF56m65a655Zmo5bm25re75YqgaWZyYW1lDQogICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJ0Q29udGVudC5pbm5lckhUTUwgPSAiIjsNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmFwcGVuZENoaWxkKGlmcmFtZSk7DQoNCiAgICAgICAgICAgIC8vIOWcqGlmcmFtZeWKoOi9veWujOaIkOWQjumHjeaWsOaJp+ihjOiEmuacrOW5tumakOiXj+WKoOi9veeKtuaAgQ0KICAgICAgICAgICAgaWZyYW1lLm9ubG9hZCA9ICgpID0+IHsNCiAgICAgICAgICAgICAgdHJ5IHsNCiAgICAgICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbpnIDopoHliqDovb3mnKzlnLBDaGFydC5qcw0KICAgICAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgICAgICFpZnJhbWUuY29udGVudFdpbmRvdy5DaGFydCAmJg0KICAgICAgICAgICAgICAgICAgIWlmcmFtZS5jb250ZW50V2luZG93LmRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoDQogICAgICAgICAgICAgICAgICAgICdzY3JpcHRbc3JjKj0iY2hhcnQuanMiIGldJw0KICAgICAgICAgICAgICAgICAgKQ0KICAgICAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICAgICAgLy8g5aaC5p6caWZyYW1l5YaF5rKh5pyJ5Yqg6L29Q2hhcnQuanPvvIzmiYvliqjmt7vliqDmnKzlnLBDaGFydC5qcw0KICAgICAgICAgICAgICAgICAgY29uc3QgY2hhcnRTY3JpcHQgPQ0KICAgICAgICAgICAgICAgICAgICBpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudC5jcmVhdGVFbGVtZW50KCJzY3JpcHQiKTsNCiAgICAgICAgICAgICAgICAgIGNoYXJ0U2NyaXB0LnNyYyA9ICIvY2hhcnQuanMiOw0KICAgICAgICAgICAgICAgICAgaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChjaGFydFNjcmlwdCk7DQoNCiAgICAgICAgICAgICAgICAgIC8vIOetieW+hUNoYXJ0Lmpz5Yqg6L295a6M5oiQ5ZCO5YaN5omn6KGM5ZCO57ut6ISa5pysDQogICAgICAgICAgICAgICAgICBjaGFydFNjcmlwdC5vbmxvYWQgPSAoKSA9PiB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuZXhlY3V0ZUlmcmFtZVNjcmlwdHMoaWZyYW1lKTsNCiAgICAgICAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgICAgICAgIC8vIOWmguaenOiEmuacrOWKoOi9veWksei0pe+8jOS5n+mcgOimgemakOiXj+WKoOi9veWKqOeUuw0KICAgICAgICAgICAgICAgICAgY2hhcnRTY3JpcHQub25lcnJvciA9ICgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L295pys5ZywQ2hhcnQuanPlpLHotKUiKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIC8vIOebtOaOpeaJp+ihjOWGheiBlOiEmuacrA0KICAgICAgICAgICAgICAgICAgdGhpcy5leGVjdXRlSWZyYW1lU2NyaXB0cyhpZnJhbWUpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiEmuacrOaJp+ihjOWHuumUmToiLCBlKTsNCiAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9Ow0KDQogICAgICAgICAgICAvLyDmt7vliqDplJnor6/lpITnkIYNCiAgICAgICAgICAgIGlmcmFtZS5vbmVycm9yID0gKCkgPT4gew0KICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCJpZnJhbWXliqDovb3lpLHotKUiKTsNCiAgICAgICAgICAgICAgdGhpcy5jaGFydExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgIC8vIOWGmeWFpeWGheWuuQ0KICAgICAgICAgICAgY29uc3QgZG9jID0gaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQ7DQogICAgICAgICAgICBkb2Mub3BlbigpOw0KICAgICAgICAgICAgZG9jLndyaXRlKGZpbmFsSHRtbCk7DQogICAgICAgICAgICBkb2MuY2xvc2UoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi55Sf5oiQ5Zu+6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihlcnJvci5tZXNzYWdlIHx8ICLnlJ/miJDlm77ooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgdGhpcy5jbG9zZUNoYXJ0RGlhbG9nKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyBkZWVwc2Vla+WbvuihqOeci+advw0KICAgIGFzeW5jIGRlZXBzZWVrQ2hhcnRBaUNoYXQoKSB7DQogICAgICAvLyDlj4LmlbDmo4Dmn6UNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nopoHop6Por7vnmoTmlofnq6AiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID4gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIueUn+aIkERlZXBzZWVr5Zu+6KGo55yL5p2/5Y+q6IO96YCJ5oup5LiA56+H5YaF5a65Iik7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgLy8g5pi+56S65a+56K+d5qGG5LiO5Yqg6L2954q25oCBDQogICAgICB0aGlzLmNoYXJ0RGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IHRydWU7DQoNCiAgICAgIC8vIOehruS/nea4heepuuS4iuasoeeahOWGheWuuQ0KICAgICAgaWYgKHRoaXMuJHJlZnMuY2hhcnRDb250ZW50KSB7DQogICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmlubmVySFRNTCA9ICIiOw0KICAgICAgfQ0KDQogICAgICB0cnkgew0KICAgICAgICAvLyAxLiDojrflj5bmlofnq6DlhoXlrrkNCiAgICAgICAgY29uc3QgYXJ0aWNsZVJlc3VsdCA9IGF3YWl0IEFQSS5BcmVhSW5mbyh0aGlzLmlkc1swXSk7DQogICAgICAgIGlmICghYXJ0aWNsZVJlc3VsdC5kYXRhIHx8ICFhcnRpY2xlUmVzdWx0LmRhdGEuY29udGVudCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6I635Y+W5paH56ug5YaF5a655aSx6LSlIik7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCBwcm9tcHQgPSB0aGlzLmNoYXJ0UHJvbXB0ICsgYFxuXG4ke2FydGljbGVSZXN1bHQuZGF0YS5jb250ZW50fWA7DQoNCiAgICAgICAgLy8gMi4g6LCD55SoQUnmjqXlj6MNCiAgICAgICAgY29uc3QgYWlSZXN1bHQgPSBhd2FpdCBkZWVwc2Vla0FpUWEocHJvbXB0LCBmYWxzZSk7DQoNCiAgICAgICAgaWYgKCFhaVJlc3VsdC5vaykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiQUnmjqXlj6PosIPnlKjlpLHotKUiKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGFpRGF0YSA9IGF3YWl0IGFpUmVzdWx0Lmpzb24oKTsNCiAgICAgICAgaWYgKCFhaURhdGEgfHwgIWFpRGF0YS5jaG9pY2VzKSB7DQogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJBSei/lOWbnuaVsOaNruagvOW8j+mUmeivryIpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8gMy4g5aSE55CGSFRNTOWGheWuuQ0KICAgICAgICBsZXQgY29udGVudDIgPSBhaURhdGEuY2hvaWNlc1swXS5tZXNzYWdlLmNvbnRlbnQ7DQoNCiAgICAgICAgLy8g5riF55CGaHRtbOagh+iusOWSjOWFtuS7lueJueauiuWtl+espg0KICAgICAgICBjb250ZW50MiA9IGNvbnRlbnQyDQogICAgICAgICAgLy8g56e76ZmkaHRtbOS7o+eggeWdl+agh+iusA0KICAgICAgICAgIC5yZXBsYWNlKC9gYGBodG1sXHMqfGBgYFxzKi9nLCAiIikNCiAgICAgICAgICAvLyDnp7vpmaTlj6/og73lrZjlnKjnmoTlhbbku5ZodG1s6K+t6KiA5qCH6K6w77yM5aaCYGBganNvbuetiQ0KICAgICAgICAgIC5yZXBsYWNlKC9gYGBbYS16QS1aXSpccyovZywgIiIpDQogICAgICAgICAgLy8g56e76Zmk5aSa5L2Z55qE56m66KGMDQogICAgICAgICAgLnJlcGxhY2UoL1xuXHMqXG5ccypcbi9nLCAiXG5cbiIpDQogICAgICAgICAgLy8g56e76Zmk6KGM6aaW6KGM5bC+56m655m95a2X56ymDQogICAgICAgICAgLnRyaW0oKTsNCg0KICAgICAgICAvLyDnoa7kv53lhoXlrrnpnZ7nqboNCiAgICAgICAgaWYgKCFjb250ZW50MiB8fCBjb250ZW50Mi5sZW5ndGggPCAxMCkgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigi6L+U5Zue55qE5Zu+6KGo5YaF5a655peg5pWIIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmo4Dmn6VIVE1M57uT5p6E55qE5a6M5pW05oCn77yM5aaC5p6c5LiN5a6M5pW05YiZ5re75Yqg5b+F6KaB55qE5qCH562+DQogICAgICAgIC8vIOi/meaYr+S4uuS6huWFvOWuueWPr+iDveS4jei/lOWbnuWujOaVtEhUTUznmoTmg4XlhrUNCiAgICAgICAgbGV0IGZpbmFsSHRtbCA9IGNvbnRlbnQyOw0KDQogICAgICAgIC8vIOWwhuWQhOenjeW9ouW8j+eahOWklumDqENETuW8leeUqOabv+aNouS4uuacrOWcsOaWh+S7tg0KICAgICAgICAvLyDmm7/mjaLlj4zlvJXlj7fniYjmnKwNCiAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgL2h0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzL2csDQogICAgICAgICAgIi9jaGFydC5qcyINCiAgICAgICAgKTsNCiAgICAgICAgLy8g5pu/5o2i5Y2V5byV5Y+354mI5pysDQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9cJ2h0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzXCcvZywNCiAgICAgICAgICAiJy9jaGFydC5qcyciDQogICAgICAgICk7DQogICAgICAgIGZpbmFsSHRtbCA9IGZpbmFsSHRtbC5yZXBsYWNlKA0KICAgICAgICAgIC9cImh0dHBzOlwvXC9jZG5cLmpzZGVsaXZyXC5uZXRcL25wbVwvY2hhcnRcLmpzXCIvZywNCiAgICAgICAgICAnIi9jaGFydC5qcyInDQogICAgICAgICk7DQogICAgICAgIC8vIOabv+aNouWPr+iDveW4puacieeJiOacrOWPt+eahOW8leeUqA0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvaHR0cHM6XC9cL2NkblwuanNkZWxpdnJcLm5ldFwvbnBtXC9jaGFydFwuanNAXGQrXC5cZCtcLlxkKy9nLA0KICAgICAgICAgICIvY2hhcnQuanMiDQogICAgICAgICk7DQogICAgICAgIC8vIOabv+aNouWFtuS7luWPr+iDveeahENETg0KICAgICAgICBmaW5hbEh0bWwgPSBmaW5hbEh0bWwucmVwbGFjZSgNCiAgICAgICAgICAvaHR0cHM6XC9cL2NkbmpzXC5jbG91ZGZsYXJlXC5jb21cL2FqYXhcL2xpYnNcL0NoYXJ0XC5qc1wvXGQrXC5cZCtcLlxkK1wvY2hhcnQoXC5taW4pP1wuanMvZywNCiAgICAgICAgICAiL2NoYXJ0LmpzIg0KICAgICAgICApOw0KDQogICAgICAgIGlmICghZmluYWxIdG1sLmluY2x1ZGVzKCI8IURPQ1RZUEUiKSAmJiAhZmluYWxIdG1sLmluY2x1ZGVzKCI8aHRtbCIpKSB7DQogICAgICAgICAgLy8g5YaF5a655Y+q5pivSFRNTOeJh+aute+8jOmcgOimgea3u+WKoOWujOaVtOe7k+aehA0KICAgICAgICAgIGZpbmFsSHRtbCA9DQogICAgICAgICAgICAiPCFET0NUWVBFIGh0bWw+IiArDQogICAgICAgICAgICAiPGh0bWw+IiArDQogICAgICAgICAgICAiPGhlYWQ+IiArDQogICAgICAgICAgICAnICA8bWV0YSBjaGFyc2V0PSJVVEYtOCI+JyArDQogICAgICAgICAgICAnICA8bWV0YSBuYW1lPSJ2aWV3cG9ydCIgY29udGVudD0id2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTEuMCI+JyArDQogICAgICAgICAgICAnICA8c2NyaXB0IHNyYz0iL2NoYXJ0LmpzIj48XC9zY3JpcHQ+JyArDQogICAgICAgICAgICAiPC9oZWFkPiIgKw0KICAgICAgICAgICAgIjxib2R5PiIgKw0KICAgICAgICAgICAgIiAgIiArDQogICAgICAgICAgICBjb250ZW50MiArDQogICAgICAgICAgICAiPC9ib2R5PiIgKw0KICAgICAgICAgICAgIjwvaHRtbD4iOw0KICAgICAgICB9IGVsc2UgaWYgKA0KICAgICAgICAgICFmaW5hbEh0bWwuaW5jbHVkZXMoIjxzY3JpcHQiKSAmJg0KICAgICAgICAgIGZpbmFsSHRtbC5pbmNsdWRlcygiPGNhbnZhcyIpDQogICAgICAgICkgew0KICAgICAgICAgIC8vIOaciWNhbnZhc+S9huayoeaciXNjcmlwdOagh+etvu+8jOWPr+iDvee8uuWwkUNoYXJ0Lmpz5byV55SoDQogICAgICAgICAgZmluYWxIdG1sID0gZmluYWxIdG1sLnJlcGxhY2UoDQogICAgICAgICAgICAiPGhlYWQ+IiwNCiAgICAgICAgICAgICI8aGVhZD4iICsgJyAgPHNjcmlwdCBzcmM9Ii9jaGFydC5qcyI+PFwvc2NyaXB0PicNCiAgICAgICAgICApOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8gNC4g5Yib5bu6aWZyYW1l5bm25riy5p+TDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBpZiAodGhpcy4kcmVmcy5jaGFydENvbnRlbnQpIHsNCiAgICAgICAgICAgIC8vIOa4heeQhuS5i+WJjeeahGlmcmFtZQ0KICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudENoYXJ0SWZyYW1lKSB7DQogICAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgICAgdGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUub25sb2FkID0gbnVsbDsNCiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5vbmVycm9yID0gbnVsbDsNCiAgICAgICAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIua4heeQhuS5i+WJjeeahGlmcmFtZeWksei0pToiLCBlKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDliJvlu7ppZnJhbWUNCiAgICAgICAgICAgIGNvbnN0IGlmcmFtZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImlmcmFtZSIpOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLndpZHRoID0gIjEwMCUiOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLmhlaWdodCA9ICI2MDBweCI7DQogICAgICAgICAgICBpZnJhbWUuc3R5bGUuYm9yZGVyID0gIm5vbmUiOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLmRpc3BsYXkgPSAiYmxvY2siOw0KICAgICAgICAgICAgaWZyYW1lLnN0eWxlLm92ZXJmbG93ID0gImF1dG8iOw0KDQogICAgICAgICAgICAvLyDkv53lrZhpZnJhbWXlvJXnlKgNCiAgICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lID0gaWZyYW1lOw0KDQogICAgICAgICAgICAvLyDmuIXnqbrlrrnlmajlubbmt7vliqBpZnJhbWUNCiAgICAgICAgICAgIHRoaXMuJHJlZnMuY2hhcnRDb250ZW50LmlubmVySFRNTCA9ICIiOw0KICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFydENvbnRlbnQuYXBwZW5kQ2hpbGQoaWZyYW1lKTsNCg0KICAgICAgICAgICAgLy8g5ZyoaWZyYW1l5Yqg6L295a6M5oiQ5ZCO6YeN5paw5omn6KGM6ISa5pys5bm26ZqQ6JeP5Yqg6L2954q25oCBDQogICAgICAgICAgICBpZnJhbWUub25sb2FkID0gKCkgPT4gew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpumcgOimgeWKoOi9veacrOWcsENoYXJ0LmpzDQogICAgICAgICAgICAgICAgaWYgKA0KICAgICAgICAgICAgICAgICAgIWlmcmFtZS5jb250ZW50V2luZG93LkNoYXJ0ICYmDQogICAgICAgICAgICAgICAgICAhaWZyYW1lLmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQucXVlcnlTZWxlY3RvcigNCiAgICAgICAgICAgICAgICAgICAgJ3NjcmlwdFtzcmMqPSJjaGFydC5qcyIgaV0nDQogICAgICAgICAgICAgICAgICApDQogICAgICAgICAgICAgICAgKSB7DQogICAgICAgICAgICAgICAgICAvLyDlpoLmnpxpZnJhbWXlhoXmsqHmnInliqDovb1DaGFydC5qc++8jOaJi+WKqOa3u+WKoOacrOWcsENoYXJ0LmpzDQogICAgICAgICAgICAgICAgICBjb25zdCBjaGFydFNjcmlwdCA9DQogICAgICAgICAgICAgICAgICAgIGlmcmFtZS5jb250ZW50V2luZG93LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoInNjcmlwdCIpOw0KICAgICAgICAgICAgICAgICAgY2hhcnRTY3JpcHQuc3JjID0gIi9jaGFydC5qcyI7DQogICAgICAgICAgICAgICAgICBpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKGNoYXJ0U2NyaXB0KTsNCg0KICAgICAgICAgICAgICAgICAgLy8g562J5b6FQ2hhcnQuanPliqDovb3lrozmiJDlkI7lho3miafooYzlkI7nu63ohJrmnKwNCiAgICAgICAgICAgICAgICAgIGNoYXJ0U2NyaXB0Lm9ubG9hZCA9ICgpID0+IHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5leGVjdXRlSWZyYW1lU2NyaXB0cyhpZnJhbWUpOw0KICAgICAgICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgICAgICAgLy8g5aaC5p6c6ISa5pys5Yqg6L295aSx6LSl77yM5Lmf6ZyA6KaB6ZqQ6JeP5Yqg6L295Yqo55S7DQogICAgICAgICAgICAgICAgICBjaGFydFNjcmlwdC5vbmVycm9yID0gKCkgPT4gew0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3mnKzlnLBDaGFydC5qc+Wksei0pSIpOw0KICAgICAgICAgICAgICAgICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgLy8g55u05o6l5omn6KGM5YaF6IGU6ISa5pysDQogICAgICAgICAgICAgICAgICB0aGlzLmV4ZWN1dGVJZnJhbWVTY3JpcHRzKGlmcmFtZSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigi6ISa5pys5omn6KGM5Ye66ZSZOiIsIGUpOw0KICAgICAgICAgICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgIC8vIOa3u+WKoOmUmeivr+WkhOeQhg0KICAgICAgICAgICAgaWZyYW1lLm9uZXJyb3IgPSAoKSA9PiB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoImlmcmFtZeWKoOi9veWksei0pSIpOw0KICAgICAgICAgICAgICB0aGlzLmNoYXJ0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgfTsNCg0KICAgICAgICAgICAgLy8g5YaZ5YWl5YaF5a65DQogICAgICAgICAgICBjb25zdCBkb2MgPSBpZnJhbWUuY29udGVudFdpbmRvdy5kb2N1bWVudDsNCiAgICAgICAgICAgIGRvYy5vcGVuKCk7DQogICAgICAgICAgICBkb2Mud3JpdGUoZmluYWxIdG1sKTsNCiAgICAgICAgICAgIGRvYy5jbG9zZSgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLnlJ/miJDlm77ooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKGVycm9yLm1lc3NhZ2UgfHwgIueUn+aIkOWbvuihqOWksei0pe+8jOivt+eojeWQjumHjeivlSIpOw0KICAgICAgICB0aGlzLmNsb3NlQ2hhcnREaWFsb2coKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWFs+mXreWbvuihqOWvueivneahhg0KICAgIGNsb3NlQ2hhcnREaWFsb2coKSB7DQogICAgICB0aGlzLmlzQWJvcnRlZCA9IHRydWU7DQogICAgICB0aGlzLmNoYXJ0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5jaGFydEh0bWwgPSAiIjsNCiAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICB0aGlzLmlzUmVxdWVzdGluZyA9IGZhbHNlOw0KDQogICAgICAvLyDmuIXnkIZDaGFydOWunuS+iw0KICAgICAgaWYgKHRoaXMuY3VycmVudENoYXJ0SWZyYW1lICYmIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLmNvbnRlbnRXaW5kb3cpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICAvLyDlsJ3or5XplIDmr4HmiYDmnIlDaGFydOWunuS+iw0KICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5jb250ZW50V2luZG93LkNoYXJ0KSB7DQogICAgICAgICAgICBjb25zdCBpbnN0YW5jZXMgPQ0KICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5jb250ZW50V2luZG93LkNoYXJ0Lmluc3RhbmNlczsNCiAgICAgICAgICAgIGlmIChpbnN0YW5jZXMpIHsNCiAgICAgICAgICAgICAgT2JqZWN0LnZhbHVlcyhpbnN0YW5jZXMpLmZvckVhY2goKGluc3RhbmNlKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKGluc3RhbmNlICYmIHR5cGVvZiBpbnN0YW5jZS5kZXN0cm95ID09PSAiZnVuY3Rpb24iKSB7DQogICAgICAgICAgICAgICAgICBpbnN0YW5jZS5kZXN0cm95KCk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmuIXnkIZDaGFydOWunuS+i+Wksei0pToiLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDmuIXnqbrlm77ooajlrrnlmajlhoXlrrkNCiAgICAgIGlmICh0aGlzLiRyZWZzLmNoYXJ0Q29udGVudCkgew0KICAgICAgICB0aGlzLiRyZWZzLmNoYXJ0Q29udGVudC5pbm5lckhUTUwgPSAiIjsNCiAgICAgIH0NCg0KICAgICAgLy8g5riF55CGaWZyYW1l5byV55SoDQogICAgICBpZiAodGhpcy5jdXJyZW50Q2hhcnRJZnJhbWUpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRDaGFydElmcmFtZS5vbmxvYWQgPSBudWxsOw0KICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lLm9uZXJyb3IgPSBudWxsOw0KICAgICAgICAgIHRoaXMuY3VycmVudENoYXJ0SWZyYW1lID0gbnVsbDsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIua4heeQhmlmcmFtZeWksei0pToiLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5omn6KGMaWZyYW1l5YaF55qE5omA5pyJ5YaF6IGU6ISa5pysDQogICAgZXhlY3V0ZUlmcmFtZVNjcmlwdHMoaWZyYW1lKSB7DQogICAgICAvLyDnroDljJblkI7nmoTmlrnms5XvvIzkuI3lho3lsJ3or5XmiYvliqjmiafooYzohJrmnKwNCiAgICAgIGNvbnNvbGUubG9nKCLlm77ooahpZnJhbWXlt7LliqDovb3vvIznrYnlvoXoh6rnhLbmuLLmn5MuLi4iKTsNCg0KICAgICAgLy8g56Gu5L+d5omA5pyJ5Zu+6KGo6YO95pyJ5py65Lya5riy5p+T5ZCO5YaN6ZqQ6JePbG9hZGluZw0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuY2hhcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICB9LCA4MDApOw0KICAgIH0sDQoNCiAgICAvLyDmlofnq6Dljrvph43mlrnms5UNCiAgICBkZWR1cGxpY2F0ZUFydGljbGVzKGFydGljbGVzKSB7DQogICAgICBpZiAoIWFydGljbGVzIHx8IGFydGljbGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gYXJ0aWNsZXM7DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHRpdGxlTWFwID0gbmV3IE1hcCgpOw0KICAgICAgY29uc3QgcmVzdWx0ID0gW107DQoNCiAgICAgIC8vIOe7n+iuoeebuOWQjOagh+mimOeahOaWh+eroOaVsOmHjw0KICAgICAgYXJ0aWNsZXMuZm9yRWFjaCgoYXJ0aWNsZSkgPT4gew0KICAgICAgICAvLyDljrvpmaRIVE1M5qCH562+5ZKM5omA5pyJ56m65qC85p2l5q+U6L6D5qCH6aKYDQogICAgICAgIGNvbnN0IGNsZWFuVGl0bGUgPSBhcnRpY2xlLnRpdGxlDQogICAgICAgICAgPyBhcnRpY2xlLnRpdGxlLnJlcGxhY2UoLzxbXj5dKj4vZywgIiIpLnJlcGxhY2UoL1xzKy9nLCAiIikNCiAgICAgICAgICA6ICIiOw0KDQogICAgICAgIGlmICh0aXRsZU1hcC5oYXMoY2xlYW5UaXRsZSkpIHsNCiAgICAgICAgICB0aXRsZU1hcC5nZXQoY2xlYW5UaXRsZSkuY291bnQrKzsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aXRsZU1hcC5zZXQoY2xlYW5UaXRsZSwgew0KICAgICAgICAgICAgYXJ0aWNsZTogeyAuLi5hcnRpY2xlIH0sDQogICAgICAgICAgICBjb3VudDogMSwNCiAgICAgICAgICAgIG9yaWdpbmFsVGl0bGU6IGFydGljbGUudGl0bGUsIC8vIOS/neWtmOWOn+Wni+agh+mimO+8iOWPr+iDveWMheWQq0hUTUzmoIfnrb7vvIkNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIC8vIOeUn+aIkOWOu+mHjeWQjueahOaWh+eroOWIl+ihqA0KICAgICAgdGl0bGVNYXAuZm9yRWFjaCgoeyBhcnRpY2xlLCBjb3VudCwgb3JpZ2luYWxUaXRsZSB9KSA9PiB7DQogICAgICAgIGlmIChjb3VudCA+IDEpIHsNCiAgICAgICAgICAvLyDlpoLmnpzmnInph43lpI3vvIzlnKjmoIfpopjlkI7pnaLliqDkuIrmlbDph4/moIforrANCiAgICAgICAgICAvLyDkvb/nlKjljp/lp4vmoIfpopjvvIjkv53mjIFIVE1M5qC85byP77yJDQogICAgICAgICAgYXJ0aWNsZS50aXRsZSA9IGAke29yaWdpbmFsVGl0bGUgfHwgIiJ977yIJHtjb3VudH3vvIlgOw0KICAgICAgICB9DQogICAgICAgIHJlc3VsdC5wdXNoKGFydGljbGUpOw0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiByZXN1bHQ7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["Wechat.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAshBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "Wechat.vue", "sourceRoot": "src/views/InfoEscalation", "sourcesContent": ["<template>\r\n  <div v-loading=\"globalLoading\" element-loading-text=\"数据加载中\">\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"26\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n        />\r\n      </pane>\r\n      <pane min-size=\"50\" max-size=\"80\" size=\"74\">\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <el-form\r\n            :model=\"queryParams\"\r\n            ref=\"Form\"\r\n            label-width=\"90px\"\r\n            @submit.native.prevent\r\n          >\r\n            <el-form-item label=\"发布日期:\" prop=\"dateType\">\r\n              <el-radio-group v-model=\"queryParams.dateType\" size=\"small\">\r\n                <el-radio-button :label=\"1\">今天</el-radio-button>\r\n                <el-radio-button :label=\"2\">近2天</el-radio-button>\r\n                <el-radio-button :label=\"4\">近7天</el-radio-button>\r\n                <el-radio-button :label=\"5\">近30天</el-radio-button>\r\n                <el-radio-button :label=\"10\">全部</el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <div style=\"display: flex\">\r\n              <el-form-item\r\n                label=\"小信优选:\"\r\n                prop=\"isTechnology\"\r\n                style=\"margin-right: 20px\"\r\n              >\r\n                <el-radio-group v-model=\"queryParams.isTechnology\" size=\"small\">\r\n                  <el-radio-button\r\n                    v-for=\"dict in dict.type.is_technology\"\r\n                    :label=\"dict.value\"\r\n                    :key=\"'is_technology' + dict.value\"\r\n                    >{{ dict.label }}</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item label=\"小信精选:\" prop=\"emotion\">\r\n                <el-radio-group v-model=\"queryParams.emotion\" size=\"small\">\r\n                  <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                    >选中</el-radio-button\r\n                  >\r\n                  <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                    >全部</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n            <el-form-item label=\"检索词库:\" prop=\"tags\">\r\n              <el-radio-group v-model=\"queryParams.tags\" size=\"small\">\r\n                <el-radio :label=\"''\">全部</el-radio>\r\n                <el-radio\r\n                  v-for=\"item in tagsList1\"\r\n                  :key=\"item.id\"\r\n                  :label=\"item.id\"\r\n                  >{{ item.name }}</el-radio\r\n                >\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item\r\n              style=\"width: 100%; overflow: auto\"\r\n              label=\"\"\r\n              prop=\"tagsSubset\"\r\n              v-if=\"queryParams.tags != ''\"\r\n            >\r\n              <el-checkbox\r\n                style=\"float: left; margin-right: 30px\"\r\n                :indeterminate=\"isIndeterminate\"\r\n                v-model=\"checkAll\"\r\n                @change=\"handleCheckAllTagsSubset\"\r\n                >全选</el-checkbox\r\n              >\r\n              <el-checkbox-group v-model=\"queryParams.tagsSubset\">\r\n                <el-checkbox\r\n                  v-for=\"item in tagsList\"\r\n                  :key=\"item.name\"\r\n                  :label=\"item.name\"\r\n                ></el-checkbox>\r\n              </el-checkbox-group>\r\n            </el-form-item>\r\n            <el-form-item class=\"keyword\" label=\"关键词:\" prop=\"keywords\">\r\n              <el-input\r\n                ref=\"keywordRef\"\r\n                placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                style=\"width: 430px\"\r\n                v-model=\"queryParams.keywords\"\r\n                @focus=\"showHistoryList()\"\r\n                @blur=\"hideHistoryList()\"\r\n                @keyup.enter.native=\"handleSearch()\"\r\n              >\r\n              </el-input>\r\n              <div class=\"history\" v-show=\"showHistory\">\r\n                <div\r\n                  class=\"historyItem\"\r\n                  v-for=\"(history, index) in historyList\"\r\n                  :key=\"index\"\r\n                  v-loading=\"historyLoading\"\r\n                >\r\n                  <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                    {{ history.keyword }}\r\n                  </div>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"removeHistory(history, 1)\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >删除</el-button\r\n                  >\r\n                </div>\r\n                <div class=\"historyItem\">\r\n                  <el-button type=\"text\" @click=\"moreHistory()\">更多</el-button>\r\n                  <el-button\r\n                    type=\"text\"\r\n                    @click=\"clearHistory()\"\r\n                    style=\"color: #999; font-size: 12px\"\r\n                    >清空</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                style=\"margin-left: 10px; height: 36px\"\r\n                @click=\"handleSearch\"\r\n                >搜索</el-button\r\n              >\r\n            </el-form-item>\r\n            <div class=\"keyword-tip\">\r\n              *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n            </div>\r\n          </el-form>\r\n          <div class=\"TopBtnGroup\">\r\n            <div class=\"TopBtnGroup_left\">\r\n              <el-checkbox v-model=\"checked\" @change=\"handleCheckAllChange\"\r\n                >全选</el-checkbox\r\n              >\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  title=\"批量删除文章\"\r\n                  class=\"icon-shanchu\"\r\n                  @click=\"batchDelete\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"icon-shuaxin-copy\"\r\n                  title=\"刷新\"\r\n                  @click=\"handleSearch('refresh')\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p class=\"toolTitle\">\r\n              <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"openReport\" v-hasPermi=\"['result:report:add']\"></i>\r\n            </p> -->\r\n              <!-- <p class=\"toolTitle\">\r\n              <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\"\r\n                v-hasPermi=\"['article:collection:snapshot']\" @click=\"resultEvent()\"></i>\r\n            </p> -->\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"添加到工作台账\"\r\n                  @click=\"openTaizhang\"\r\n                  v-hasPermi=\"['article:work:add']\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-document-add\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"发布到每日最新热点\"\r\n                  @click=\"publishHot\"\r\n                ></i>\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-chat-dot-round\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"Deepseek深度解读\"\r\n                  @click=\"articleAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n                  >Deepseek深度解读</span\r\n                >\r\n              </p>\r\n              <p class=\"toolTitle\">\r\n                <i\r\n                  class=\"el-icon-pie-chart\"\r\n                  style=\"font-size: 24px\"\r\n                  title=\"生成Deepseek图表看板\"\r\n                  @click=\"chartAiChat\"\r\n                ></i>\r\n                <span class=\"deepseek-text\" @click=\"chartAiChat\"\r\n                  >生成Deepseek图表看板</span\r\n                >\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <el-checkbox\r\n                v-model=\"showSummary\"\r\n                @change=\"(e) => (showSummary = e)\"\r\n                style=\"margin-right: 10px\"\r\n                >是否显示摘要</el-checkbox\r\n              >\r\n              <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n              <el-select v-model=\"queryParams.sortMode\" size=\"mini\">\r\n                <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n                <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n                <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n                <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n                <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n              </el-select>\r\n            </div>\r\n          </div>\r\n          <!-- <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate0\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技无关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate1\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为科技有关</el-button\r\n              >\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                :disabled=\"multiple\"\r\n                @click=\"handleUpdate2\"\r\n                v-hasPermi=\"['wechat:user:remove']\"\r\n                >设置为其他</el-button\r\n              >\r\n            </el-col>\r\n          </el-row> -->\r\n          <el-table\r\n            :data=\"ArticleList\"\r\n            style=\"width: 100%; user-select: text\"\r\n            :show-header=\"false\"\r\n            ref=\"table\"\r\n            :height=\"\r\n              'calc(100vh - ' +\r\n              (374 + (queryParams.tags != '' ? 51 : 0)) +\r\n              'px)'\r\n            \"\r\n            @selection-change=\"handleTableSelectionChange\"\r\n          >\r\n            <el-table-column type=\"selection\" width=\"35\" align=\"center\" />\r\n            <el-table-column width=\"50\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <span style=\"color: #080808; font-size: 15px\">\r\n                  {{\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                    scope.$index +\r\n                    1\r\n                  }}\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column\r\n              label=\"编号\"\r\n              align=\"center\"\r\n              key=\"id\"\r\n              prop=\"id\"\r\n              width=\"100\"\r\n              v-if=\"false\"\r\n            />\r\n            <el-table-column prop=\"title\" label=\"日期\" min-width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span class=\"article_title\" @click=\"openNewView(scope.row)\">\r\n                  <span\r\n                    style=\"color: #080808\"\r\n                    v-html=\"scope.row.title || scope.row.cnTitle\"\r\n                  ></span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ \"(\" + scope.row.publishTime + \")\" }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    {{ scope.row.sourceName }}\r\n                  </span>\r\n                  <span>&nbsp;||&nbsp;</span>\r\n                  <span style=\"color: #5589f5\">\r\n                    大模型筛选:{{ scope.row.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                  </span>\r\n                </span>\r\n                <div\r\n                  class=\"ArticlMain\"\r\n                  style=\"\r\n                    display: -webkit-box;\r\n                    -webkit-box-orient: vertical;\r\n                    -webkit-line-clamp: 2;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    word-break: break-all;\r\n                  \"\r\n                  v-if=\"\r\n                    showSummary &&\r\n                    hasActualContent(scope.row.cnSummary || scope.row.summary)\r\n                  \"\r\n                >\r\n                  <span style=\"color: #9b9b9b\">摘要：</span>\r\n                  <span\r\n                    style=\"color: #4b4b4b\"\r\n                    v-html=\"\r\n                      changeColor(\r\n                        scope.row.cnSummary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        ) ||\r\n                          scope.row.summary.replace(\r\n                            /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                            'span'\r\n                          )\r\n                      )\r\n                    \"\r\n                    @click=\"openNewView(scope.row)\"\r\n                  ></span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column prop=\"publishTime\" label=\"发布时间\" width=\"180\">\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"操作\" fixed=\"right\">\r\n              <template slot-scope=\"scope\">\r\n                <i class=\"icon--_tianjiadaoku\" title=\"添加到报告\" @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"></i>\r\n              </template>\r\n            </el-table-column> -->\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total > 0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"handlePagination\"\r\n            :autoScroll=\"true\"\r\n          />\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"500px\"\r\n      :before-close=\"closeReport\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeReport\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"handleHistoryPagination\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n        :autoScroll=\"true\"\r\n      />\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek图表看板\"\r\n      :visible.sync=\"chartDialogVisible\"\r\n      width=\"1200px\"\r\n      :before-close=\"closeChartDialog\"\r\n      custom-class=\"chart-dialog\"\r\n      destroy-on-close\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div\r\n        v-if=\"chartDialogVisible\"\r\n        class=\"chart-container\"\r\n        v-loading=\"chartLoading\"\r\n        element-loading-text=\"正在生成图表看板...\"\r\n        element-loading-spinner=\"el-icon-loading\"\r\n        element-loading-background=\"rgba(255, 255, 255, 0.8)\"\r\n      >\r\n        <div class=\"chart-content\" ref=\"chartContent\"></div>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"closeChartDialog\">关闭</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listWork,\r\n  getWork,\r\n  delWork,\r\n  addWork,\r\n  updateWork,\r\n} from \"@/api/article/work\";\r\nimport { listKeywords } from \"@/api/article/keywords\";\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n  getListByIds,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport { mapGetters } from \"vuex\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\n\r\nexport default {\r\n  components: { Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableLoading: false, // 表格loading状态\r\n      queryParams: {\r\n        id: 100,\r\n        pageNum: 1,\r\n        pageSize: 50,\r\n        dateType: 4,\r\n        tags: \"\",\r\n        tagsSubset: [],\r\n        keywords: \"\",\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      },\r\n      total: 0,\r\n      treeDataTransfer: [], // 原始树形数据\r\n      filterText: \"\", // 左侧树搜索栏\r\n      checkList: [], // 左侧勾选数据\r\n      ArticleList: [], // 列表数据\r\n      checked: false, // 全选\r\n      ids: [], // 选中的数据\r\n      // 非多个禁用\r\n      multiple: true,\r\n      dialogVisible: false, // 添加到报告弹框\r\n      reportOptions: [], // 报告列表\r\n      reportId: \"\", // 已选择的报告\r\n      tagsList: [], // 检索词库二级列表\r\n      tagsList1: [], // 检索词库一级列表\r\n      checkAll: false, // 检索词库全选\r\n      isIndeterminate: true, // 检索词库选了值\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      showSummary: true,\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n      /* 初始化完成标记 */\r\n      initializationCompleted: false,\r\n      /* 搜索防抖 */\r\n      searchDebounceTimer: null,\r\n      /* 查询防抖 */\r\n      queryDebounceTimer: null,\r\n      /* 防止重复查询 */\r\n      isQuerying: false,\r\n      /* 标记右侧筛选条件是否发生变化 */\r\n      isRightFilter: false,\r\n      /* 标记左侧树是否重置 */\r\n      isLeftReset: false,\r\n      /* 选中的数据源分类 */\r\n      selectedClassify: null,\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n      nodeCheckList: [],\r\n      chartDialogVisible: false,\r\n      chartHtml: \"\",\r\n      chartLoading: true,\r\n      currentChartIframe: null, // 添加变量跟踪当前iframe\r\n      difyApikey: {\r\n        article: \"\",\r\n        chart: \"\",\r\n      },\r\n      chartPrompt: \"\",\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"queryParams.dateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.tags\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n\r\n        // this.queryParams.tagsSubset = [];\r\n        this.checkAll = true;\r\n        this.isIndeterminate = false;\r\n\r\n        if (newVal != \"\") {\r\n          // 不在这里设置tableLoading，让后续的queryArticleList来处理\r\n          listKeywords({ parentId: newVal, pageNum: 1, pageSize: 10 })\r\n            .then((res) => {\r\n              this.tagsList = res.data;\r\n              this.handleCheckAllTagsSubset(true);\r\n              // this.handleRightFilterChange();\r\n            })\r\n            .catch((error) => {\r\n              console.error(\"获取检索词库失败:\", error);\r\n              this.$message.error(\"获取检索词库失败\");\r\n            });\r\n        } else {\r\n          this.handleRightFilterChange();\r\n        }\r\n      },\r\n    },\r\n    \"queryParams.tagsSubset\": {\r\n      handler(newVal, oldVal) {\r\n        if (\r\n          !this.initializationCompleted ||\r\n          JSON.stringify(newVal) === JSON.stringify(oldVal)\r\n        )\r\n          return;\r\n        this.handleRightFilterChange();\r\n      },\r\n    },\r\n    \"queryParams.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      },\r\n    },\r\n    dialogVisible(val) {\r\n      if (val) {\r\n        api.getNewBuilt({ sourceType: \"1\" }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n            this.closeReport();\r\n          }\r\n        });\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"roles\"]),\r\n  },\r\n  async created() {\r\n    getConfigKey(\"sys.ai.platform\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.aiPlatform = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.articleAiPrompt = res.msg;\r\n      }\r\n    });\r\n    getConfigKey(\"wechat.ai.chartPrompt\").then((res) => {\r\n      if (res.code == 200) {\r\n        this.chartPrompt = res.msg;\r\n      }\r\n    });\r\n    // 获取用户头像\r\n    this.userAvatar = this.$store.getters.avatar;\r\n    try {\r\n      // 先加载基础数据\r\n      Promise.all([\r\n        this.getArticleHistory(),\r\n        listKeywords({ parentId: 0, pageNum: 1, pageSize: 10 }).then((res) => {\r\n          this.tagsList1 = res.data.filter((item) => item.parentId == 0);\r\n        }),\r\n      ]);\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      if (this.roles.includes(\"information\")) {\r\n        this.showSummary = false;\r\n      }\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {},\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // 处理右侧筛选条件变化\r\n    handleRightFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handlePagination() {\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 历史记录分页处理\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n      this.$nextTick(() => {\r\n        const dialogContent = document.querySelector(\".el-dialog__body\");\r\n        if (dialogContent) {\r\n          dialogContent.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          platformType: 0,\r\n          id: this.queryParams.id,\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          m: 1,\r\n          dateType:\r\n            this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n          tags: this.queryParams.tags,\r\n          tagsSubset: this.queryParams.tagsSubset,\r\n          keywords: this.queryParams.keywords,\r\n          isTechnology: this.queryParams.isTechnology,\r\n          emotion: this.queryParams.emotion,\r\n          label: this.queryParams.tagsSubset.join(\",\"),\r\n          // 添加关键字过滤参数\r\n          filterwords: this.filterText || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          hasCache: this.queryParams.hasCache,\r\n        };\r\n\r\n        if (!this.queryParams.tags) {\r\n          params.tagsSubset = [];\r\n          params.label = \"\";\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.queryParams.pageNum,\r\n            pageSize: this.queryParams.pageSize,\r\n            id: this.queryParams.id,\r\n            isSort: this.queryParams.sortMode,\r\n            dateType:\r\n              this.queryParams.dateType != 6 ? this.queryParams.dateType : \"\",\r\n            tags: this.queryParams.tags,\r\n            tagsSubset: this.queryParams.tagsSubset,\r\n            keywords: this.queryParams.keywords,\r\n            isTechnology: this.queryParams.isTechnology,\r\n            emotion: this.queryParams.emotion,\r\n            label: this.queryParams.tagsSubset.join(\",\"),\r\n            platformType: 0,\r\n          };\r\n\r\n          if (!this.queryParams.tags) {\r\n            params.tagsSubset = [];\r\n            params.label = \"\";\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 1 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          const res = await api.esRetrieval(params);\r\n\r\n          if (res.code == 200) {\r\n            let articleList = res.data.list\r\n              ? res.data.list.map((item) => {\r\n                  item.cnTitle = item.cnTitle\r\n                    ? this.changeColor(item.cnTitle)\r\n                    : null;\r\n                  item.title = this.changeColor(item.title);\r\n                  return item;\r\n                })\r\n              : [];\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.queryParams.keywords ||\r\n              this.queryParams.keywords.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n            this.total = res.data.total || 0;\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.queryParams.pageSize * (this.queryParams.pageNum - 1) >=\r\n                this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.queryParams.pageNum = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.queryParams.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.queryParams.pageNum = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤文本，避免触发 handleFilterSearch\r\n      this.filterText = \"\";\r\n      this.selectedClassify = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.queryParams.pageNum = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 重置树选择（保留原方法名以兼容）\r\n    treeClear() {\r\n      this.handleReset();\r\n    },\r\n\r\n    // 处理树分页\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库全选处理\r\n    handleCheckAllTagsSubset(val) {\r\n      this.queryParams.tagsSubset = val\r\n        ? this.tagsList.map((item) => item.name)\r\n        : [];\r\n      this.isIndeterminate = false;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 更新过滤文本\r\n      this.filterText = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.queryParams.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 检索词库多选处理\r\n    handleCheckedChange(value) {\r\n      let checkedCount = value.length;\r\n      this.checkAll = checkedCount === this.tagsList.length;\r\n      this.isIndeterminate =\r\n        checkedCount > 0 && checkedCount < this.tagsList.length;\r\n      this.queryParams.pageNum = 1;\r\n\r\n      if (!this.initializationCompleted) return;\r\n\r\n      this.scrollToTopImmediately();\r\n      this.handleRightFilterChange();\r\n    },\r\n\r\n    // 搜索处理\r\n    handleSearch(flag) {\r\n      this.scrollToTopImmediately();\r\n      if (!flag) {\r\n        this.queryParams.pageNum = 1;\r\n      }\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 关键词历史选择\r\n    keywordsChange(item) {\r\n      this.queryParams.keywords = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.queryParams.pageNum = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 右侧表格多选框选中数据\r\n    handleTableSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      if (selection.length == this.ArticleList.length) {\r\n        this.checked = true;\r\n      } else {\r\n        this.checked = false;\r\n      }\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 全选\r\n    handleCheckAllChange(val) {\r\n      if (val) {\r\n        this.$refs[\"table\"].toggleAllSelection();\r\n      } else {\r\n        this.$refs[\"table\"].clearSelection();\r\n      }\r\n    },\r\n    // 打开添加到报告\r\n    openReport() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.dialogVisible = true;\r\n    },\r\n    // 确定添加到报告\r\n    async reportSubmit() {\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      let keyWordList = this.ids.map((item) => {\r\n        return { reportId: this.reportId, listId: item };\r\n      });\r\n      let res = await api.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.queryArticleList();\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.$refs[\"table\"].clearSelection();\r\n      this.checked = false;\r\n      this.closeReport();\r\n    },\r\n    // 关闭添加到报告\r\n    closeReport() {\r\n      this.reportId = \"\";\r\n      this.dialogVisible = false;\r\n    },\r\n    // 批量删除\r\n    batchDelete() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要删除的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.ids.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 添加到台账\r\n    openTaizhang() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要添加的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认添加已勾选的数据项到台账统计?\")\r\n        .then(() => {\r\n          addWork(this.ids).then(() => {\r\n            this.$message({ type: \"success\", message: \"添加成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 发布到每日最新热点\r\n    publishHot() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message({\r\n          message: \"请勾选要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.ids.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.queryArticleList();\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    // 历史记录相关方法\r\n    async removeHistory(item, type) {\r\n      clearTimeout(this.historyTimeout);\r\n      await delArticleHistory([item.id]);\r\n      if (type == 1) {\r\n        this.$refs[\"keywordRef\"].focus();\r\n        this.getArticleHistory();\r\n      } else {\r\n        this.getArticleHistory();\r\n        this.getArticleHistory1();\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      this.showHistory = true;\r\n    },\r\n\r\n    hideHistoryList() {\r\n      this.historyTimeout = setTimeout(() => {\r\n        this.showHistory = false;\r\n      }, 500);\r\n    },\r\n\r\n    getArticleHistory() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ pageNum: 1, pageSize: 5, type: 1 }).then(\r\n        (response) => {\r\n          this.historyList = response.rows;\r\n          this.historyLoading = false;\r\n        }\r\n      );\r\n    },\r\n\r\n    async clearHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.$refs[\"keywordRef\"].focus();\r\n      await cleanArticleHistory(1);\r\n      this.getArticleHistory();\r\n    },\r\n\r\n    moreHistory() {\r\n      clearTimeout(this.historyTimeout);\r\n      this.showHistory = false;\r\n      this.historyLoading = true;\r\n      this.getArticleHistory1();\r\n      this.dialogVisible1 = true;\r\n    },\r\n\r\n    getArticleHistory1() {\r\n      this.historyLoading = true;\r\n      listArticleHistory({ ...this.queryParams1, type: 1 }).then((response) => {\r\n        this.historyList1 = response.rows;\r\n        this.total1 = response.total;\r\n        this.historyLoading = false;\r\n      });\r\n    },\r\n\r\n    // 文章详情\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 检查文本是否有实际内容\r\n    hasActualContent(text) {\r\n      if (!text) return false;\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      if (this.$refs.rightMain) {\r\n        this.$refs.rightMain.scrollTop = 0;\r\n      }\r\n\r\n      if (this.$refs.table) {\r\n        const bodyWrapper = this.$refs.table.$el.querySelector(\r\n          \".el-table__body-wrapper\"\r\n        );\r\n        if (bodyWrapper) {\r\n          bodyWrapper.scrollTop = 0;\r\n        }\r\n      }\r\n    },\r\n\r\n    // 关键字高亮\r\n    changeColor(str) {\r\n      const regex = /<img\\b[^>]*>/gi;\r\n      let Str = str && str.replace(regex, \"\");\r\n      if (\r\n        Str &&\r\n        ((this.queryParams.tags &&\r\n          this.queryParams.tagsSubset &&\r\n          this.queryParams.tagsSubset.length) ||\r\n          this.queryParams.keywords)\r\n      ) {\r\n        let keywords = [\r\n          ...this.queryParams.tagsSubset,\r\n          ...(this.queryParams.keywords\r\n            ? this.queryParams.keywords.split(\",\")\r\n            : []),\r\n        ];\r\n        keywords.forEach((keyitem) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            let replaceString =\r\n              '<span class=\"highlight\" style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n\r\n    // 快照生成\r\n    resultEvent() {\r\n      if (this.ids.length == 0) {\r\n        return this.$message.warning(\"请先选择文章\");\r\n      }\r\n      let ids = this.ids;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (ids.length == 1) {\r\n        let row = this.ArticleList.filter((item) => item.id == ids[0]);\r\n        if (row && row.snapshotUrl) zhuangtai = \"查看\";\r\n        url = row.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        this.$msgbox({\r\n          title: \"提示\",\r\n          message: \"快照正在生成中，请稍后查看\",\r\n          showCancelButton: false,\r\n          confirmButtonText: \"关闭\",\r\n          beforeClose: (_, __, done) => {\r\n            done();\r\n          },\r\n        });\r\n        API.downLoadExportKe(ids)\r\n          .then((response) => {\r\n            if (response.code != 200) {\r\n              this.$message({\r\n                message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                type: \"error\",\r\n              });\r\n            }\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.ids.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.ids.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.ids.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(this.ids.join(\",\"));\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.ids.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.ids.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n    chartAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyChartAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekChartAiChat();\r\n      }\r\n    },\r\n    // dify图表看板\r\n    async difyChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await difyAiQa(\r\n          articleResult.data.content,\r\n          \"blocking\",\r\n          \"dify.chart.apikey\"\r\n        );\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.answer) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = \"\";\r\n\r\n        try {\r\n          // 尝试解析JSON格式（有些返回可能是JSON字符串）\r\n          const parsedData = JSON.parse(aiData.answer);\r\n          content2 =\r\n            parsedData.answer ||\r\n            parsedData.html ||\r\n            parsedData.content ||\r\n            aiData.answer;\r\n        } catch (e) {\r\n          // 如果不是JSON格式，直接使用原始内容\r\n          content2 = aiData.answer;\r\n        }\r\n\r\n        // 处理思考标记\r\n        const thinkStartIndex = content2.indexOf(\"<think>\");\r\n        const thinkEndIndex = content2.indexOf(\"</think>\");\r\n\r\n        // 提取有效内容\r\n        if (thinkStartIndex !== -1 && thinkEndIndex !== -1) {\r\n          // 如果存在思考标记，只取</think>后面的内容\r\n          content2 = content2.substring(thinkEndIndex + 8).trim();\r\n        }\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // deepseek图表看板\r\n    async deepseekChartAiChat() {\r\n      // 参数检查\r\n      if (this.ids.length === 0) {\r\n        this.$message.warning(\"请先选择要解读的文章\");\r\n        return;\r\n      }\r\n\r\n      if (this.ids.length > 1) {\r\n        this.$message.warning(\"生成Deepseek图表看板只能选择一篇内容\");\r\n        return;\r\n      }\r\n\r\n      // 显示对话框与加载状态\r\n      this.chartDialogVisible = true;\r\n      this.chartLoading = true;\r\n\r\n      // 确保清空上次的内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      try {\r\n        // 1. 获取文章内容\r\n        const articleResult = await API.AreaInfo(this.ids[0]);\r\n        if (!articleResult.data || !articleResult.data.content) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        const prompt = this.chartPrompt + `\\n\\n${articleResult.data.content}`;\r\n\r\n        // 2. 调用AI接口\r\n        const aiResult = await deepseekAiQa(prompt, false);\r\n\r\n        if (!aiResult.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        const aiData = await aiResult.json();\r\n        if (!aiData || !aiData.choices) {\r\n          throw new Error(\"AI返回数据格式错误\");\r\n        }\r\n\r\n        // 3. 处理HTML内容\r\n        let content2 = aiData.choices[0].message.content;\r\n\r\n        // 清理html标记和其他特殊字符\r\n        content2 = content2\r\n          // 移除html代码块标记\r\n          .replace(/```html\\s*|```\\s*/g, \"\")\r\n          // 移除可能存在的其他html语言标记，如```json等\r\n          .replace(/```[a-zA-Z]*\\s*/g, \"\")\r\n          // 移除多余的空行\r\n          .replace(/\\n\\s*\\n\\s*\\n/g, \"\\n\\n\")\r\n          // 移除行首行尾空白字符\r\n          .trim();\r\n\r\n        // 确保内容非空\r\n        if (!content2 || content2.length < 10) {\r\n          throw new Error(\"返回的图表内容无效\");\r\n        }\r\n\r\n        // 检查HTML结构的完整性，如果不完整则添加必要的标签\r\n        // 这是为了兼容可能不返回完整HTML的情况\r\n        let finalHtml = content2;\r\n\r\n        // 将各种形式的外部CDN引用替换为本地文件\r\n        // 替换双引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换单引号版本\r\n        finalHtml = finalHtml.replace(\r\n          /\\'https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\'/g,\r\n          \"'/chart.js'\"\r\n        );\r\n        finalHtml = finalHtml.replace(\r\n          /\\\"https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js\\\"/g,\r\n          '\"/chart.js\"'\r\n        );\r\n        // 替换可能带有版本号的引用\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdn\\.jsdelivr\\.net\\/npm\\/chart\\.js@\\d+\\.\\d+\\.\\d+/g,\r\n          \"/chart.js\"\r\n        );\r\n        // 替换其他可能的CDN\r\n        finalHtml = finalHtml.replace(\r\n          /https:\\/\\/cdnjs\\.cloudflare\\.com\\/ajax\\/libs\\/Chart\\.js\\/\\d+\\.\\d+\\.\\d+\\/chart(\\.min)?\\.js/g,\r\n          \"/chart.js\"\r\n        );\r\n\r\n        if (!finalHtml.includes(\"<!DOCTYPE\") && !finalHtml.includes(\"<html\")) {\r\n          // 内容只是HTML片段，需要添加完整结构\r\n          finalHtml =\r\n            \"<!DOCTYPE html>\" +\r\n            \"<html>\" +\r\n            \"<head>\" +\r\n            '  <meta charset=\"UTF-8\">' +\r\n            '  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">' +\r\n            '  <script src=\"/chart.js\"><\\/script>' +\r\n            \"</head>\" +\r\n            \"<body>\" +\r\n            \"  \" +\r\n            content2 +\r\n            \"</body>\" +\r\n            \"</html>\";\r\n        } else if (\r\n          !finalHtml.includes(\"<script\") &&\r\n          finalHtml.includes(\"<canvas\")\r\n        ) {\r\n          // 有canvas但没有script标签，可能缺少Chart.js引用\r\n          finalHtml = finalHtml.replace(\r\n            \"<head>\",\r\n            \"<head>\" + '  <script src=\"/chart.js\"><\\/script>'\r\n          );\r\n        }\r\n\r\n        // 4. 创建iframe并渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.chartContent) {\r\n            // 清理之前的iframe\r\n            if (this.currentChartIframe) {\r\n              try {\r\n                this.currentChartIframe.onload = null;\r\n                this.currentChartIframe.onerror = null;\r\n              } catch (e) {\r\n                console.error(\"清理之前的iframe失败:\", e);\r\n              }\r\n            }\r\n\r\n            // 创建iframe\r\n            const iframe = document.createElement(\"iframe\");\r\n            iframe.style.width = \"100%\";\r\n            iframe.style.height = \"600px\";\r\n            iframe.style.border = \"none\";\r\n            iframe.style.display = \"block\";\r\n            iframe.style.overflow = \"auto\";\r\n\r\n            // 保存iframe引用\r\n            this.currentChartIframe = iframe;\r\n\r\n            // 清空容器并添加iframe\r\n            this.$refs.chartContent.innerHTML = \"\";\r\n            this.$refs.chartContent.appendChild(iframe);\r\n\r\n            // 在iframe加载完成后重新执行脚本并隐藏加载状态\r\n            iframe.onload = () => {\r\n              try {\r\n                // 检查是否需要加载本地Chart.js\r\n                if (\r\n                  !iframe.contentWindow.Chart &&\r\n                  !iframe.contentWindow.document.querySelector(\r\n                    'script[src*=\"chart.js\" i]'\r\n                  )\r\n                ) {\r\n                  // 如果iframe内没有加载Chart.js，手动添加本地Chart.js\r\n                  const chartScript =\r\n                    iframe.contentWindow.document.createElement(\"script\");\r\n                  chartScript.src = \"/chart.js\";\r\n                  iframe.contentWindow.document.head.appendChild(chartScript);\r\n\r\n                  // 等待Chart.js加载完成后再执行后续脚本\r\n                  chartScript.onload = () => {\r\n                    this.executeIframeScripts(iframe);\r\n                  };\r\n\r\n                  // 如果脚本加载失败，也需要隐藏加载动画\r\n                  chartScript.onerror = () => {\r\n                    console.error(\"加载本地Chart.js失败\");\r\n                    this.chartLoading = false;\r\n                  };\r\n                } else {\r\n                  // 直接执行内联脚本\r\n                  this.executeIframeScripts(iframe);\r\n                }\r\n              } catch (e) {\r\n                console.error(\"脚本执行出错:\", e);\r\n                this.chartLoading = false;\r\n              }\r\n            };\r\n\r\n            // 添加错误处理\r\n            iframe.onerror = () => {\r\n              console.error(\"iframe加载失败\");\r\n              this.chartLoading = false;\r\n            };\r\n\r\n            // 写入内容\r\n            const doc = iframe.contentWindow.document;\r\n            doc.open();\r\n            doc.write(finalHtml);\r\n            doc.close();\r\n          }\r\n        });\r\n      } catch (error) {\r\n        console.error(\"生成图表失败:\", error);\r\n        this.$message.error(error.message || \"生成图表失败，请稍后重试\");\r\n        this.closeChartDialog();\r\n      }\r\n    },\r\n    // 关闭图表对话框\r\n    closeChartDialog() {\r\n      this.isAborted = true;\r\n      this.chartDialogVisible = false;\r\n      this.chartHtml = \"\";\r\n      this.chartLoading = false;\r\n      this.isRequesting = false;\r\n\r\n      // 清理Chart实例\r\n      if (this.currentChartIframe && this.currentChartIframe.contentWindow) {\r\n        try {\r\n          // 尝试销毁所有Chart实例\r\n          if (this.currentChartIframe.contentWindow.Chart) {\r\n            const instances =\r\n              this.currentChartIframe.contentWindow.Chart.instances;\r\n            if (instances) {\r\n              Object.values(instances).forEach((instance) => {\r\n                if (instance && typeof instance.destroy === \"function\") {\r\n                  instance.destroy();\r\n                }\r\n              });\r\n            }\r\n          }\r\n        } catch (e) {\r\n          console.error(\"清理Chart实例失败:\", e);\r\n        }\r\n      }\r\n\r\n      // 清空图表容器内容\r\n      if (this.$refs.chartContent) {\r\n        this.$refs.chartContent.innerHTML = \"\";\r\n      }\r\n\r\n      // 清理iframe引用\r\n      if (this.currentChartIframe) {\r\n        try {\r\n          this.currentChartIframe.onload = null;\r\n          this.currentChartIframe.onerror = null;\r\n          this.currentChartIframe = null;\r\n        } catch (e) {\r\n          console.error(\"清理iframe失败:\", e);\r\n        }\r\n      }\r\n    },\r\n    // 执行iframe内的所有内联脚本\r\n    executeIframeScripts(iframe) {\r\n      // 简化后的方法，不再尝试手动执行脚本\r\n      console.log(\"图表iframe已加载，等待自然渲染...\");\r\n\r\n      // 确保所有图表都有机会渲染后再隐藏loading\r\n      setTimeout(() => {\r\n        this.chartLoading = false;\r\n      }, 800);\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.input_Fixed {\r\n  width: 100%;\r\n}\r\n\r\n.treeBox {\r\n  // margin-top:70px;\r\n  width: 100%;\r\n  height: calc(100vh - 178px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.rightMain {\r\n  height: calc(100vh - 60px);\r\n  overflow: hidden;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    // background: #dbdbd8;\r\n    // margin-bottom: 20px;\r\n    height: 60px;\r\n    box-shadow: 0 0px 10px 0px #cecdcd;\r\n    border-bottom: solid 1px #e2e2e2;\r\n\r\n    .TopBtnGroup_left {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .toolTitle {\r\n      margin: 0 10px;\r\n      display: flex;\r\n      align-items: center;\r\n      cursor: pointer;\r\n\r\n      .deepseek-text {\r\n        color: #5589f5; // 使用与图标相同的颜色\r\n        margin-left: 4px;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      &:nth-of-type(1) {\r\n        margin-left: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ArticlMain {\r\n    padding: 0 0 0 30px;\r\n    color: #3f3f3f;\r\n    font-size: 14px;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .ArticlMain > span:hover {\r\n    color: #1889f3;\r\n    border-bottom: solid 1px #0798f8;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n::v-deep .drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n::v-deep .drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-document:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-icon-pie-chart:before {\r\n  color: #5589f5;\r\n}\r\n\r\n::v-deep .el-table td.el-table__cell div {\r\n  padding-left: 10px;\r\n}\r\n\r\n::v-deep .el-table-column--selection .cell {\r\n  padding-right: 0px;\r\n  padding-left: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n::v-deep .el-form-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 0;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 90px;\r\n  line-height: 1;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-table--medium .el-table__cell {\r\n  padding: 10px 0;\r\n}\r\n\r\n.article_title {\r\n  margin-left: 10px;\r\n  font-size: 15px;\r\n}\r\n\r\n.article_title:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n  cursor: pointer;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.chart-container {\r\n  min-height: 600px;\r\n  width: 100%;\r\n  overflow: auto;\r\n  padding: 0;\r\n  position: relative;\r\n\r\n  .chart-content {\r\n    width: 100%;\r\n    height: 600px;\r\n    overflow: auto;\r\n    display: block;\r\n  }\r\n}\r\n\r\n::v-deep .chart-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-dialog__header {\r\n    // padding: 15px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 10px 15px;\r\n    border-top: 1px solid #e4e7ed;\r\n  }\r\n}\r\n\r\n.pagination-container {\r\n  padding: 0 !important;\r\n}\r\n</style>\r\n"]}]}