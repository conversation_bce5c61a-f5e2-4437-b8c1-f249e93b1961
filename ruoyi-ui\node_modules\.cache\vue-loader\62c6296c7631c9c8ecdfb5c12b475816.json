{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue?vue&type=style&index=0&id=c56bca42&scoped=true&lang=scss", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue", "mtime": 1753857010226}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["comparisonChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "comparisonChart.vue", "sourceRoot": "src/views/bigScreenSanhao/secondLevel", "sourcesContent": ["<template>\r\n  <div v-if=\"visible\" class=\"custom-dialog-mask\" @click=\"handleMaskClick\">\r\n    <div\r\n      v-show=\"show\"\r\n      class=\"custom-dialog\"\r\n      :class=\"{ 'comparison-chart-fullscreen': isFullscreen }\"\r\n      :style=\"isFullscreen ? {} : { width: width + 'px' }\"\r\n      @click.stop\r\n    >\r\n      <div class=\"custom-dialog-header\">\r\n        <span>{{ title }}</span>\r\n        <div style=\"display: flex; align-items: center\">\r\n          <div\r\n            @click=\"handleScreen\"\r\n            :title=\"isFullscreen ? '退出全屏' : '全屏'\"\r\n            style=\"\r\n              margin-right: 20px;\r\n              cursor: pointer;\r\n              color: #ffffff;\r\n              font-size: 20px;\r\n            \"\r\n          >\r\n            <i\r\n              :class=\"isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'\"\r\n              style=\"width: 20px; height: 20px\"\r\n            ></i>\r\n          </div>\r\n          <div class=\"custom-dialog-close\" @click=\"closeDialog\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"custom-dialog-body\">\r\n        <div class=\"bg-box\" v-loading=\"loading\">\r\n          <div class=\"bg-box-title\">国内技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total > 0\"\r\n              :total=\"total\"\r\n              :page.sync=\"queryParams.pageNum\"\r\n              :limit.sync=\"queryParams.pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\" v-loading=\"loading1\">\r\n          <div class=\"bg-box-title\">国外技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData1\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams1.pageNum - 1) * queryParams1.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total1 > 0\"\r\n              :total=\"total1\"\r\n              :page.sync=\"queryParams1.pageNum\"\r\n              :limit.sync=\"queryParams1.pageSize\"\r\n              @pagination=\"getList1\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国内热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"1\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国外热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"0\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport baar3DEcharts from \"../components/baar3DEcharts\";\r\nimport { technicalReportList } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"ComparisonChart\",\r\n  components: {\r\n    baar3DEcharts,\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"自定义弹窗\",\r\n    },\r\n    closeOnClickMask: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    width: {\r\n      type: Number,\r\n      default: 1280,\r\n    },\r\n    sccenId: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      reportData: [{ name: \"1\" }],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total: 0,\r\n      reportData1: [{ name: \"1\" }],\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      show: false,\r\n      loading: true,\r\n      loading1: true,\r\n      // 全屏状态\r\n      isFullscreen: false,\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 添加ESC键监听\r\n    document.addEventListener(\"keydown\", this.handleKeydown);\r\n    // 添加窗口大小变化监听\r\n    window.addEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除ESC键监听\r\n    document.removeEventListener(\"keydown\", this.handleKeydown);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  watch: {\r\n    visible: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          // 重置全屏状态\r\n          this.isFullscreen = false;\r\n          this.queryParams = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.queryParams1 = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.show = false;\r\n          this.getList();\r\n          this.getList1();\r\n        }\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      technicalReportList({\r\n        ...this.queryParams,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 1,\r\n      }).then((res) => {\r\n        this.reportData = res.rows;\r\n        this.total = res.total;\r\n        this.show = true;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    getList1() {\r\n      this.loading1 = true;\r\n      technicalReportList({\r\n        ...this.queryParams1,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 0,\r\n      }).then((res) => {\r\n        this.reportData1 = res.rows;\r\n        this.total1 = res.total;\r\n        this.show = true;\r\n        this.loading1 = false;\r\n      });\r\n    },\r\n\r\n    openHotTechnologyDetails(data) {\r\n      this.$emit(\"openHotTechnology\", data);\r\n    },\r\n\r\n    // 关闭弹窗的方法\r\n    closeDialog() {\r\n      this.$emit(\"update:visible\", false);\r\n    },\r\n\r\n    // 处理遮罩层点击事件\r\n    handleMaskClick() {\r\n      if (this.closeOnClickMask) {\r\n        this.closeDialog();\r\n      }\r\n    },\r\n\r\n    // 全屏切换\r\n    handleScreen() {\r\n      this.isFullscreen = !this.isFullscreen;\r\n\r\n      // 延迟调整图表大小，确保DOM更新完成\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          // 如果有图表组件，调整其大小\r\n          // 这里可以根据实际的图表组件引用进行调整\r\n        }, 300); // 等待CSS动画完成\r\n      });\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeydown(event) {\r\n      // 按ESC键退出全屏\r\n      if (event.key === \"Escape\" && this.isFullscreen && this.visible) {\r\n        this.isFullscreen = false;\r\n      }\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleWindowResize() {\r\n      if (this.isFullscreen) {\r\n        // 重新调整图表大小\r\n        // 这里可以根据实际的图表组件引用进行调整\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.custom-dialog-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n\r\n  // 确保在所有分辨率下都能正确覆盖\r\n  min-width: 100%;\r\n  min-height: 100%;\r\n  overflow: hidden;\r\n\r\n  .custom-dialog {\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    width: 500px;\r\n    border: 10px solid;\r\n    border-right-width: 5px;\r\n    border-left-width: 5px;\r\n    border-image: url(\"../../../assets/bigScreenSanhao/dialogBg.png\") 27 round;\r\n    background-color: #000000d0;\r\n    padding-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.comparison-chart-fullscreen {\r\n      width: calc(100vw - 40px) !important;\r\n      height: calc(100vh - 40px) !important;\r\n      max-width: none !important;\r\n      max-height: none !important;\r\n      margin: 0 !important;\r\n      // 确保在所有分辨率下都能正确显示\r\n      min-width: calc(100% - 40px) !important;\r\n      min-height: calc(100% - 40px) !important;\r\n\r\n      .custom-dialog-body {\r\n        height: calc(100% - 80px); // 减去header高度和padding\r\n        max-height: calc(100% - 80px);\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0 20px 0 5%;\r\n      margin: 10px -3px 20px;\r\n      background-image: url(\"../../../assets/bigScreenSanhao/dialogTitle.png\");\r\n      background-size: 100% 100%;\r\n      height: 50px;\r\n      font-weight: 600;\r\n      font-size: 22px;\r\n      color: #ffffff;\r\n      line-height: 50px;\r\n\r\n      span {\r\n        padding-right: 10px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .custom-dialog-close {\r\n        width: 20px;\r\n        height: 20px;\r\n        background-image: url(\"../../../assets/bigScreenSanhao/dialogClose.png\");\r\n        background-size: 100% 100%;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-body {\r\n      height: 800px;\r\n      overflow-y: auto;\r\n      overflow-x: hidden;\r\n      padding: 0px 20px 0px;\r\n\r\n      .bg-box {\r\n        background: #1b283b;\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 8px 16px 16px;\r\n        margin-bottom: 20px;\r\n\r\n        .bg-box-title {\r\n          font-weight: 800;\r\n          font-size: 18px;\r\n          color: #ffffff;\r\n          height: 30px;\r\n          line-height: 30px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .bg-box-content {\r\n          font-size: 16px;\r\n\r\n          .bg-box-content-list {\r\n            padding: 0 0 10px 15px;\r\n            font-size: 14px;\r\n            color: #ffffffc6;\r\n\r\n            span {\r\n              font-weight: 600;\r\n              color: #ffffff;\r\n              margin-right: 10px;\r\n            }\r\n          }\r\n\r\n          ::v-deep .el-table tr {\r\n            background-color: #1f3850ff !important;\r\n          }\r\n\r\n          ::v-deep .el-table__inner-wrapper:before {\r\n            height: 0px;\r\n            border-color: #00000000;\r\n          }\r\n\r\n          ::v-deep .el-table__header th {\r\n            background-color: #1f3850 !important;\r\n            color: rgba(255, 255, 255);\r\n            font-size: 16px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body td {\r\n            background-color: #1d3046;\r\n            color: rgba(255, 255, 255, 0.9);\r\n            font-size: 14px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body tr:hover > td {\r\n            background-color: #132f56 !important;\r\n          }\r\n        }\r\n\r\n        .content-flex {\r\n          display: flex;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .pagination-container {\r\n  background-color: #2a304000;\r\n  color: #f2f2f2;\r\n  height: 55px;\r\n  margin: 0;\r\n\r\n  .el-select .el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n\r\n  .el-pagination__editor.el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n::v-deep .el-pagination__total,\r\n::v-deep .el-pagination__jump {\r\n  color: #f2f2f2;\r\n}\r\n\r\n::v-deep .el-pagination .btn-prev,\r\n::v-deep .el-pagination .btn-next,\r\n::v-deep .el-pagination button:disabled {\r\n  background-color: #ffffff00;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .el-pager li {\r\n  background: #ffffff00;\r\n  color: #fff;\r\n\r\n  &.active {\r\n    color: #1890ff;\r\n  }\r\n}\r\n</style>\r\n"]}]}