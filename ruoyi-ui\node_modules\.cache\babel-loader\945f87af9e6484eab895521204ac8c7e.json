{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1753863308265}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_usaMap", "_interopRequireDefault", "require", "_timeLine", "_graphEcharts", "_technologyArticles", "_trumpViewTree", "_viewTree", "_policyRisk", "_articleDetails", "_suppressionOfRisks", "_enterpriseInformation", "_comparisonChart", "_hotTechnology", "_baarTreeEcharts", "_technologyDetails", "_techBubbleDialog", "_ArticleNotification", "_sanhao", "_index", "_zhiku", "_renwu", "_MarkmapDialog", "_htmlUtils", "_markmapLib", "_markmap<PERSON>iew", "name", "components", "usaMap", "timeLine", "graphEcharts", "technologyArticles", "trumpViewTree", "viewTree", "policyRisk", "articleDetails", "suppressionOfRisks", "enterpriseInformation", "comparisonChart", "hotTechnology", "baarTreeEcharts", "technologyDetails", "techBubbleDialog", "MarkmapDialog", "ArticleNotification", "props", "notificationArticles", "type", "Array", "default", "showNotification", "Boolean", "data", "policyRiskShowModal", "comparisonChartShowModal", "hotTechnologyShowModal", "hotTechnologytTitle", "hotTechnologytID", "baarTreeEchartsShowModal", "baarTreeEchartsType", "technologyDetailsShowModal", "technologyDetailsTitle", "technologyDetailsItem", "suppressionOfRisksShowModal", "enterpriseInformationShowModal", "enterpriseInformationTitle", "articleDetailsShowModal", "articleDetailsTitle", "articleDetailsContent", "articleDetailsContentEn", "suppressListData", "riskBarChartData", "riskEnterpriseList", "riskEnterpriseListTotal", "enterpriseInformationContent", "patentList", "softwareList", "patentTotal", "softwareTotal", "policyRiskList1", "policyRiskList2", "policyRiskList1Total", "usaMapData", "articleDetailsItem", "remengwenzhangList", "scrollTimer", "scrollTimer1", "scrollTimer2", "isHovered", "isHovered1", "isHovered2", "scrollStep", "drawerInfo", "articleDialogVisible", "fontSize", "oriFontSize", "characterViewData", "thinkTankViewData", "remengwenzhangList1", "activeButton", "qianyankejiList", "gatherTotal", "gatherDayNumber", "markmapVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "markmapTitle", "aiLoading", "frontLoginParams", "username", "password", "frontToken", "activeTechButton", "currentTechScreenSn", "techBubbleDialogVisible", "techBubbleDialogTitle", "techBubbleDialogScreenSn", "activeTab", "domainMarkdown", "sinaUrl", "zhikuActive", "computed", "remengwenzhangBoxStyle", "notificationHeight", "height", "concat", "mounted", "_this", "loginTCES", "then", "console", "log", "catch", "error", "loginSINA", "res", "getSuppressData", "initHotList", "initHotList1", "updateScrollbar", "updateScrollbar1", "fetchUsaMapData", "largeGatherQueryGatherData", "<PERSON><PERSON><PERSON><PERSON>", "clearScrollTimer", "clearScrollTimer1", "clearScrollTimer2", "handleMarkmapClose", "methods", "_this2", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "response", "wrap", "_callee$", "_context", "prev", "next", "proposalsCount", "projectSn", "screenSn", "columnSn", "sent", "t0", "stop", "openArticleDetails", "item", "_this3", "technicalArticleDetail", "id", "title", "content", "enContent", "openNewView", "openEnterpriseInformation", "_this4", "suppressPatentList", "suppressSn", "pageNum", "pageSize", "rows", "total", "suppressSoftwareList", "_objectSpread2", "enterpriseName", "patentPagination", "queryParams", "_this5", "softwarePagination", "_this6", "_this7", "suppressData", "Object", "keys", "for<PERSON>ach", "key", "push", "date", "description", "length", "slice", "reverse", "getRiskDetail", "_this8", "suppressEnterpriseList", "map", "index", "suppressLevelCount", "year", "product", "严重", "一般", "较轻", "riskEnterpriseListPagination", "_this9", "getPolicyRiskDetail", "_this10", "proposalsList", "proposalsToChinaData", "policyRiskPagination", "_this11", "openHotTechnology", "reportSn", "openbaarTreeEcharts", "markdownData", "trump", "$refs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "msk", "ws", "openTechnologyDetails", "_this12", "largeHotList2", "$nextTick", "startScroll", "_this13", "handleButtonClick", "startScroll2", "_this14", "wrapper", "scrollWrapper", "scrollContent", "setInterval", "scrollTop", "scrollHeight", "clientHeight", "clearInterval", "handleMouseEnter", "handleMouseLeave", "startScroll1", "_this15", "scrollWrapper1", "scrollContent1", "_this16", "tabOrder", "currentIndex", "indexOf", "nextIndex", "handleMouseEnter1", "handleMouseLeave1", "handleMouseEnter2", "handleMouseLeave2", "scrollPercent", "scrollbarHeight", "Math", "max", "scrollbarTop", "document", "documentElement", "style", "setProperty", "_this17", "_callee2", "_callee2$", "_context2", "largeHotQueryById", "cnTitle", "cnC<PERSON>nt", "formattingJson", "openNewView1", "_this18", "_callee3", "_callee3$", "_context3", "getLargeFTT", "sn", "containsHtmlTags", "replace", "extractHtmlTags", "hasValidHtmlStructure", "handleClose", "increaseFontSize", "decreaseFontSize", "handleNodeClick", "currentCharacter", "rawData", "JSON", "parse", "stringify", "treeData2", "limitLevel3Children", "_this19", "isArray", "children", "_this20", "obj", "脑机接口", "量子信息", "人形机器人", "生成式人工智能", "生物制造", "未来显示", "未来网络", "新型储能", "其他", "kjdtArticleList", "labelSn", "deduplicatedData", "deduplicateArticles", "qykjdtOpenNewTab", "window", "open", "articles", "seen", "Set", "result", "article", "normalizedTitle", "has", "add", "padWithZeros", "num", "targetLength", "numStr", "toString", "padding", "repeat", "openNewTab", "url", "updateHotArticlesList", "_this21", "_callee4", "newArticles", "_callee4$", "_context4", "isArticleDataChanged", "i", "newArticle", "oldArticle", "publishTime", "sourceName", "handleTechButtonClick", "buttonName", "handleTechBubbleDialogClose", "handleNotificationClose", "$emit", "handleViewArticle", "switchTab", "tabName", "markdownType", "_this22", "<PERSON><PERSON><PERSON><PERSON>", "renderMarkmap", "allTypes", "_this23", "_callee5", "svg", "processedContent", "transformer", "_transformer$transfor", "root", "mm", "resize<PERSON><PERSON>ler", "_callee5$", "_context5", "loading", "abrupt", "markmap", "Error", "innerHTML", "Transformer", "transform", "Markmap", "create", "autoFit", "duration", "nodeMinHeight", "spacingVertical", "spacingHorizontal", "paddingX", "color", "node", "colors", "depth", "nodeFont", "fonts", "max<PERSON><PERSON><PERSON>", "initialExpandLevel", "zoom", "pan", "linkShape", "linkWidth", "linkColor", "setTimeout", "fit", "fitRatio", "_mm$state", "state", "minX", "maxX", "minY", "maxY", "width", "containerWidth", "clientWidth", "containerHeight", "scale", "min", "setData", "initialScale", "initialPosition", "addEventListener", "$once", "removeEventListener", "$message", "finish", "openSina"], "sources": ["src/views/bigScreenSanhao/tabOne.vue"], "sourcesContent": ["<template>\r\n  <div style=\"height: 100%; display: flex\" class=\"two\">\r\n    <div class=\"left\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">热点推荐</div>\r\n          <!-- <div class=\"bsContentTitleHelp\"></div> -->\r\n\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('MonitorUse?id=1')\"\r\n            style=\"right: 80px\"\r\n          >\r\n            更多\r\n          </div>\r\n          <div class=\"bsContentTitleMore\" @click=\"openSina\">舆情通</div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <div class=\"remengwenzhang-box\" :style=\"remengwenzhangBoxStyle\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper\"\r\n              @mouseenter=\"handleMouseEnter\"\r\n              @mouseleave=\"handleMouseLeave\"\r\n              @scroll=\"updateScrollbar\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView(item)\"\r\n                >\r\n                  <div\r\n                    class=\"block\"\r\n                    :style=\"{\r\n                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',\r\n                    }\"\r\n                  ></div>\r\n                  <div class=\"title\">{{ item.title }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"scroll-bar\"></div>\r\n          </div>\r\n\r\n          <!-- 文章通知组件 -->\r\n          <article-notification\r\n            ref=\"articleNotification\"\r\n            :articles=\"notificationArticles\"\r\n            :visible=\"showNotification\"\r\n            @close=\"handleNotificationClose\"\r\n            @view-article=\"handleViewArticle\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">政策风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getPolicyRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <usaMap\r\n            style=\"width: 516px; height: 247px\"\r\n            :external-data=\"usaMapData\"\r\n          ></usaMap>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">打压风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <timeLine\r\n            :timelineEvents=\"suppressListData\"\r\n            @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n            style=\"width: 516px; height: 247px\"\r\n          ></timeLine>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <div class=\"center-top\">\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherTotal, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">有效采集量</div>\r\n        </div>\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherDayNumber, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">当日采集数量</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"bsContentBox1\"\r\n        @mouseenter=\"handleMouseEnter2\"\r\n        @mouseleave=\"handleMouseLeave2\"\r\n      >\r\n        <div class=\"bsContentTitle1\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\" style=\"display: flex\">\r\n            <div\r\n              @click=\"zhikuActive = 0\"\r\n              :style=\"{ fontWeight: zhikuActive === 0 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              重点人物分析\r\n            </div>\r\n            <div style=\"margin: 0 4px\">/</div>\r\n            <div\r\n              @click=\"zhikuActive = 1\"\r\n              :style=\"{ fontWeight: zhikuActive === 1 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              产业与技术专题分析\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"bsContentTitleHelp\" ></div> -->\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          style=\"display: flex; flex-direction: column; gap: 8px; padding: 8px\"\r\n        >\r\n          <!-- Tab 切换按钮 -->\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 0\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'trump' }\"\r\n              @click=\"switchTab('trump')\"\r\n            >\r\n              特朗普\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'msk' }\"\r\n              @click=\"switchTab('msk')\"\r\n            >\r\n              埃隆·里夫·马斯克\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'ws' }\"\r\n              @click=\"switchTab('ws')\"\r\n            >\r\n              詹姆斯·唐纳德·万斯\r\n            </div>\r\n          </div>\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 1\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'bdt' }\"\r\n              @click=\"switchTab('bdt', 7)\"\r\n            >\r\n              半导体领域\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'gdzb' }\"\r\n              @click=\"switchTab('gdzb', 8)\"\r\n            >\r\n              高端装备与材料\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'xnyqc' }\"\r\n              @click=\"switchTab('xnyqc', 9)\"\r\n            >\r\n              新能源汽车与电池\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'szhzx' }\"\r\n              @click=\"switchTab('szhzx', 10)\"\r\n            >\r\n              数字化转型与工业软件\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'lszz' }\"\r\n              @click=\"switchTab('lszz', 11)\"\r\n            >\r\n              绿色制造与新能源\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'swyy' }\"\r\n              @click=\"switchTab('swyy', 12)\"\r\n            >\r\n              生物医药与医疗器械\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 人物观点内容（特朗普/马斯克公用） -->\r\n          <div\r\n            v-show=\"\r\n              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <!-- 上方人物观点树状图 -->\r\n            <div class=\"trump-view-container\">\r\n              <trumpViewTree\r\n                ref=\"characterViewTree\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n                style=\"width: 100%; height: 300px\"\r\n                :move=\"isHovered2\"\r\n                :currentCharacter=\"activeTab\"\r\n              >\r\n              </trumpViewTree>\r\n            </div>\r\n            <div class=\"view-tree-container\">\r\n              <viewTree\r\n                :treeData=\"characterViewData\"\r\n                :title=\"'renwu'\"\r\n                :visible=\"\r\n                  activeTab === 'trump' ||\r\n                  activeTab === 'msk' ||\r\n                  activeTab === 'ws'\r\n                \"\r\n                style=\"width: 100%; height: 100%\"\r\n                @openNewView=\"openNewView1\"\r\n                @openbaarTreeEcharts=\"openbaarTreeEcharts()\"\r\n              ></viewTree>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中国制造短板分析内容 -->\r\n          <div\r\n            v-show=\"\r\n              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <svg ref=\"markmap\" class=\"markmap-svg\"></svg>\r\n          </div>\r\n\r\n          <div\r\n            style=\"\"\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openbaarTreeEcharts()\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">前沿科技动态</div>\r\n          <div class=\"bsContentTitleMore\" @click=\"qykjdtOpenNewTab()\">更多</div>\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          @mouseleave=\"handleMouseLeave1\"\r\n          @scroll=\"updateScrollbar1\"\r\n        >\r\n          <div class=\"kejidongtai-box\">\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '脑机接口' }\"\r\n              @click=\"handleButtonClick('脑机接口')\"\r\n            >\r\n              脑机接口\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '量子信息' }\"\r\n              @click=\"handleButtonClick('量子信息')\"\r\n            >\r\n              量子信息\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '人形机器人' }\"\r\n              @click=\"handleButtonClick('人形机器人')\"\r\n            >\r\n              人形机器人\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生成式人工智能' }\"\r\n              @click=\"handleButtonClick('生成式人工智能')\"\r\n            >\r\n              生成式人工智能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生物制造' }\"\r\n              @click=\"handleButtonClick('生物制造')\"\r\n            >\r\n              生物制造\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来显示' }\"\r\n              @click=\"handleButtonClick('未来显示')\"\r\n            >\r\n              未来显示\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来网络' }\"\r\n              @click=\"handleButtonClick('未来网络')\"\r\n            >\r\n              未来网络\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '新型储能' }\"\r\n              @click=\"handleButtonClick('新型储能')\"\r\n            >\r\n              新型储能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '其他' }\"\r\n              @click=\"handleButtonClick('其他')\"\r\n            >\r\n              其他\r\n            </div>\r\n          </div>\r\n          <div class=\"remengwenzhang-box1\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper1\"\r\n              @mouseenter=\"handleMouseEnter1\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent1\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList1\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView1(item)\"\r\n                >\r\n                  <div class=\"block\"></div>\r\n                  <div class=\"title\">{{ item.cnTitle }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"scroll-bar\"></div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox2\" style=\"width: 516px; height: 600px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">国内外前沿热点技术</div>\r\n          <div\r\n            class=\"bsContentTitleHelp\"\r\n            @click=\"comparisonChartShowModal = true\"\r\n          ></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"\r\n              openNewTab(\r\n                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'\r\n              )\r\n            \"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"kejidongtai-box\">\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '11' }\"\r\n            @click=\"handleTechButtonClick('11', '新能源')\"\r\n          >\r\n            新能源\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '12' }\"\r\n            @click=\"handleTechButtonClick('12', '新材料')\"\r\n          >\r\n            新材料\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '13' }\"\r\n            @click=\"handleTechButtonClick('13', '高端装备')\"\r\n          >\r\n            高端装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '14' }\"\r\n            @click=\"handleTechButtonClick('14', '新能源汽车')\"\r\n          >\r\n            新能源汽车\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '17' }\"\r\n            @click=\"handleTechButtonClick('17', '船舶与海洋工程装备')\"\r\n          >\r\n            船舶与海洋工程装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '16' }\"\r\n            @click=\"handleTechButtonClick('16', '民用航空')\"\r\n          >\r\n            民用航空\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '15' }\"\r\n            @click=\"handleTechButtonClick('15', '绿色环保')\"\r\n          >\r\n            绿色环保\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '18' }\"\r\n            @click=\"handleTechButtonClick('18', '新一代信息技术')\"\r\n          >\r\n            新一代信息技术\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"bsContentContent\" style=\"height: 188px\">\r\n          <technologyArticles\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openHotTechnology=\"openHotTechnology\"\r\n          ></technologyArticles>\r\n        </div> -->\r\n        <div class=\"bsContentContent\" style=\"padding-top: 0px; height: 450px\">\r\n          <graphEcharts\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openTechnologyDetails=\"openTechnologyDetails\"\r\n          ></graphEcharts>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <policyRisk\r\n      :visible=\"policyRiskShowModal\"\r\n      :list1=\"policyRiskList1\"\r\n      :list2=\"policyRiskList2\"\r\n      :total=\"policyRiskList1Total\"\r\n      :usa-map-data=\"usaMapData\"\r\n      title=\"美国相关提案\"\r\n      @update:visible=\"policyRiskShowModal = $event\"\r\n      @pagination=\"policyRiskPagination\"\r\n      @openArticleDetail=\"openArticleDetails('policyRisk-news', $event)\"\r\n    >\r\n    </policyRisk>\r\n    <suppressionOfRisks\r\n      :visible=\"suppressionOfRisksShowModal\"\r\n      :levelCount=\"riskBarChartData\"\r\n      :enterpriseList=\"riskEnterpriseList\"\r\n      :total=\"riskEnterpriseListTotal\"\r\n      title=\"打压风险\"\r\n      @update:visible=\"suppressionOfRisksShowModal = $event\"\r\n      @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n      @pagination=\"riskEnterpriseListPagination\"\r\n    >\r\n    </suppressionOfRisks>\r\n    <technologyDetails\r\n      :visible=\"technologyDetailsShowModal\"\r\n      @update:visible=\"technologyDetailsShowModal = $event\"\r\n      @openArticleDetail=\"(e) => openArticleDetails('technology-article', e)\"\r\n      :title=\"technologyDetailsTitle\"\r\n      :item=\"technologyDetailsItem\"\r\n    ></technologyDetails>\r\n    <articleDetails\r\n      :visible=\"articleDetailsShowModal\"\r\n      :title=\"articleDetailsTitle\"\r\n      :content=\"articleDetailsContent\"\r\n      :contentEn=\"articleDetailsContentEn\"\r\n      :item=\"articleDetailsItem\"\r\n      @update:visible=\"articleDetailsShowModal = $event\"\r\n    >\r\n    </articleDetails>\r\n    <enterpriseInformation\r\n      :visible=\"enterpriseInformationShowModal\"\r\n      :title=\"enterpriseInformationTitle\"\r\n      :content=\"enterpriseInformationContent\"\r\n      :patentList=\"patentList\"\r\n      :softwareList=\"softwareList\"\r\n      :total1=\"patentTotal\"\r\n      :total2=\"softwareTotal\"\r\n      @update:visible=\"enterpriseInformationShowModal = $event\"\r\n      @pagination1=\"patentPagination\"\r\n      @pagination2=\"softwarePagination\"\r\n      @openArticleDetail=\"\r\n        (e) => openArticleDetails('enterpriseInformation-news', e)\r\n      \"\r\n    >\r\n    </enterpriseInformation>\r\n    <comparisonChart\r\n      :visible=\"comparisonChartShowModal\"\r\n      @update:visible=\"comparisonChartShowModal = $event\"\r\n      @openHotTechnology=\"openHotTechnology\"\r\n      title=\"前沿技术热点对比图详情\"\r\n    ></comparisonChart>\r\n    <hotTechnology\r\n      :visible=\"hotTechnologyShowModal\"\r\n      :title=\"hotTechnologytTitle\"\r\n      :id=\"hotTechnologytID\"\r\n      @update:visible=\"hotTechnologyShowModal = $event\"\r\n    ></hotTechnology>\r\n    <baarTreeEcharts\r\n      :visible=\"baarTreeEchartsShowModal\"\r\n      :type=\"baarTreeEchartsType\"\r\n      title=\"智库观点\"\r\n      @update:visible=\"baarTreeEchartsShowModal = $event\"\r\n      @openNewView=\"openNewView1\"\r\n    ></baarTreeEcharts>\r\n\r\n    <!-- 热点推荐文章详情弹窗 -->\r\n    <el-dialog\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"articleDialogVisible\"\r\n      width=\"65%\"\r\n      append-to-body\r\n      :before-close=\"handleClose\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"fz\">\r\n        <div class=\"text\">字号：</div>\r\n        <div class=\"btns\">\r\n          <div class=\"btn-minus\" @click=\"decreaseFontSize\">-</div>\r\n          <div class=\"font-size\">{{ fontSize }}px</div>\r\n          <div class=\"btn-plus\" @click=\"increaseFontSize\">+</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"dialog-art\"\r\n        :style=\"{ fontSize: fontSize + 'px' }\"\r\n        v-html=\"drawerInfo.cnContent\"\r\n      ></div>\r\n      <el-empty\r\n        description=\"当前文章暂无数据\"\r\n        v-if=\"!drawerInfo.cnContent\"\r\n      ></el-empty>\r\n    </el-dialog>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"aiLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n\r\n    <!-- 技术领域泡泡图弹窗 -->\r\n    <techBubbleDialog\r\n      :visible=\"techBubbleDialogVisible\"\r\n      :title=\"techBubbleDialogTitle\"\r\n      :screenSn=\"techBubbleDialogScreenSn\"\r\n      @update:visible=\"techBubbleDialogVisible = $event\"\r\n      @openTechnologyDetails=\"openTechnologyDetails\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport usaMap from \"./components/usaMap\";\r\nimport timeLine from \"./components/timeLine\";\r\nimport graphEcharts from \"./components/graphEcharts\";\r\nimport technologyArticles from \"./components/technologyArticles\";\r\nimport trumpViewTree from \"./components/trumpViewTree\";\r\nimport viewTree from \"./components/viewTree\";\r\nimport policyRisk from \"./secondLevel/policyRisk\";\r\nimport articleDetails from \"./secondLevel/articleDetails\";\r\nimport suppressionOfRisks from \"./secondLevel/suppressionOfRisks\";\r\nimport enterpriseInformation from \"./secondLevel/enterpriseInformation\";\r\nimport comparisonChart from \"./secondLevel/comparisonChart\";\r\nimport hotTechnology from \"./secondLevel/hotTechnology\";\r\nimport baarTreeEcharts from \"./components/baarTreeEcharts\";\r\nimport technologyDetails from \"./secondLevel/technologyDetails\";\r\nimport techBubbleDialog from \"./secondLevel/techBubbleDialog\";\r\nimport ArticleNotification from \"@/components/ArticleNotification\";\r\nimport {\r\n  technicalArticleDetail,\r\n  suppressData,\r\n  suppressLevelCount,\r\n  suppressEnterpriseList,\r\n  suppressPatentList,\r\n  suppressSoftwareList,\r\n  proposalsList,\r\n  proposalsToChinaData,\r\n  proposalsCount,\r\n  kjdtArticleList,\r\n  loginTCES,\r\n  loginSINA,\r\n} from \"@/api/bigScreen/sanhao.js\";\r\nimport {\r\n  largeHotQueryById,\r\n  largeGatherQueryGatherData,\r\n  getLargeFTT,\r\n  largeHotList2,\r\n} from \"@/api/bigScreen/index1\";\r\nimport { markObj } from \"./data/zhiku.js\";\r\nimport { treeData2, markdownData } from \"./data/renwu.js\";\r\nimport MarkmapDialog from \"../bigScreenThree/components/MarkmapDialog.vue\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { Transformer } from \"markmap-lib\";\r\nimport { Markmap } from \"markmap-view\";\r\n\r\nexport default {\r\n  name: \"TabOne\",\r\n  components: {\r\n    usaMap,\r\n    timeLine,\r\n    graphEcharts,\r\n    technologyArticles,\r\n    trumpViewTree,\r\n    viewTree,\r\n    policyRisk,\r\n    articleDetails,\r\n    suppressionOfRisks,\r\n    enterpriseInformation,\r\n    comparisonChart,\r\n    hotTechnology,\r\n    baarTreeEcharts,\r\n    technologyDetails,\r\n    techBubbleDialog,\r\n    MarkmapDialog,\r\n    ArticleNotification,\r\n  },\r\n  props: {\r\n    notificationArticles: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    showNotification: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      policyRiskShowModal: false,\r\n      comparisonChartShowModal: false,\r\n      hotTechnologyShowModal: false,\r\n      hotTechnologytTitle: \"\",\r\n      hotTechnologytID: null,\r\n      baarTreeEchartsShowModal: false,\r\n      baarTreeEchartsType: null,\r\n      technologyDetailsShowModal: false,\r\n      technologyDetailsTitle: \"\",\r\n      technologyDetailsItem: null,\r\n      suppressionOfRisksShowModal: false,\r\n      enterpriseInformationShowModal: false,\r\n      enterpriseInformationTitle: \"\",\r\n      articleDetailsShowModal: false,\r\n      articleDetailsTitle: \"\",\r\n      articleDetailsContent: \"\",\r\n      articleDetailsContentEn: \"\",\r\n      suppressListData: [],\r\n      riskBarChartData: [],\r\n      riskEnterpriseList: [],\r\n      riskEnterpriseListTotal: 0,\r\n      enterpriseInformationContent: {},\r\n      patentList: [],\r\n      softwareList: [],\r\n      patentTotal: 0,\r\n      softwareTotal: 0,\r\n      policyRiskList1: [],\r\n      policyRiskList2: [],\r\n      policyRiskList1Total: 0,\r\n      // 美国地图数据\r\n      usaMapData: null,\r\n      articleDetailsItem: {},\r\n      // 热点推荐相关数据\r\n      remengwenzhangList: [],\r\n      scrollTimer: null,\r\n      scrollTimer1: null,\r\n      scrollTimer2: null,\r\n      isHovered: false,\r\n      isHovered1: false,\r\n      isHovered2: false,\r\n      scrollStep: 1,\r\n      drawerInfo: {},\r\n      articleDialogVisible: false,\r\n      fontSize: 16,\r\n      oriFontSize: 20,\r\n      // 人物观点数据\r\n      characterViewData: [],\r\n      // 智库观点数据\r\n      thinkTankViewData: [],\r\n      remengwenzhangList1: [],\r\n      activeButton: null,\r\n      qianyankejiList: [],\r\n      gatherTotal: 0,\r\n      gatherDayNumber: 0,\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"智库观点\",\r\n      aiLoading: false,\r\n      frontLoginParams: {\r\n        username: \"guanliyuan\",\r\n        password: \"123456\",\r\n      },\r\n      frontToken: \"\",\r\n      // 技术领域相关\r\n      activeTechButton: \"11\", // 默认选中新能源\r\n      currentTechScreenSn: \"11\", // 当前技术领域的screenSn\r\n      // 技术领域泡泡图弹窗相关\r\n      techBubbleDialogVisible: false,\r\n      techBubbleDialogTitle: \"\",\r\n      techBubbleDialogScreenSn: \"\",\r\n      // 智库观点tab切换\r\n      activeTab: \"trump\", // 默认显示特朗普tab\r\n      domainMarkdown: \"\",\r\n      sinaUrl: \"\",\r\n      zhikuActive: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算热门文章列表框的样式\r\n    remengwenzhangBoxStyle() {\r\n      const notificationHeight = 110; // 通知组件的高度\r\n\r\n      if (this.showNotification) {\r\n        return {\r\n          height: `calc(100% - ${notificationHeight}px)`,\r\n        };\r\n      } else {\r\n        return {\r\n          height: `100%`,\r\n        };\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    // 调用登录TCES接口\r\n    loginTCES()\r\n      .then(() => {\r\n        console.log(\"TCES登录成功\");\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"TCES登录失败:\", error);\r\n      });\r\n\r\n    // 调用登录新浪接口\r\n    loginSINA()\r\n      .then((res) => {\r\n        console.log(\"新浪登录成功\");\r\n        this.sinaUrl = res;\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"新浪登录失败:\", error);\r\n      });\r\n\r\n    this.getSuppressData();\r\n    this.initHotList();\r\n    this.initHotList1();\r\n    this.updateScrollbar();\r\n    this.updateScrollbar1();\r\n    this.fetchUsaMapData();\r\n    largeGatherQueryGatherData({}).then((res) => {\r\n      this.gatherTotal = res.data.gatherTotal;\r\n      this.gatherDayNumber = res.data.gatherDayNumber;\r\n    });\r\n  },\r\n  beforeDestroy() {\r\n    this.clearScrollTimer();\r\n    this.clearScrollTimer1();\r\n    this.clearScrollTimer2();\r\n    this.handleMarkmapClose();\r\n  },\r\n  methods: {\r\n    // 获取美国地图数据\r\n    async fetchUsaMapData() {\r\n      try {\r\n        const response = await proposalsCount({\r\n          projectSn: \"1\",\r\n          screenSn: \"1\",\r\n          columnSn: \"1\",\r\n        });\r\n        this.usaMapData = response;\r\n      } catch (error) {\r\n        console.error(\"获取美国地图数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    openArticleDetails(type, item) {\r\n      this.articleDetailsItem = item;\r\n      switch (type) {\r\n        case \"technology-article\":\r\n          technicalArticleDetail({ id: item.id }).then((res) => {\r\n            this.articleDetailsTitle = item.title;\r\n            this.articleDetailsContent = res.data.content;\r\n            this.articleDetailsContentEn = res.data.enContent;\r\n            this.articleDetailsShowModal = true;\r\n          });\r\n          break;\r\n        case \"enterpriseInformation-news\":\r\n          this.openNewView(item);\r\n          break;\r\n        case \"policyRisk-news\":\r\n          this.openNewView(item);\r\n          break;\r\n      }\r\n    },\r\n    openEnterpriseInformation(item) {\r\n      suppressPatentList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n        this.patentTotal = res.total;\r\n      });\r\n      suppressSoftwareList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n        this.softwareTotal = res.total;\r\n      });\r\n      this.enterpriseInformationContent = { ...item };\r\n      this.enterpriseInformationTitle = item.enterpriseName;\r\n      this.enterpriseInformationShowModal = true;\r\n    },\r\n\r\n    patentPagination(suppressSn, queryParams) {\r\n      suppressPatentList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n      });\r\n    },\r\n\r\n    softwarePagination(suppressSn, queryParams) {\r\n      suppressSoftwareList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n      });\r\n    },\r\n\r\n    getSuppressData() {\r\n      suppressData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        let data = [];\r\n        Object.keys(res.data).forEach((key) => {\r\n          data.push({\r\n            date: key,\r\n            description:\r\n              res.data[key].length <= 3\r\n                ? res.data[key]\r\n                : res.data[key].slice(\r\n                    res.data[key].length - 3,\r\n                    res.data[key].length\r\n                  ),\r\n          });\r\n        });\r\n        this.suppressListData = data.reverse();\r\n      });\r\n    },\r\n\r\n    getRiskDetail() {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n        this.riskEnterpriseListTotal = res.total;\r\n      });\r\n      suppressLevelCount({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        // 将对象格式转换为数组格式\r\n        const data = Object.keys(res.data).map((year) => ({\r\n          product: year,\r\n          严重: res.data[year].严重,\r\n          一般: res.data[year].一般,\r\n          较轻: res.data[year].较轻,\r\n        }));\r\n        this.riskBarChartData = data;\r\n        this.suppressionOfRisksShowModal = true;\r\n      });\r\n    },\r\n\r\n    riskEnterpriseListPagination(queryParams) {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n      });\r\n    },\r\n\r\n    getPolicyRiskDetail() {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n        this.policyRiskList1Total = res.total;\r\n      });\r\n      proposalsToChinaData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        this.policyRiskList2 = res.data;\r\n        this.policyRiskShowModal = true;\r\n      });\r\n    },\r\n\r\n    policyRiskPagination(queryParams) {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n      });\r\n    },\r\n\r\n    openHotTechnology(data) {\r\n      this.hotTechnologyShowModal = true;\r\n      this.hotTechnologytTitle = data.title;\r\n      this.hotTechnologytID = data.reportSn;\r\n    },\r\n    openbaarTreeEcharts(data) {\r\n      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\\D+/g, ''), 10)\r\n      // this.baarTreeEchartsShowModal = true;\r\n\r\n      // 根据当前选中的tab来决定使用哪个markObj\r\n      if (this.activeTab === \"trump\") {\r\n        // 使用导入的markdownData\r\n        this.markmapContent =\r\n          markdownData.trump[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"特朗普\";\r\n      } else if (this.activeTab === \"msk\") {\r\n        // 马斯克的markdownData\r\n        this.markmapContent =\r\n          markdownData.msk[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"埃隆·里夫·马斯克\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        // 万斯的markdownData\r\n        this.markmapContent =\r\n          markdownData.ws[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"詹姆斯·唐纳德·万斯\";\r\n      } else {\r\n        this.markmapContent = this.domainMarkdown;\r\n        switch (this.activeTab) {\r\n          case \"bdt\":\r\n            this.markmapTitle = \"半导体领域\";\r\n            break;\r\n          case \"gdzb\":\r\n            this.markmapTitle = \"高端装备与材料\";\r\n            break;\r\n          case \"xnyqc\":\r\n            this.markmapTitle = \"新能源汽车与电池\";\r\n            break;\r\n          case \"szhzx\":\r\n            this.markmapTitle = \"数字化转型与工业软件\";\r\n            break;\r\n          case \"lszz\":\r\n            this.markmapTitle = \"绿色制造与新能源\";\r\n            break;\r\n          case \"swyy\":\r\n            this.markmapTitle = \"生物医药与医疗器械\";\r\n            break;\r\n        }\r\n      }\r\n      this.aiLoading = false;\r\n      this.markmapVisible = true;\r\n    },\r\n\r\n    openTechnologyDetails(data) {\r\n      this.technologyDetailsShowModal = true;\r\n      this.technologyDetailsTitle = data.name;\r\n      this.technologyDetailsItem = data.data;\r\n    },\r\n\r\n    // 热点推荐相关方法\r\n    initHotList() {\r\n      // 使用bigScreenThree相同的API接口\r\n      largeHotList2()\r\n        .then((res) => {\r\n          this.remengwenzhangList = res.data || [];\r\n          this.$nextTick(() => {\r\n            this.startScroll();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取热点推荐数据失败:\", error);\r\n          // 如果API调用失败，使用空数组\r\n          this.remengwenzhangList = [];\r\n        });\r\n    },\r\n    initHotList1() {\r\n      this.handleButtonClick(\"脑机接口\");\r\n      this.$nextTick(() => {\r\n        this.startScroll2();\r\n      });\r\n    },\r\n\r\n    startScroll() {\r\n      this.clearScrollTimer();\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      const content = this.$refs.scrollContent;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer = setInterval(() => {\r\n        if (this.isHovered) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar();\r\n      }, 40);\r\n    },\r\n\r\n    clearScrollTimer() {\r\n      if (this.scrollTimer) {\r\n        clearInterval(this.scrollTimer);\r\n        this.scrollTimer = null;\r\n      }\r\n    },\r\n\r\n    handleMouseEnter() {\r\n      this.isHovered = true;\r\n    },\r\n\r\n    handleMouseLeave() {\r\n      this.isHovered = false;\r\n      this.startScroll();\r\n    },\r\n    startScroll1() {\r\n      this.clearScrollTimer1();\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      const content = this.$refs.scrollContent1;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer1 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar1();\r\n      }, 20);\r\n    },\r\n    startScroll2() {\r\n      this.clearScrollTimer2();\r\n      this.scrollTimer2 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        // 定义所有tab标签的顺序\r\n        const tabOrder = [\r\n          \"脑机接口\",\r\n          \"量子信息\",\r\n          \"人形机器人\",\r\n          \"生成式人工智能\",\r\n          \"生物制造\",\r\n          \"未来显示\",\r\n          \"未来网络\",\r\n          \"新型储能\",\r\n          \"其他\",\r\n        ];\r\n\r\n        // 找到当前活跃标签的索引\r\n        const currentIndex = tabOrder.indexOf(this.activeButton);\r\n        // 计算下一个标签的索引，如果到最后一个则回到第一个\r\n        const nextIndex = (currentIndex + 1) % tabOrder.length;\r\n        // 切换到下一个标签\r\n        this.handleButtonClick(tabOrder[nextIndex]);\r\n      }, 8000);\r\n    },\r\n    clearScrollTimer1() {\r\n      if (this.scrollTimer1) {\r\n        clearInterval(this.scrollTimer1);\r\n        this.scrollTimer1 = null;\r\n      }\r\n    },\r\n    clearScrollTimer2() {\r\n      if (this.scrollTimer2) {\r\n        clearInterval(this.scrollTimer2);\r\n        this.scrollTimer2 = null;\r\n      }\r\n    },\r\n    handleMouseEnter1() {\r\n      this.isHovered1 = true;\r\n    },\r\n\r\n    handleMouseLeave1() {\r\n      this.isHovered1 = false;\r\n      // this.startScroll1();\r\n      // this.startScroll2();\r\n    },\r\n    handleMouseEnter2() {\r\n      this.isHovered2 = true;\r\n    },\r\n\r\n    handleMouseLeave2() {\r\n      this.isHovered2 = false;\r\n    },\r\n    updateScrollbar() {\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n    updateScrollbar1() {\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n\r\n    async openNewView(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await largeHotQueryById(item.id);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    async openNewView1(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await getLargeFTT(item.sn);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          // content = content.replace(/\\${[^}]+}/g, \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          // 移除带样式的标签，保留内容\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n\r\n    handleClose() {\r\n      this.drawerInfo = {};\r\n      this.articleDialogVisible = false;\r\n    },\r\n\r\n    increaseFontSize() {\r\n      if (this.fontSize < 30) {\r\n        this.fontSize += 2;\r\n      }\r\n    },\r\n\r\n    decreaseFontSize() {\r\n      if (this.fontSize > 16) {\r\n        this.fontSize -= 2;\r\n      }\r\n    },\r\n    handleNodeClick(type) {\r\n      // 根据当前activeTab获取对应人物的数据\r\n      let currentCharacter = \"trump\"; // 默认特朗普\r\n      if (this.activeTab === \"msk\") {\r\n        currentCharacter = \"msk\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        currentCharacter = \"ws\";\r\n      }\r\n      let rawData = JSON.parse(\r\n        JSON.stringify(treeData2[currentCharacter][type] || [])\r\n      );\r\n      this.characterViewData = this.limitLevel3Children(rawData);\r\n    },\r\n    limitLevel3Children(data) {\r\n      if (!data || !Array.isArray(data)) return data;\r\n      return data.map((item) => {\r\n        if (\r\n          (item.type == \"level2-1\" ||\r\n            item.type == \"level2-2\" ||\r\n            item.type == \"level2-3\") &&\r\n          Array.isArray(item.children)\r\n        ) {\r\n          item.children = item.children.slice(0, 2); // 只保留前两个\r\n        }\r\n\r\n        if (item.children) {\r\n          item.children = this.limitLevel3Children(item.children);\r\n        }\r\n\r\n        return item;\r\n      });\r\n    },\r\n    handleButtonClick(type) {\r\n      let obj = {\r\n        脑机接口: \"3\",\r\n        量子信息: \"4\",\r\n        人形机器人: \"6\",\r\n        生成式人工智能: \"1\",\r\n        生物制造: \"7\",\r\n        未来显示: \"8\",\r\n        未来网络: \"9\",\r\n        新型储能: \"10\",\r\n        其他: \"2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      this.activeButton = type;\r\n\r\n      // 重置轮播时间\r\n      this.startScroll2();\r\n\r\n      kjdtArticleList({\r\n        labelSn: obj[type],\r\n      }).then((res) => {\r\n        // 对数据进行去重处理，基于cnTitle去除空格后判断\r\n        const deduplicatedData = this.deduplicateArticles(res || []);\r\n        this.remengwenzhangList1 = deduplicatedData;\r\n        this.$nextTick(() => {\r\n          const wrapper = this.$refs.scrollWrapper1;\r\n          wrapper.scrollTop = 0;\r\n        });\r\n      });\r\n    },\r\n    qykjdtOpenNewTab() {\r\n      let obj = {\r\n        脑机接口: \"/qianyankejidongtai/naojijiekou?id=1&domain=3\",\r\n        量子信息: \"/qianyankejidongtai/liangzixinxi?id=1&domain=4\",\r\n        人形机器人: \"/qianyankejidongtai/renxingjiqiren?id=1&domain=6\",\r\n        生成式人工智能: \"/qianyankejidongtai/rengongzhineng?id=1&domain=1\",\r\n        生物制造: \"/qianyankejidongtai/shengwuzhizao?id=1&domain=7\",\r\n        未来显示: \"/qianyankejidongtai/weilaixianshi?id=1&domain=8\",\r\n        未来网络: \"/qianyankejidongtai/weilaiwangluo?id=1&domain=9\",\r\n        新型储能: \"/qianyankejidongtai/xinxingchuneng?id=1&domain=10\",\r\n        其他: \"/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      window.open(obj[this.activeButton], \"_blank\");\r\n    },\r\n    // 文章去重方法，基于cnTitle去除空格后判断\r\n    deduplicateArticles(articles) {\r\n      if (!Array.isArray(articles)) {\r\n        return [];\r\n      }\r\n\r\n      const seen = new Set();\r\n      const result = [];\r\n\r\n      articles.forEach((article) => {\r\n        if (article && article.cnTitle) {\r\n          // 去除cnTitle中的所有空格\r\n          const normalizedTitle = article.cnTitle.replace(/\\s+/g, \"\");\r\n\r\n          if (!seen.has(normalizedTitle)) {\r\n            seen.add(normalizedTitle);\r\n            result.push(article);\r\n          }\r\n        } else {\r\n          // 如果没有cnTitle，也保留这条记录\r\n          result.push(article);\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    padWithZeros(num, targetLength) {\r\n      const numStr = num.toString();\r\n      const padding = \"0\".repeat(targetLength - numStr.length);\r\n      return `${padding}${numStr}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    openNewTab(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    handleMarkmapClose() {\r\n      this.markmapContent = \"\";\r\n      this.aiLoading = false;\r\n      this.markmapVisible = false;\r\n    },\r\n\r\n    // 更新热点推荐文章列表\r\n    async updateHotArticlesList() {\r\n      try {\r\n        const response = await largeHotList2();\r\n        if (response && response.data) {\r\n          const newArticles = response.data;\r\n          // 对比数据是否一致\r\n          if (this.isArticleDataChanged(newArticles)) {\r\n            this.remengwenzhangList = newArticles;\r\n          } else {\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"更新热点推荐文章列表失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 检查文章数据是否发生变化\r\n    isArticleDataChanged(newArticles) {\r\n      // 如果当前列表为空，直接返回true\r\n      if (this.remengwenzhangList.length === 0) {\r\n        return newArticles.length > 0;\r\n      }\r\n\r\n      // 如果数量不同，说明有变化\r\n      if (this.remengwenzhangList.length !== newArticles.length) {\r\n        return true;\r\n      }\r\n\r\n      // 对比每篇文章的关键信息\r\n      for (let i = 0; i < newArticles.length; i++) {\r\n        const newArticle = newArticles[i];\r\n        const oldArticle = this.remengwenzhangList[i];\r\n\r\n        // 对比文章ID、标题、发布时间等关键字段\r\n        if (\r\n          newArticle.id !== oldArticle.id ||\r\n          newArticle.title !== oldArticle.title ||\r\n          newArticle.publishTime !== oldArticle.publishTime ||\r\n          newArticle.sourceName !== oldArticle.sourceName\r\n        ) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 所有数据都一致\r\n      return false;\r\n    },\r\n\r\n    // 处理技术领域按钮点击\r\n    handleTechButtonClick(screenSn, buttonName) {\r\n      console.log(\"切换技术领域:\", buttonName, \"screenSn:\", screenSn);\r\n      this.activeTechButton = screenSn;\r\n      this.currentTechScreenSn = screenSn;\r\n      // 弹出技术领域泡泡图弹窗\r\n      this.techBubbleDialogVisible = true;\r\n      this.techBubbleDialogTitle = buttonName;\r\n      this.techBubbleDialogScreenSn = screenSn;\r\n\r\n      // 通知子组件更新数据\r\n      this.$nextTick(() => {\r\n        // 可以通过ref直接调用子组件的方法来刷新数据\r\n        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新\r\n      });\r\n    },\r\n\r\n    // 关闭技术领域泡泡图弹窗\r\n    handleTechBubbleDialogClose() {\r\n      this.techBubbleDialogVisible = false;\r\n      this.techBubbleDialogTitle = \"\";\r\n      this.techBubbleDialogScreenSn = \"\";\r\n    },\r\n\r\n    // 处理通知关闭\r\n    handleNotificationClose() {\r\n      this.$emit(\"notification-close\");\r\n    },\r\n\r\n    // 处理查看单篇文章\r\n    handleViewArticle(article) {\r\n      this.$emit(\"notification-view-article\", article);\r\n    },\r\n\r\n    // 切换智库观点tab\r\n    switchTab(tabName, markdownType) {\r\n      if (markdownType) {\r\n        this.activeTab = tabName;\r\n        this.domainMarkdown = markObj[\"type\" + markdownType];\r\n        this.renderMarkmap();\r\n      } else if (tabName === \"trump\" || tabName === \"msk\" || tabName === \"ws\") {\r\n        // 如果点击的是当前已激活的人物tab，需要重新触发数据加载\r\n        if (this.activeTab === tabName) {\r\n          // 清空数据\r\n          this.characterViewData = [];\r\n          // 通过ref直接调用trumpViewTree组件的方法来重新初始化\r\n          this.$nextTick(() => {\r\n            if (\r\n              this.$refs.characterViewTree &&\r\n              this.$refs.characterViewTree.allTypes.length > 0\r\n            ) {\r\n              this.$refs.characterViewTree.handleNodeClick(\r\n                this.$refs.characterViewTree.allTypes[0]\r\n              );\r\n            }\r\n          });\r\n        } else {\r\n          // 切换到不同的人物tab时，设置activeTab，让watcher自动处理\r\n          this.activeTab = tabName;\r\n          this.characterViewData = [];\r\n        }\r\n      }\r\n    },\r\n    async renderMarkmap() {\r\n      if (!this.domainMarkdown) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$nextTick();\r\n        const svg = this.$refs.markmap;\r\n        if (!svg) {\r\n          throw new Error(\"SVG element not found\");\r\n        }\r\n\r\n        // 清空之前的内容\r\n        svg.innerHTML = \"\";\r\n\r\n        // 处理内容，移除 markdown 标记\r\n        let processedContent = this.domainMarkdown\r\n          .replace(/^```markdown\\s*/i, \"\") // 移除开头的 ```markdown\r\n          .replace(/\\s*```\\s*$/, \"\"); // 移除结尾的 ```\r\n\r\n        const transformer = new Transformer();\r\n        const { root } = transformer.transform(processedContent);\r\n\r\n        // 创建思维导图\r\n        const mm = Markmap.create(\r\n          svg,\r\n          {\r\n            autoFit: true,\r\n            duration: 0,\r\n            nodeMinHeight: 20,\r\n            spacingVertical: 10,\r\n            spacingHorizontal: 100,\r\n            paddingX: 20,\r\n            color: (node) => {\r\n              const colors = {\r\n                0: \"#0052ff\", // 亮蓝色\r\n                1: \"#009600\", // 亮绿色\r\n                2: \"#ff6600\", // 亮橙色\r\n                3: \"#8000ff\", // 亮紫色\r\n                4: \"#ff0066\", // 亮粉色\r\n              };\r\n              return colors[node.depth] || \"#0052ff\";\r\n            },\r\n            nodeFont: (node) => {\r\n              const fonts = {\r\n                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n              };\r\n              return (\r\n                fonts[node.depth] ||\r\n                '400 14px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto'\r\n              );\r\n            },\r\n            maxWidth: 400,\r\n            initialExpandLevel: -1,\r\n            zoom: true,\r\n            pan: true,\r\n            linkShape: \"diagonal\",\r\n            linkWidth: (node) => 2.5 - node.depth * 0.5,\r\n            linkColor: (node) => {\r\n              const colors = {\r\n                0: \"rgba(0, 82, 255, 0.8)\", // 亮蓝色\r\n                1: \"rgba(0, 150, 0, 0.8)\", // 亮绿色\r\n                2: \"rgba(255, 102, 0, 0.8)\", // 亮橙色\r\n              };\r\n              return colors[node.depth] || \"rgba(128, 0, 255, 0.8)\";\r\n            },\r\n          },\r\n          root\r\n        );\r\n\r\n        // 修改初始化动画部分\r\n        setTimeout(() => {\r\n          mm.fit(); // 适应视图大小\r\n\r\n          // 重新设置数据以触发重绘\r\n          const fitRatio = 0.95; // 留出一些边距\r\n          const { minX, maxX, minY, maxY } = mm.state;\r\n          const width = maxX - minX;\r\n          const height = maxY - minY;\r\n          const containerWidth = svg.clientWidth;\r\n          const containerHeight = svg.clientHeight;\r\n\r\n          // 计算合适的缩放比例\r\n          const scale = Math.min(\r\n            (containerWidth / width) * fitRatio,\r\n            (containerHeight / height) * fitRatio\r\n          );\r\n\r\n          // 更新数据以应用新的缩放\r\n          mm.setData(root, {\r\n            initialScale: scale,\r\n            initialPosition: [\r\n              (containerWidth - width * scale) / 2,\r\n              (containerHeight - height * scale) / 2,\r\n            ],\r\n          });\r\n        }, 100);\r\n\r\n        // 监听窗口大小变化\r\n        const resizeHandler = () => mm.fit();\r\n        window.addEventListener(\"resize\", resizeHandler);\r\n\r\n        // 组件销毁时清理\r\n        this.$once(\"hook:beforeDestroy\", () => {\r\n          window.removeEventListener(\"resize\", resizeHandler);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Markmap rendering error:\", error);\r\n        this.$message.error(\"思维导图渲染失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    // 打开新浪舆情通\r\n    openSina() {\r\n      window.open(this.sinaUrl, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.two {\r\n  height: 100%;\r\n  width: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .left {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .center {\r\n    margin: 0 11px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .center-top {\r\n      height: 150px;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      align-items: center;\r\n\r\n      .top-content {\r\n        position: relative;\r\n        width: 315px;\r\n        height: 98px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg1.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bg1 {\r\n        position: absolute;\r\n        top: 17px;\r\n        left: 43px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg2.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .top-content-number {\r\n        font-size: 24px;\r\n        color: #00e5ff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 44px;\r\n      }\r\n\r\n      .top-content-name {\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .bsContentBox2 {\r\n      background-image: url(\"../../assets/bigScreenSanhao/contentBg1.png\");\r\n\r\n      .bsContentContent {\r\n        height: calc((100% - 43px) / 2);\r\n      }\r\n\r\n      .kejidongtai-box {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .bsContentBox,\r\n  .bsContentBox2 {\r\n    background-image: url(\"../../assets/bigScreenSanhao/contentBg.png\");\r\n    background-size: 100% 100%;\r\n\r\n    .bsContentTitle {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n    }\r\n  }\r\n\r\n  .bsContentBox1 {\r\n    flex: 1;\r\n\r\n    .bsContentTitle1 {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      background-image: url(\"../../assets/bigScreenSanhao/title1.png\");\r\n      background-size: 100% 100%;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n\r\n        span {\r\n          font-weight: normal;\r\n          cursor: pointer;\r\n          color: rgba(0, 171, 244, 0.5);\r\n        }\r\n\r\n        .titleColor {\r\n          font-weight: 800;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n\r\n      .trump-view-container {\r\n        height: 300px;\r\n      }\r\n\r\n      .view-tree-container {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        & > div {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        z-index: 99;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      // Tab 按钮样式\r\n      .tab-buttons {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .tab-button {\r\n          padding: 8px 8px;\r\n          background: rgba(0, 171, 244, 0.2);\r\n          border: 1px solid rgba(0, 171, 244, 0.5);\r\n          border-radius: 4px;\r\n          color: rgba(0, 171, 244, 0.8);\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 171, 244, 0.3);\r\n            color: #00abf4;\r\n          }\r\n\r\n          &.active {\r\n            background: rgba(0, 171, 244, 0.5);\r\n            color: #ffffff;\r\n            border-color: #00abf4;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tab 内容样式\r\n      .tab-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .trump-view-container {\r\n          height: 300px;\r\n        }\r\n\r\n        .view-tree-container {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          & > div {\r\n            flex: 1;\r\n          }\r\n        }\r\n\r\n        .markmap-svg {\r\n          width: 100%;\r\n          height: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 热点推荐滚动列表样式\r\n  .remengwenzhang-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remengwenzhang-box1 {\r\n    width: 100%;\r\n    height: 230px;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: url(\"../../assets/bigScreenTwo/dialogBackground.png\") no-repeat;\r\n  background-size: 100% 100% !important;\r\n  background-size: cover;\r\n  height: 800px;\r\n\r\n  .el-dialog__header {\r\n    background-color: #1d233400;\r\n    font-size: 30px;\r\n    color: #ffffff;\r\n    line-height: 100px;\r\n    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);\r\n    height: 100px;\r\n\r\n    .el-dialog__title {\r\n      display: inline-block;\r\n      width: calc(100% - 100px);\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    background-color: #2a304000;\r\n    color: #f2f2f2;\r\n    height: calc(100% - 140px);\r\n    overflow: hidden;\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    background-color: #1d233400;\r\n    padding: 18px 20px;\r\n  }\r\n\r\n  .el-button {\r\n    background-color: #002766;\r\n    color: #fff;\r\n    border: 0px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    background: url(\"../../assets/bigScreenTwo/关闭小.png\") no-repeat;\r\n    background-size: 100% 100% !important;\r\n    background-size: cover;\r\n    width: 31px;\r\n    height: 31px;\r\n    top: 16px;\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-art {\r\n  background: #1d293b;\r\n  padding: 20px;\r\n  height: 590px;\r\n  overflow-y: auto;\r\n  line-height: 1.8em;\r\n  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,\r\n    sans-serif;\r\n\r\n  ::v-deep p {\r\n    text-indent: 2em;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::v-deep .dialog-art::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n\r\n.fz {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20px;\r\n\r\n  .text {\r\n    font-weight: 400;\r\n    font-size: 20px;\r\n    color: #ffffff;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #1d293b;\r\n    border-radius: 14px;\r\n    padding: 0 10px;\r\n    height: 28px;\r\n\r\n    .btn-minus,\r\n    .btn-plus {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 20px;\r\n      color: #ffffff;\r\n\r\n      &:hover {\r\n        color: #2f7cfe;\r\n      }\r\n    }\r\n\r\n    .font-size {\r\n      margin: 0 15px;\r\n      color: #ffffff;\r\n      font-size: 16px;\r\n      min-width: 45px;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.kejidongtai-box {\r\n  width: 100%;\r\n  // height: 45px;\r\n  // padding-top: 11px;\r\n  // margin: 3px 0;\r\n  display: flex;\r\n  // justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  column-gap: 10px;\r\n  row-gap: 10px;\r\n  padding: 10px;\r\n  margin-top: 10px;\r\n\r\n  .kejidongtai-button {\r\n    // width: 111px;\r\n    height: 33px;\r\n    line-height: 33px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan1.png\");\r\n    background-size: 100% 100%;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .active {\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan2.png\");\r\n  }\r\n}\r\n\r\n:deep(.markmap-node) {\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n:deep(.markmap-node-circle) {\r\n  fill: transparent; // 修改节点背景为透明\r\n  stroke-width: 2px;\r\n}\r\n\r\n:deep(.markmap-node-text) {\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\r\n    \"Helvetica Neue\", Arial;\r\n\r\n  tspan {\r\n    fill: #333 !important; // 修改文字颜色为深色\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.markmap-link) {\r\n  fill: none;\r\n  stroke-width: 2.5px; // 加粗连线\r\n}\r\n\r\n// 根节点样式\r\n:deep(.markmap-node[data-depth=\"0\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #0052ff; // 亮蓝色\r\n    stroke-width: 3px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 20px !important;\r\n    font-weight: bold !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 二级节点样式\r\n:deep(.markmap-node[data-depth=\"1\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #009600; // 亮绿色\r\n    stroke-width: 2.5px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 三级节点样式\r\n:deep(.markmap-node[data-depth=\"2\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #ff6600; // 亮橙色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 16px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 其他层级节点样式\r\n:deep(.markmap-node[data-depth=\"3\"]),\r\n:deep(.markmap-node[data-depth=\"4\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #8000ff; // 亮紫色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 14px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8kBA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,mBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,cAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,WAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,eAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,mBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,sBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,gBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,cAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,gBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,kBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,iBAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,oBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AACA,IAAAgB,OAAA,GAAAhB,OAAA;AAcA,IAAAiB,MAAA,GAAAjB,OAAA;AAMA,IAAAkB,MAAA,GAAAlB,OAAA;AACA,IAAAmB,MAAA,GAAAnB,OAAA;AACA,IAAAoB,cAAA,GAAArB,sBAAA,CAAAC,OAAA;AACA,IAAAqB,UAAA,GAAArB,OAAA;AAKA,IAAAsB,WAAA,GAAAtB,OAAA;AACA,IAAAuB,YAAA,GAAAvB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAwB,IAAA;EACAC,UAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,qBAAA,EAAAA,8BAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,gBAAA,EAAAA,yBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,mBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,oBAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAC,gBAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,mBAAA;MACAC,wBAAA;MACAC,sBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,wBAAA;MACAC,mBAAA;MACAC,0BAAA;MACAC,sBAAA;MACAC,qBAAA;MACAC,2BAAA;MACAC,8BAAA;MACAC,0BAAA;MACAC,uBAAA;MACAC,mBAAA;MACAC,qBAAA;MACAC,uBAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,kBAAA;MACAC,uBAAA;MACAC,4BAAA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,aAAA;MACAC,eAAA;MACAC,eAAA;MACAC,oBAAA;MACA;MACAC,UAAA;MACAC,kBAAA;MACA;MACAC,kBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,YAAA;MACAC,SAAA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;MACAC,UAAA;MACAC,oBAAA;MACAC,QAAA;MACAC,WAAA;MACA;MACAC,iBAAA;MACA;MACAC,iBAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,eAAA;MACAC,WAAA;MACAC,eAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;MACAC,SAAA;MACAC,gBAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,gBAAA;MAAA;MACAC,mBAAA;MAAA;MACA;MACAC,uBAAA;MACAC,qBAAA;MACAC,wBAAA;MACA;MACAC,SAAA;MAAA;MACAC,cAAA;MACAC,OAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,sBAAA,WAAAA,uBAAA;MACA,IAAAC,kBAAA;;MAEA,SAAAxE,gBAAA;QACA;UACAyE,MAAA,iBAAAC,MAAA,CAAAF,kBAAA;QACA;MACA;QACA;UACAC,MAAA;QACA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAC,iBAAA,IACAC,IAAA;MACAC,OAAA,CAAAC,GAAA;IACA,GACAC,KAAA,WAAAC,KAAA;MACAH,OAAA,CAAAG,KAAA,cAAAA,KAAA;IACA;;IAEA;IACA,IAAAC,iBAAA,IACAL,IAAA,WAAAM,GAAA;MACAL,OAAA,CAAAC,GAAA;MACAJ,KAAA,CAAAR,OAAA,GAAAgB,GAAA;IACA,GACAH,KAAA,WAAAC,KAAA;MACAH,OAAA,CAAAG,KAAA,YAAAA,KAAA;IACA;IAEA,KAAAG,eAAA;IACA,KAAAC,WAAA;IACA,KAAAC,YAAA;IACA,KAAAC,eAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,eAAA;IACA,IAAAC,iCAAA,MAAAb,IAAA,WAAAM,GAAA;MACAR,KAAA,CAAAzB,WAAA,GAAAiC,GAAA,CAAAlF,IAAA,CAAAiD,WAAA;MACAyB,KAAA,CAAAxB,eAAA,GAAAgC,GAAA,CAAAlF,IAAA,CAAAkD,eAAA;IACA;EACA;EACAwC,aAAA,WAAAA,cAAA;IACA,KAAAC,gBAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA;IACAP,eAAA,WAAAA,gBAAA;MAAA,IAAAQ,MAAA;MAAA,WAAAC,kBAAA,CAAApG,OAAA,mBAAAqG,oBAAA,CAAArG,OAAA,IAAAsG,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA;QAAA,WAAAH,oBAAA,CAAArG,OAAA,IAAAyG,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,sBAAA;gBACAC,SAAA;gBACAC,QAAA;gBACAC,QAAA;cACA;YAAA;cAJAT,QAAA,GAAAG,QAAA,CAAAO,IAAA;cAKAf,MAAA,CAAAlE,UAAA,GAAAuE,QAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAQ,EAAA,GAAAR,QAAA;cAEA3B,OAAA,CAAAG,KAAA,gBAAAwB,QAAA,CAAAQ,EAAA;YAAA;YAAA;cAAA,OAAAR,QAAA,CAAAS,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IAEA;IAEAc,kBAAA,WAAAA,mBAAAvH,IAAA,EAAAwH,IAAA;MAAA,IAAAC,MAAA;MACA,KAAArF,kBAAA,GAAAoF,IAAA;MACA,QAAAxH,IAAA;QACA;UACA,IAAA0H,8BAAA;YAAAC,EAAA,EAAAH,IAAA,CAAAG;UAAA,GAAA1C,IAAA,WAAAM,GAAA;YACAkC,MAAA,CAAArG,mBAAA,GAAAoG,IAAA,CAAAI,KAAA;YACAH,MAAA,CAAApG,qBAAA,GAAAkE,GAAA,CAAAlF,IAAA,CAAAwH,OAAA;YACAJ,MAAA,CAAAnG,uBAAA,GAAAiE,GAAA,CAAAlF,IAAA,CAAAyH,SAAA;YACAL,MAAA,CAAAtG,uBAAA;UACA;UACA;QACA;UACA,KAAA4G,WAAA,CAAAP,IAAA;UACA;QACA;UACA,KAAAO,WAAA,CAAAP,IAAA;UACA;MACA;IACA;IACAQ,yBAAA,WAAAA,0BAAAR,IAAA;MAAA,IAAAS,MAAA;MACA,IAAAC,0BAAA;QACAC,UAAA,EAAAX,IAAA,CAAAW,UAAA;QACAC,OAAA;QACAC,QAAA;MACA,GAAApD,IAAA,WAAAM,GAAA;QACA0C,MAAA,CAAArG,UAAA,GAAA2D,GAAA,CAAA+C,IAAA;QACAL,MAAA,CAAAnG,WAAA,GAAAyD,GAAA,CAAAgD,KAAA;MACA;MACA,IAAAC,4BAAA;QACAL,UAAA,EAAAX,IAAA,CAAAW,UAAA;QACAC,OAAA;QACAC,QAAA;MACA,GAAApD,IAAA,WAAAM,GAAA;QACA0C,MAAA,CAAApG,YAAA,GAAA0D,GAAA,CAAA+C,IAAA;QACAL,MAAA,CAAAlG,aAAA,GAAAwD,GAAA,CAAAgD,KAAA;MACA;MACA,KAAA5G,4BAAA,OAAA8G,cAAA,CAAAvI,OAAA,MAAAsH,IAAA;MACA,KAAAtG,0BAAA,GAAAsG,IAAA,CAAAkB,cAAA;MACA,KAAAzH,8BAAA;IACA;IAEA0H,gBAAA,WAAAA,iBAAAR,UAAA,EAAAS,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAX,0BAAA,MAAAO,cAAA,CAAAvI,OAAA;QACAiI,UAAA,EAAAA;MAAA,GACAS,WAAA,CACA,EAAA3D,IAAA,WAAAM,GAAA;QACAsD,MAAA,CAAAjH,UAAA,GAAA2D,GAAA,CAAA+C,IAAA;MACA;IACA;IAEAQ,kBAAA,WAAAA,mBAAAX,UAAA,EAAAS,WAAA;MAAA,IAAAG,MAAA;MACA,IAAAP,4BAAA,MAAAC,cAAA,CAAAvI,OAAA;QACAiI,UAAA,EAAAA;MAAA,GACAS,WAAA,CACA,EAAA3D,IAAA,WAAAM,GAAA;QACAwD,MAAA,CAAAlH,YAAA,GAAA0D,GAAA,CAAA+C,IAAA;MACA;IACA;IAEA9C,eAAA,WAAAA,gBAAA;MAAA,IAAAwD,MAAA;MACA,IAAAC,oBAAA;QACAhC,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAlC,IAAA,WAAAM,GAAA;QACA,IAAAlF,IAAA;QACA6I,MAAA,CAAAC,IAAA,CAAA5D,GAAA,CAAAlF,IAAA,EAAA+I,OAAA,WAAAC,GAAA;UACAhJ,IAAA,CAAAiJ,IAAA;YACAC,IAAA,EAAAF,GAAA;YACAG,WAAA,EACAjE,GAAA,CAAAlF,IAAA,CAAAgJ,GAAA,EAAAI,MAAA,QACAlE,GAAA,CAAAlF,IAAA,CAAAgJ,GAAA,IACA9D,GAAA,CAAAlF,IAAA,CAAAgJ,GAAA,EAAAK,KAAA,CACAnE,GAAA,CAAAlF,IAAA,CAAAgJ,GAAA,EAAAI,MAAA,MACAlE,GAAA,CAAAlF,IAAA,CAAAgJ,GAAA,EAAAI,MACA;UACA;QACA;QACAT,MAAA,CAAAzH,gBAAA,GAAAlB,IAAA,CAAAsJ,OAAA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,8BAAA;QACA7C,SAAA;QACAC,QAAA;QACAC,QAAA;QACAiB,OAAA;QACAC,QAAA;MACA,GAAApD,IAAA,WAAAM,GAAA;QACAsE,MAAA,CAAApI,kBAAA,GAAA8D,GAAA,CAAA+C,IAAA,CAAAyB,GAAA,WAAAvC,IAAA,EAAAwC,KAAA;UAAA,WAAAvB,cAAA,CAAAvI,OAAA,MAAAuI,cAAA,CAAAvI,OAAA,MACAsH,IAAA;YACAxH,IAAA,EAAAgK,KAAA;UAAA;QAAA,CACA;QACAH,MAAA,CAAAnI,uBAAA,GAAA6D,GAAA,CAAAgD,KAAA;MACA;MACA,IAAA0B,0BAAA;QACAhD,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAlC,IAAA,WAAAM,GAAA;QACA;QACA,IAAAlF,IAAA,GAAA6I,MAAA,CAAAC,IAAA,CAAA5D,GAAA,CAAAlF,IAAA,EAAA0J,GAAA,WAAAG,IAAA;UAAA;YACAC,OAAA,EAAAD,IAAA;YACAE,EAAA,EAAA7E,GAAA,CAAAlF,IAAA,CAAA6J,IAAA,EAAAE,EAAA;YACAC,EAAA,EAAA9E,GAAA,CAAAlF,IAAA,CAAA6J,IAAA,EAAAG,EAAA;YACAC,EAAA,EAAA/E,GAAA,CAAAlF,IAAA,CAAA6J,IAAA,EAAAI;UACA;QAAA;QACAT,MAAA,CAAArI,gBAAA,GAAAnB,IAAA;QACAwJ,MAAA,CAAA7I,2BAAA;MACA;IACA;IAEAuJ,4BAAA,WAAAA,6BAAA3B,WAAA;MAAA,IAAA4B,MAAA;MACA,IAAAV,8BAAA,MAAArB,cAAA,CAAAvI,OAAA;QACA+G,SAAA;QACAC,QAAA;QACAC,QAAA;MAAA,GACAyB,WAAA,CACA,EAAA3D,IAAA,WAAAM,GAAA;QACAiF,MAAA,CAAA/I,kBAAA,GAAA8D,GAAA,CAAA+C,IAAA,CAAAyB,GAAA,WAAAvC,IAAA,EAAAwC,KAAA;UAAA,WAAAvB,cAAA,CAAAvI,OAAA,MAAAuI,cAAA,CAAAvI,OAAA,MACAsH,IAAA;YACAxH,IAAA,EAAAgK,KAAA;UAAA;QAAA,CACA;MACA;IACA;IAEAS,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,qBAAA;QACA1D,SAAA;QACAC,QAAA;QACAC,QAAA;QACAiB,OAAA;QACAC,QAAA;MACA,GAAApD,IAAA,WAAAM,GAAA;QACAmF,OAAA,CAAA1I,eAAA,GAAAuD,GAAA,CAAA+C,IAAA;QACAoC,OAAA,CAAAxI,oBAAA,GAAAqD,GAAA,CAAAgD,KAAA;MACA;MACA,IAAAqC,4BAAA;QACA3D,SAAA;QACAC,QAAA;QACAC,QAAA;MACA,GAAAlC,IAAA,WAAAM,GAAA;QACAmF,OAAA,CAAAzI,eAAA,GAAAsD,GAAA,CAAAlF,IAAA;QACAqK,OAAA,CAAApK,mBAAA;MACA;IACA;IAEAuK,oBAAA,WAAAA,qBAAAjC,WAAA;MAAA,IAAAkC,OAAA;MACA,IAAAH,qBAAA,MAAAlC,cAAA,CAAAvI,OAAA;QACA+G,SAAA;QACAC,QAAA;QACAC,QAAA;MAAA,GACAyB,WAAA,CACA,EAAA3D,IAAA,WAAAM,GAAA;QACAuF,OAAA,CAAA9I,eAAA,GAAAuD,GAAA,CAAA+C,IAAA;MACA;IACA;IAEAyC,iBAAA,WAAAA,kBAAA1K,IAAA;MACA,KAAAG,sBAAA;MACA,KAAAC,mBAAA,GAAAJ,IAAA,CAAAuH,KAAA;MACA,KAAAlH,gBAAA,GAAAL,IAAA,CAAA2K,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA5K,IAAA;MACA;MACA;;MAEA;MACA,SAAAgE,SAAA;QACA;QACA,KAAAZ,cAAA,GACAyH,mBAAA,CAAAC,KAAA,MAAAC,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA5H,YAAA;MACA,gBAAAW,SAAA;QACA;QACA,KAAAZ,cAAA,GACAyH,mBAAA,CAAAK,GAAA,MAAAH,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA5H,YAAA;MACA,gBAAAW,SAAA;QACA;QACA,KAAAZ,cAAA,GACAyH,mBAAA,CAAAM,EAAA,MAAAJ,KAAA,CAAAC,iBAAA,CAAAC,QAAA;QACA,KAAA5H,YAAA;MACA;QACA,KAAAD,cAAA,QAAAa,cAAA;QACA,aAAAD,SAAA;UACA;YACA,KAAAX,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;UACA;YACA,KAAAA,YAAA;YACA;QACA;MACA;MACA,KAAAC,SAAA;MACA,KAAAH,cAAA;IACA;IAEAiI,qBAAA,WAAAA,sBAAApL,IAAA;MACA,KAAAQ,0BAAA;MACA,KAAAC,sBAAA,GAAAT,IAAA,CAAA1B,IAAA;MACA,KAAAoC,qBAAA,GAAAV,IAAA,CAAAA,IAAA;IACA;IAEA;IACAoF,WAAA,WAAAA,YAAA;MAAA,IAAAiG,OAAA;MACA;MACA,IAAAC,oBAAA,IACA1G,IAAA,WAAAM,GAAA;QACAmG,OAAA,CAAArJ,kBAAA,GAAAkD,GAAA,CAAAlF,IAAA;QACAqL,OAAA,CAAAE,SAAA;UACAF,OAAA,CAAAG,WAAA;QACA;MACA,GACAzG,KAAA,WAAAC,KAAA;QACAH,OAAA,CAAAG,KAAA,gBAAAA,KAAA;QACA;QACAqG,OAAA,CAAArJ,kBAAA;MACA;IACA;IACAqD,YAAA,WAAAA,aAAA;MAAA,IAAAoG,OAAA;MACA,KAAAC,iBAAA;MACA,KAAAH,SAAA;QACAE,OAAA,CAAAE,YAAA;MACA;IACA;IAEAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,OAAA;MACA,KAAAjG,gBAAA;MACA,IAAAkG,OAAA,QAAAd,KAAA,CAAAe,aAAA;MACA,IAAAtE,OAAA,QAAAuD,KAAA,CAAAgB,aAAA;MAEA,KAAAF,OAAA,KAAArE,OAAA;MAEA,KAAAvF,WAAA,GAAA+J,WAAA;QACA,IAAAJ,OAAA,CAAAxJ,SAAA;QAEA,IAAAyJ,OAAA,CAAAI,SAAA,IAAAzE,OAAA,CAAA0E,YAAA,GAAAL,OAAA,CAAAM,YAAA;UACAN,OAAA,CAAAI,SAAA;QACA;UACAJ,OAAA,CAAAI,SAAA,IAAAL,OAAA,CAAArJ,UAAA;QACA;QACAqJ,OAAA,CAAAtG,eAAA;MACA;IACA;IAEAK,gBAAA,WAAAA,iBAAA;MACA,SAAA1D,WAAA;QACAmK,aAAA,MAAAnK,WAAA;QACA,KAAAA,WAAA;MACA;IACA;IAEAoK,gBAAA,WAAAA,iBAAA;MACA,KAAAjK,SAAA;IACA;IAEAkK,gBAAA,WAAAA,iBAAA;MACA,KAAAlK,SAAA;MACA,KAAAoJ,WAAA;IACA;IACAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAA5G,iBAAA;MACA,IAAAiG,OAAA,QAAAd,KAAA,CAAA0B,cAAA;MACA,IAAAjF,OAAA,QAAAuD,KAAA,CAAA2B,cAAA;MAEA,KAAAb,OAAA,KAAArE,OAAA;MAEA,KAAAtF,YAAA,GAAA8J,WAAA;QACA,IAAAQ,OAAA,CAAAnK,UAAA;QAEA,IAAAwJ,OAAA,CAAAI,SAAA,IAAAzE,OAAA,CAAA0E,YAAA,GAAAL,OAAA,CAAAM,YAAA;UACAN,OAAA,CAAAI,SAAA;QACA;UACAJ,OAAA,CAAAI,SAAA,IAAAO,OAAA,CAAAjK,UAAA;QACA;QACAiK,OAAA,CAAAjH,gBAAA;MACA;IACA;IACAoG,YAAA,WAAAA,aAAA;MAAA,IAAAgB,OAAA;MACA,KAAA9G,iBAAA;MACA,KAAA1D,YAAA,GAAA6J,WAAA;QACA,IAAAW,OAAA,CAAAtK,UAAA;;QAEA;QACA,IAAAuK,QAAA,IACA,QACA,QACA,SACA,WACA,QACA,QACA,QACA,QACA,KACA;;QAEA;QACA,IAAAC,YAAA,GAAAD,QAAA,CAAAE,OAAA,CAAAH,OAAA,CAAA5J,YAAA;QACA;QACA,IAAAgK,SAAA,IAAAF,YAAA,QAAAD,QAAA,CAAAxD,MAAA;QACA;QACAuD,OAAA,CAAAjB,iBAAA,CAAAkB,QAAA,CAAAG,SAAA;MACA;IACA;IACAnH,iBAAA,WAAAA,kBAAA;MACA,SAAA1D,YAAA;QACAkK,aAAA,MAAAlK,YAAA;QACA,KAAAA,YAAA;MACA;IACA;IACA2D,iBAAA,WAAAA,kBAAA;MACA,SAAA1D,YAAA;QACAiK,aAAA,MAAAjK,YAAA;QACA,KAAAA,YAAA;MACA;IACA;IACA6K,iBAAA,WAAAA,kBAAA;MACA,KAAA3K,UAAA;IACA;IAEA4K,iBAAA,WAAAA,kBAAA;MACA,KAAA5K,UAAA;MACA;MACA;IACA;IACA6K,iBAAA,WAAAA,kBAAA;MACA,KAAA5K,UAAA;IACA;IAEA6K,iBAAA,WAAAA,kBAAA;MACA,KAAA7K,UAAA;IACA;IACAgD,eAAA,WAAAA,gBAAA;MACA,IAAAuG,OAAA,QAAAd,KAAA,CAAAe,aAAA;MACA,KAAAD,OAAA;MAEA,IAAAI,SAAA,GAAAJ,OAAA,CAAAI,SAAA;QAAAC,YAAA,GAAAL,OAAA,CAAAK,YAAA;QAAAC,YAAA,GAAAN,OAAA,CAAAM,YAAA;MACA,IAAAiB,aAAA,GAAAjB,YAAA,GAAAD,YAAA;MACA,IAAAmB,eAAA,GAAAC,IAAA,CAAAC,GAAA,KAAAH,aAAA,GAAAjB,YAAA;MACA,IAAAqB,YAAA,GAAAvB,SAAA,GAAAC,YAAA,GAAAC,YAAA;MAEAsB,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,yBAAApJ,MAAA,CACA6I,eAAA,OACA;MACAI,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,sBAAApJ,MAAA,CACAgJ,YAAA,OACA;IACA;IACAjI,gBAAA,WAAAA,iBAAA;MACA,IAAAsG,OAAA,QAAAd,KAAA,CAAA0B,cAAA;MACA,KAAAZ,OAAA;MAEA,IAAAI,SAAA,GAAAJ,OAAA,CAAAI,SAAA;QAAAC,YAAA,GAAAL,OAAA,CAAAK,YAAA;QAAAC,YAAA,GAAAN,OAAA,CAAAM,YAAA;MACA,IAAAiB,aAAA,GAAAjB,YAAA,GAAAD,YAAA;MACA,IAAAmB,eAAA,GAAAC,IAAA,CAAAC,GAAA,KAAAH,aAAA,GAAAjB,YAAA;MACA,IAAAqB,YAAA,GAAAvB,SAAA,GAAAC,YAAA,GAAAC,YAAA;MAEAsB,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,yBAAApJ,MAAA,CACA6I,eAAA,OACA;MACAI,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAC,WAAA,CACA,sBAAApJ,MAAA,CACAgJ,YAAA,OACA;IACA;IAEA9F,WAAA,WAAAA,YAAAP,IAAA;MAAA,IAAA0G,OAAA;MAAA,WAAA5H,kBAAA,CAAApG,OAAA,mBAAAqG,oBAAA,CAAArG,OAAA,IAAAsG,IAAA,UAAA2H,SAAA;QAAA,IAAA5I,GAAA,EAAAsC,OAAA;QAAA,WAAAtB,oBAAA,CAAArG,OAAA,IAAAyG,IAAA,UAAAyH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,IAAA,GAAAuH,SAAA,CAAAtH,IAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAtH,IAAA;cAAA,OAGA,IAAAuH,wBAAA,EAAA9G,IAAA,CAAAG,EAAA;YAAA;cAAApC,GAAA,GAAA8I,SAAA,CAAAjH,IAAA;cACA8G,OAAA,CAAArL,UAAA;gBACA0L,OAAA,EACA/G,IAAA,CAAA+G,OAAA,IAAA/G,IAAA,CAAAI,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAuH,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAkO,OAAA;gBACA3G,KAAA,EACAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA,IAAAhJ,GAAA,CAAAlF,IAAA,CAAAuH,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAkO,OAAA;gBACAC,SAAA,EAAAjJ,GAAA,CAAAlF,IAAA,CAAAwH,OAAA,IAAAtC,GAAA,CAAAlF,IAAA,CAAAmO;cACA;;cAEA;cACA3G,OAAA,GAAAqG,OAAA,CAAAO,cAAA,CAAAP,OAAA,CAAArL,UAAA,CAAA2L,SAAA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAN,OAAA,CAAArL,UAAA,CAAA2L,SAAA,GAAA3G,OAAA;cAEAqG,OAAA,CAAApL,oBAAA;cACAoL,OAAA,CAAAlL,WAAA,GAAAkL,OAAA,CAAAnL,QAAA;cAAAsL,SAAA,CAAAtH,IAAA;cAAA;YAAA;cAAAsH,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAhH,EAAA,GAAAgH,SAAA;cAEAnJ,OAAA,CAAAG,KAAA,cAAAgJ,SAAA,CAAAhH,EAAA;cACA;cACA6G,OAAA,CAAArL,UAAA;gBACA0L,OAAA,EAAA/G,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACA3G,KAAA,EAAAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACAC,SAAA;cACA;cACAN,OAAA,CAAApL,oBAAA;cACAoL,OAAA,CAAAlL,WAAA,GAAAkL,OAAA,CAAAnL,QAAA;YAAA;YAAA;cAAA,OAAAsL,SAAA,CAAA/G,IAAA;UAAA;QAAA,GAAA6G,QAAA;MAAA;IAEA;IACAO,YAAA,WAAAA,aAAAlH,IAAA;MAAA,IAAAmH,OAAA;MAAA,WAAArI,kBAAA,CAAApG,OAAA,mBAAAqG,oBAAA,CAAArG,OAAA,IAAAsG,IAAA,UAAAoI,SAAA;QAAA,IAAArJ,GAAA,EAAAsC,OAAA;QAAA,WAAAtB,oBAAA,CAAArG,OAAA,IAAAyG,IAAA,UAAAkI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhI,IAAA,GAAAgI,SAAA,CAAA/H,IAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAA/H,IAAA;cAAA,OAGA,IAAAgI,kBAAA,EAAAvH,IAAA,CAAAwH,EAAA;YAAA;cAAAzJ,GAAA,GAAAuJ,SAAA,CAAA1H,IAAA;cACAuH,OAAA,CAAA9L,UAAA;gBACA0L,OAAA,EACA/G,IAAA,CAAA+G,OAAA,IAAA/G,IAAA,CAAAI,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAuH,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAkO,OAAA;gBACA3G,KAAA,EACAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA,IAAAhJ,GAAA,CAAAlF,IAAA,CAAAuH,KAAA,IAAArC,GAAA,CAAAlF,IAAA,CAAAkO,OAAA;gBACAC,SAAA,EAAAjJ,GAAA,CAAAlF,IAAA,CAAAwH,OAAA,IAAAtC,GAAA,CAAAlF,IAAA,CAAAmO;cACA;;cAEA;cACA3G,OAAA,GAAA8G,OAAA,CAAAF,cAAA,CAAAE,OAAA,CAAA9L,UAAA,CAAA2L,SAAA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACAG,OAAA,CAAA9L,UAAA,CAAA2L,SAAA,GAAA3G,OAAA;cAEA8G,OAAA,CAAA7L,oBAAA;cACA6L,OAAA,CAAA3L,WAAA,GAAA2L,OAAA,CAAA5L,QAAA;cAAA+L,SAAA,CAAA/H,IAAA;cAAA;YAAA;cAAA+H,SAAA,CAAAhI,IAAA;cAAAgI,SAAA,CAAAzH,EAAA,GAAAyH,SAAA;cAEA5J,OAAA,CAAAG,KAAA,cAAAyJ,SAAA,CAAAzH,EAAA;cACA;cACAsH,OAAA,CAAA9L,UAAA;gBACA0L,OAAA,EAAA/G,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACA3G,KAAA,EAAAJ,IAAA,CAAAI,KAAA,IAAAJ,IAAA,CAAA+G,OAAA;gBACAC,SAAA;cACA;cACAG,OAAA,CAAA7L,oBAAA;cACA6L,OAAA,CAAA3L,WAAA,GAAA2L,OAAA,CAAA5L,QAAA;YAAA;YAAA;cAAA,OAAA+L,SAAA,CAAAxH,IAAA;UAAA;QAAA,GAAAsH,QAAA;MAAA;IAEA;IACAH,cAAA,WAAAA,eAAA5G,OAAA;MACA,IAAAA,OAAA;QACA,QAAAoH,2BAAA,EAAApH,OAAA;UACAA,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UAEAhK,OAAA,CAAAC,GAAA,iQAAAgK,0BAAA,EAAAtH,OAAA;UACA3C,OAAA,CAAAC,GAAA,wQAAAiK,gCAAA,EAAAvH,OAAA;QACA;UACAA,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;UACA;UACArH,OAAA,GAAAA,OAAA,CAAAqH,OAAA,CACA,+CACA,IACA;QACA;MACA;MACA,OAAArH,OAAA;IACA;IAEAwH,WAAA,WAAAA,YAAA;MACA,KAAAxM,UAAA;MACA,KAAAC,oBAAA;IACA;IAEAwM,gBAAA,WAAAA,iBAAA;MACA,SAAAvM,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IAEAwM,gBAAA,WAAAA,iBAAA;MACA,SAAAxM,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACAyM,eAAA,WAAAA,gBAAAxP,IAAA;MACA;MACA,IAAAyP,gBAAA;MACA,SAAApL,SAAA;QACAoL,gBAAA;MACA,gBAAApL,SAAA;QACAoL,gBAAA;MACA;MACA,IAAAC,OAAA,GAAAC,IAAA,CAAAC,KAAA,CACAD,IAAA,CAAAE,SAAA,CAAAC,gBAAA,CAAAL,gBAAA,EAAAzP,IAAA,QACA;MACA,KAAAiD,iBAAA,QAAA8M,mBAAA,CAAAL,OAAA;IACA;IACAK,mBAAA,WAAAA,oBAAA1P,IAAA;MAAA,IAAA2P,OAAA;MACA,KAAA3P,IAAA,KAAAJ,KAAA,CAAAgQ,OAAA,CAAA5P,IAAA,UAAAA,IAAA;MACA,OAAAA,IAAA,CAAA0J,GAAA,WAAAvC,IAAA;QACA,IACA,CAAAA,IAAA,CAAAxH,IAAA,kBACAwH,IAAA,CAAAxH,IAAA,kBACAwH,IAAA,CAAAxH,IAAA,mBACAC,KAAA,CAAAgQ,OAAA,CAAAzI,IAAA,CAAA0I,QAAA,GACA;UACA1I,IAAA,CAAA0I,QAAA,GAAA1I,IAAA,CAAA0I,QAAA,CAAAxG,KAAA;QACA;QAEA,IAAAlC,IAAA,CAAA0I,QAAA;UACA1I,IAAA,CAAA0I,QAAA,GAAAF,OAAA,CAAAD,mBAAA,CAAAvI,IAAA,CAAA0I,QAAA;QACA;QAEA,OAAA1I,IAAA;MACA;IACA;IACAuE,iBAAA,WAAAA,kBAAA/L,IAAA;MAAA,IAAAmQ,OAAA;MACA,IAAAC,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,EAAA;MACA;MACA,KAAAzN,YAAA,GAAApD,IAAA;;MAEA;MACA,KAAAgM,YAAA;MAEA,IAAA8E,uBAAA;QACAC,OAAA,EAAAX,GAAA,CAAApQ,IAAA;MACA,GAAAiF,IAAA,WAAAM,GAAA;QACA;QACA,IAAAyL,gBAAA,GAAAb,OAAA,CAAAc,mBAAA,CAAA1L,GAAA;QACA4K,OAAA,CAAAhN,mBAAA,GAAA6N,gBAAA;QACAb,OAAA,CAAAvE,SAAA;UACA,IAAAM,OAAA,GAAAiE,OAAA,CAAA/E,KAAA,CAAA0B,cAAA;UACAZ,OAAA,CAAAI,SAAA;QACA;MACA;IACA;IACA4E,gBAAA,WAAAA,iBAAA;MACA,IAAAd,GAAA;QACAC,IAAA;QACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,IAAA;QACAC,EAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAhB,GAAA,MAAAhN,YAAA;IACA;IACA;IACA6N,mBAAA,WAAAA,oBAAAI,QAAA;MACA,KAAApR,KAAA,CAAAgQ,OAAA,CAAAoB,QAAA;QACA;MACA;MAEA,IAAAC,IAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;MAEAH,QAAA,CAAAjI,OAAA,WAAAqI,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAAlD,OAAA;UACA;UACA,IAAAmD,eAAA,GAAAD,OAAA,CAAAlD,OAAA,CAAAW,OAAA;UAEA,KAAAoC,IAAA,CAAAK,GAAA,CAAAD,eAAA;YACAJ,IAAA,CAAAM,GAAA,CAAAF,eAAA;YACAF,MAAA,CAAAlI,IAAA,CAAAmI,OAAA;UACA;QACA;UACA;UACAD,MAAA,CAAAlI,IAAA,CAAAmI,OAAA;QACA;MACA;MAEA,OAAAD,MAAA;IACA;IACAK,YAAA,WAAAA,aAAAC,GAAA,EAAAC,YAAA;MACA,IAAAC,MAAA,GAAAF,GAAA,CAAAG,QAAA;MACA,IAAAC,OAAA,OAAAC,MAAA,CAAAJ,YAAA,GAAAC,MAAA,CAAAvI,MAAA;MACA,UAAA5E,MAAA,CAAAqN,OAAA,EAAArN,MAAA,CAAAmN,MAAA,EAAA9C,OAAA;IACA;IACAkD,UAAA,WAAAA,WAAAC,GAAA;MACAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA;IACA;IACAlM,kBAAA,WAAAA,mBAAA;MACA,KAAA1C,cAAA;MACA,KAAAE,SAAA;MACA,KAAAH,cAAA;IACA;IAEA;IACA8O,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAjM,kBAAA,CAAApG,OAAA,mBAAAqG,oBAAA,CAAArG,OAAA,IAAAsG,IAAA,UAAAgM,SAAA;QAAA,IAAA9L,QAAA,EAAA+L,WAAA;QAAA,WAAAlM,oBAAA,CAAArG,OAAA,IAAAyG,IAAA,UAAA+L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7L,IAAA,GAAA6L,SAAA,CAAA5L,IAAA;YAAA;cAAA4L,SAAA,CAAA7L,IAAA;cAAA6L,SAAA,CAAA5L,IAAA;cAAA,OAEA,IAAA4E,oBAAA;YAAA;cAAAjF,QAAA,GAAAiM,SAAA,CAAAvL,IAAA;cACA,IAAAV,QAAA,IAAAA,QAAA,CAAArG,IAAA;gBACAoS,WAAA,GAAA/L,QAAA,CAAArG,IAAA,EACA;gBACA,IAAAkS,OAAA,CAAAK,oBAAA,CAAAH,WAAA;kBACAF,OAAA,CAAAlQ,kBAAA,GAAAoQ,WAAA;gBACA,QACA;cACA;cAAAE,SAAA,CAAA5L,IAAA;cAAA;YAAA;cAAA4L,SAAA,CAAA7L,IAAA;cAAA6L,SAAA,CAAAtL,EAAA,GAAAsL,SAAA;cAEAzN,OAAA,CAAAG,KAAA,kBAAAsN,SAAA,CAAAtL,EAAA;YAAA;YAAA;cAAA,OAAAsL,SAAA,CAAArL,IAAA;UAAA;QAAA,GAAAkL,QAAA;MAAA;IAEA;IAEA;IACAI,oBAAA,WAAAA,qBAAAH,WAAA;MACA;MACA,SAAApQ,kBAAA,CAAAoH,MAAA;QACA,OAAAgJ,WAAA,CAAAhJ,MAAA;MACA;;MAEA;MACA,SAAApH,kBAAA,CAAAoH,MAAA,KAAAgJ,WAAA,CAAAhJ,MAAA;QACA;MACA;;MAEA;MACA,SAAAoJ,CAAA,MAAAA,CAAA,GAAAJ,WAAA,CAAAhJ,MAAA,EAAAoJ,CAAA;QACA,IAAAC,UAAA,GAAAL,WAAA,CAAAI,CAAA;QACA,IAAAE,UAAA,QAAA1Q,kBAAA,CAAAwQ,CAAA;;QAEA;QACA,IACAC,UAAA,CAAAnL,EAAA,KAAAoL,UAAA,CAAApL,EAAA,IACAmL,UAAA,CAAAlL,KAAA,KAAAmL,UAAA,CAAAnL,KAAA,IACAkL,UAAA,CAAAE,WAAA,KAAAD,UAAA,CAAAC,WAAA,IACAF,UAAA,CAAAG,UAAA,KAAAF,UAAA,CAAAE,UAAA,EACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAAhM,QAAA,EAAAiM,UAAA;MACAjO,OAAA,CAAAC,GAAA,6OAAAgO,UAAA,qCAAAjM,QAAA;MACA,KAAAlD,gBAAA,GAAAkD,QAAA;MACA,KAAAjD,mBAAA,GAAAiD,QAAA;MACA;MACA,KAAAhD,uBAAA;MACA,KAAAC,qBAAA,GAAAgP,UAAA;MACA,KAAA/O,wBAAA,GAAA8C,QAAA;;MAEA;MACA,KAAA0E,SAAA;QACA;QACA;MAAA,CACA;IACA;IAEA;IACAwH,2BAAA,WAAAA,4BAAA;MACA,KAAAlP,uBAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,wBAAA;IACA;IAEA;IACAiP,uBAAA,WAAAA,wBAAA;MACA,KAAAC,KAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA9B,OAAA;MACA,KAAA6B,KAAA,8BAAA7B,OAAA;IACA;IAEA;IACA+B,SAAA,WAAAA,UAAAC,OAAA,EAAAC,YAAA;MAAA,IAAAC,OAAA;MACA,IAAAD,YAAA;QACA,KAAArP,SAAA,GAAAoP,OAAA;QACA,KAAAnP,cAAA,GAAAsP,cAAA,UAAAF,YAAA;QACA,KAAAG,aAAA;MACA,WAAAJ,OAAA,gBAAAA,OAAA,cAAAA,OAAA;QACA;QACA,SAAApP,SAAA,KAAAoP,OAAA;UACA;UACA,KAAAxQ,iBAAA;UACA;UACA,KAAA2I,SAAA;YACA,IACA+H,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,IACAsI,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAyI,QAAA,CAAArK,MAAA,MACA;cACAkK,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAmE,eAAA,CACAmE,OAAA,CAAAvI,KAAA,CAAAC,iBAAA,CAAAyI,QAAA,GACA;YACA;UACA;QACA;UACA;UACA,KAAAzP,SAAA,GAAAoP,OAAA;UACA,KAAAxQ,iBAAA;QACA;MACA;IACA;IACA4Q,aAAA,WAAAA,cAAA;MAAA,IAAAE,OAAA;MAAA,WAAAzN,kBAAA,CAAApG,OAAA,mBAAAqG,oBAAA,CAAArG,OAAA,IAAAsG,IAAA,UAAAwN,SAAA;QAAA,IAAAC,GAAA,EAAAC,gBAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,IAAA,EAAAC,EAAA,EAAAC,aAAA;QAAA,WAAAhO,oBAAA,CAAArG,OAAA,IAAAyG,IAAA,UAAA6N,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3N,IAAA,GAAA2N,SAAA,CAAA1N,IAAA;YAAA;cAAA,IACAgN,OAAA,CAAAzP,cAAA;gBAAAmQ,SAAA,CAAA1N,IAAA;gBAAA;cAAA;cACAgN,OAAA,CAAAW,OAAA;cAAA,OAAAD,SAAA,CAAAE,MAAA;YAAA;cAAAF,SAAA,CAAA3N,IAAA;cAAA2N,SAAA,CAAA1N,IAAA;cAAA,OAKAgN,OAAA,CAAAnI,SAAA;YAAA;cACAqI,GAAA,GAAAF,OAAA,CAAA3I,KAAA,CAAAwJ,OAAA;cAAA,IACAX,GAAA;gBAAAQ,SAAA,CAAA1N,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA8N,KAAA;YAAA;cAGA;cACAZ,GAAA,CAAAa,SAAA;;cAEA;cACAZ,gBAAA,GAAAH,OAAA,CAAAzP,cAAA,CACA4K,OAAA;cAAA,CACAA,OAAA;cAEAiF,WAAA,OAAAY,uBAAA;cAAAX,qBAAA,GACAD,WAAA,CAAAa,SAAA,CAAAd,gBAAA,GAAAG,IAAA,GAAAD,qBAAA,CAAAC,IAAA,EAEA;cACAC,EAAA,GAAAW,oBAAA,CAAAC,MAAA,CACAjB,GAAA,EACA;gBACAkB,OAAA;gBACAC,QAAA;gBACAC,aAAA;gBACAC,eAAA;gBACAC,iBAAA;gBACAC,QAAA;gBACAC,KAAA,WAAAA,MAAAC,IAAA;kBACA,IAAAC,MAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;kBACA;kBACA,OAAAA,MAAA,CAAAD,IAAA,CAAAE,KAAA;gBACA;gBACAC,QAAA,WAAAA,SAAAH,IAAA;kBACA,IAAAI,KAAA;oBACA;oBACA;oBACA;kBACA;kBACA,OACAA,KAAA,CAAAJ,IAAA,CAAAE,KAAA,KACA;gBAEA;gBACAG,QAAA;gBACAC,kBAAA;gBACAC,IAAA;gBACAC,GAAA;gBACAC,SAAA;gBACAC,SAAA,WAAAA,UAAAV,IAAA;kBAAA,aAAAA,IAAA,CAAAE,KAAA;gBAAA;gBACAS,SAAA,WAAAA,UAAAX,IAAA;kBACA,IAAAC,MAAA;oBACA;oBAAA;oBACA;oBAAA;oBACA;kBACA;kBACA,OAAAA,MAAA,CAAAD,IAAA,CAAAE,KAAA;gBACA;cACA,GACAvB,IACA,GAEA;cACAiC,UAAA;gBACAhC,EAAA,CAAAiC,GAAA;;gBAEA;gBACA,IAAAC,QAAA;gBACA,IAAAC,SAAA,GAAAnC,EAAA,CAAAoC,KAAA;kBAAAC,IAAA,GAAAF,SAAA,CAAAE,IAAA;kBAAAC,IAAA,GAAAH,SAAA,CAAAG,IAAA;kBAAAC,IAAA,GAAAJ,SAAA,CAAAI,IAAA;kBAAAC,IAAA,GAAAL,SAAA,CAAAK,IAAA;gBACA,IAAAC,KAAA,GAAAH,IAAA,GAAAD,IAAA;gBACA,IAAA/R,MAAA,GAAAkS,IAAA,GAAAD,IAAA;gBACA,IAAAG,cAAA,GAAA/C,GAAA,CAAAgD,WAAA;gBACA,IAAAC,eAAA,GAAAjD,GAAA,CAAAzH,YAAA;;gBAEA;gBACA,IAAA2K,KAAA,GAAAxJ,IAAA,CAAAyJ,GAAA,CACAJ,cAAA,GAAAD,KAAA,GAAAP,QAAA,EACAU,eAAA,GAAAtS,MAAA,GAAA4R,QACA;;gBAEA;gBACAlC,EAAA,CAAA+C,OAAA,CAAAhD,IAAA;kBACAiD,YAAA,EAAAH,KAAA;kBACAI,eAAA,GACA,CAAAP,cAAA,GAAAD,KAAA,GAAAI,KAAA,OACA,CAAAD,eAAA,GAAAtS,MAAA,GAAAuS,KAAA;gBAEA;cACA;;cAEA;cACA5C,aAAA,YAAAA,cAAA;gBAAA,OAAAD,EAAA,CAAAiC,GAAA;cAAA;cACApF,MAAA,CAAAqG,gBAAA,WAAAjD,aAAA;;cAEA;cACAR,OAAA,CAAA0D,KAAA;gBACAtG,MAAA,CAAAuG,mBAAA,WAAAnD,aAAA;cACA;cAAAE,SAAA,CAAA1N,IAAA;cAAA;YAAA;cAAA0N,SAAA,CAAA3N,IAAA;cAAA2N,SAAA,CAAApN,EAAA,GAAAoN,SAAA;cAEAvP,OAAA,CAAAG,KAAA,6BAAAoP,SAAA,CAAApN,EAAA;cACA0M,OAAA,CAAA4D,QAAA,CAAAtS,KAAA;YAAA;cAAAoP,SAAA,CAAA3N,IAAA;cAEAiN,OAAA,CAAAW,OAAA;cAAA,OAAAD,SAAA,CAAAmD,MAAA;YAAA;YAAA;cAAA,OAAAnD,SAAA,CAAAnN,IAAA;UAAA;QAAA,GAAA0M,QAAA;MAAA;IAEA;IACA;IACA6D,QAAA,WAAAA,SAAA;MACA1G,MAAA,CAAAC,IAAA,MAAA7M,OAAA;IACA;EACA;AACA", "ignoreList": []}]}