{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue", "mtime": 1753857010226}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_baar3DEcharts", "_interopRequireDefault", "require", "_sanhao", "name", "components", "baar3DEcharts", "props", "visible", "type", "Boolean", "default", "title", "String", "closeOnClickMask", "width", "Number", "sccenId", "data", "reportData", "queryParams", "pageNum", "pageSize", "total", "reportData1", "queryParams1", "total1", "show", "loading", "loading1", "isFullscreen", "mounted", "document", "addEventListener", "handleKeydown", "window", "handleWindowResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "watch", "handler", "newVal", "getList", "getList1", "methods", "_this", "technicalReportList", "_objectSpread2", "projectSn", "screenSn", "columnSn", "isForeign", "then", "res", "rows", "_this2", "openHotTechnologyDetails", "$emit", "closeDialog", "handleMaskClick", "handleScreen", "$nextTick", "setTimeout", "event", "key"], "sources": ["src/views/bigScreenSanhao/secondLevel/comparisonChart.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"visible\" class=\"custom-dialog-mask\" @click=\"handleMaskClick\">\r\n    <div\r\n      v-show=\"show\"\r\n      class=\"custom-dialog\"\r\n      :class=\"{ 'comparison-chart-fullscreen': isFullscreen }\"\r\n      :style=\"isFullscreen ? {} : { width: width + 'px' }\"\r\n      @click.stop\r\n    >\r\n      <div class=\"custom-dialog-header\">\r\n        <span>{{ title }}</span>\r\n        <div style=\"display: flex; align-items: center\">\r\n          <div\r\n            @click=\"handleScreen\"\r\n            :title=\"isFullscreen ? '退出全屏' : '全屏'\"\r\n            style=\"\r\n              margin-right: 20px;\r\n              cursor: pointer;\r\n              color: #ffffff;\r\n              font-size: 20px;\r\n            \"\r\n          >\r\n            <i\r\n              :class=\"isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'\"\r\n              style=\"width: 20px; height: 20px\"\r\n            ></i>\r\n          </div>\r\n          <div class=\"custom-dialog-close\" @click=\"closeDialog\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"custom-dialog-body\">\r\n        <div class=\"bg-box\" v-loading=\"loading\">\r\n          <div class=\"bg-box-title\">国内技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total > 0\"\r\n              :total=\"total\"\r\n              :page.sync=\"queryParams.pageNum\"\r\n              :limit.sync=\"queryParams.pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\" v-loading=\"loading1\">\r\n          <div class=\"bg-box-title\">国外技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData1\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams1.pageNum - 1) * queryParams1.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total1 > 0\"\r\n              :total=\"total1\"\r\n              :page.sync=\"queryParams1.pageNum\"\r\n              :limit.sync=\"queryParams1.pageSize\"\r\n              @pagination=\"getList1\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国内热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"1\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国外热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"0\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport baar3DEcharts from \"../components/baar3DEcharts\";\r\nimport { technicalReportList } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"ComparisonChart\",\r\n  components: {\r\n    baar3DEcharts,\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"自定义弹窗\",\r\n    },\r\n    closeOnClickMask: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    width: {\r\n      type: Number,\r\n      default: 1280,\r\n    },\r\n    sccenId: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      reportData: [{ name: \"1\" }],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total: 0,\r\n      reportData1: [{ name: \"1\" }],\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      show: false,\r\n      loading: true,\r\n      loading1: true,\r\n      // 全屏状态\r\n      isFullscreen: false,\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 添加ESC键监听\r\n    document.addEventListener(\"keydown\", this.handleKeydown);\r\n    // 添加窗口大小变化监听\r\n    window.addEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除ESC键监听\r\n    document.removeEventListener(\"keydown\", this.handleKeydown);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  watch: {\r\n    visible: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          // 重置全屏状态\r\n          this.isFullscreen = false;\r\n          this.queryParams = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.queryParams1 = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.show = false;\r\n          this.getList();\r\n          this.getList1();\r\n        }\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      technicalReportList({\r\n        ...this.queryParams,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 1,\r\n      }).then((res) => {\r\n        this.reportData = res.rows;\r\n        this.total = res.total;\r\n        this.show = true;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    getList1() {\r\n      this.loading1 = true;\r\n      technicalReportList({\r\n        ...this.queryParams1,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 0,\r\n      }).then((res) => {\r\n        this.reportData1 = res.rows;\r\n        this.total1 = res.total;\r\n        this.show = true;\r\n        this.loading1 = false;\r\n      });\r\n    },\r\n\r\n    openHotTechnologyDetails(data) {\r\n      this.$emit(\"openHotTechnology\", data);\r\n    },\r\n\r\n    // 关闭弹窗的方法\r\n    closeDialog() {\r\n      this.$emit(\"update:visible\", false);\r\n    },\r\n\r\n    // 处理遮罩层点击事件\r\n    handleMaskClick() {\r\n      if (this.closeOnClickMask) {\r\n        this.closeDialog();\r\n      }\r\n    },\r\n\r\n    // 全屏切换\r\n    handleScreen() {\r\n      this.isFullscreen = !this.isFullscreen;\r\n\r\n      // 延迟调整图表大小，确保DOM更新完成\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          // 如果有图表组件，调整其大小\r\n          // 这里可以根据实际的图表组件引用进行调整\r\n        }, 300); // 等待CSS动画完成\r\n      });\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeydown(event) {\r\n      // 按ESC键退出全屏\r\n      if (event.key === \"Escape\" && this.isFullscreen && this.visible) {\r\n        this.isFullscreen = false;\r\n      }\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleWindowResize() {\r\n      if (this.isFullscreen) {\r\n        // 重新调整图表大小\r\n        // 这里可以根据实际的图表组件引用进行调整\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.custom-dialog-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n\r\n  // 确保在所有分辨率下都能正确覆盖\r\n  min-width: 100%;\r\n  min-height: 100%;\r\n  overflow: hidden;\r\n\r\n  .custom-dialog {\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    width: 500px;\r\n    border: 10px solid;\r\n    border-right-width: 5px;\r\n    border-left-width: 5px;\r\n    border-image: url(\"../../../assets/bigScreenSanhao/dialogBg.png\") 27 round;\r\n    background-color: #000000d0;\r\n    padding-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.comparison-chart-fullscreen {\r\n      width: calc(100vw - 40px) !important;\r\n      height: calc(100vh - 40px) !important;\r\n      max-width: none !important;\r\n      max-height: none !important;\r\n      margin: 0 !important;\r\n      // 确保在所有分辨率下都能正确显示\r\n      min-width: calc(100% - 40px) !important;\r\n      min-height: calc(100% - 40px) !important;\r\n\r\n      .custom-dialog-body {\r\n        height: calc(100% - 80px); // 减去header高度和padding\r\n        max-height: calc(100% - 80px);\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0 20px 0 5%;\r\n      margin: 10px -3px 20px;\r\n      background-image: url(\"../../../assets/bigScreenSanhao/dialogTitle.png\");\r\n      background-size: 100% 100%;\r\n      height: 50px;\r\n      font-weight: 600;\r\n      font-size: 22px;\r\n      color: #ffffff;\r\n      line-height: 50px;\r\n\r\n      span {\r\n        padding-right: 10px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .custom-dialog-close {\r\n        width: 20px;\r\n        height: 20px;\r\n        background-image: url(\"../../../assets/bigScreenSanhao/dialogClose.png\");\r\n        background-size: 100% 100%;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-body {\r\n      height: 800px;\r\n      overflow-y: auto;\r\n      overflow-x: hidden;\r\n      padding: 0px 20px 0px;\r\n\r\n      .bg-box {\r\n        background: #1b283b;\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 8px 16px 16px;\r\n        margin-bottom: 20px;\r\n\r\n        .bg-box-title {\r\n          font-weight: 800;\r\n          font-size: 18px;\r\n          color: #ffffff;\r\n          height: 30px;\r\n          line-height: 30px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .bg-box-content {\r\n          font-size: 16px;\r\n\r\n          .bg-box-content-list {\r\n            padding: 0 0 10px 15px;\r\n            font-size: 14px;\r\n            color: #ffffffc6;\r\n\r\n            span {\r\n              font-weight: 600;\r\n              color: #ffffff;\r\n              margin-right: 10px;\r\n            }\r\n          }\r\n\r\n          ::v-deep .el-table tr {\r\n            background-color: #1f3850ff !important;\r\n          }\r\n\r\n          ::v-deep .el-table__inner-wrapper:before {\r\n            height: 0px;\r\n            border-color: #00000000;\r\n          }\r\n\r\n          ::v-deep .el-table__header th {\r\n            background-color: #1f3850 !important;\r\n            color: rgba(255, 255, 255);\r\n            font-size: 16px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body td {\r\n            background-color: #1d3046;\r\n            color: rgba(255, 255, 255, 0.9);\r\n            font-size: 14px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body tr:hover > td {\r\n            background-color: #132f56 !important;\r\n          }\r\n        }\r\n\r\n        .content-flex {\r\n          display: flex;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .pagination-container {\r\n  background-color: #2a304000;\r\n  color: #f2f2f2;\r\n  height: 55px;\r\n  margin: 0;\r\n\r\n  .el-select .el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n\r\n  .el-pagination__editor.el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n::v-deep .el-pagination__total,\r\n::v-deep .el-pagination__jump {\r\n  color: #f2f2f2;\r\n}\r\n\r\n::v-deep .el-pagination .btn-prev,\r\n::v-deep .el-pagination .btn-next,\r\n::v-deep .el-pagination button:disabled {\r\n  background-color: #ffffff00;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .el-pager li {\r\n  background: #ffffff00;\r\n  color: #fff;\r\n\r\n  &.active {\r\n    color: #1890ff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA+LA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,gBAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAI,KAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAM,OAAA;MACAR,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;EACA;EACAO,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;QAAAf,IAAA;MAAA;MACAgB,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,WAAA;QAAApB,IAAA;MAAA;MACAqB,YAAA;QACAJ,OAAA;QACAC,QAAA;MACA;MACAI,MAAA;MACAC,IAAA;MACAC,OAAA;MACAC,QAAA;MACA;MACAC,YAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA;IACAC,QAAA,CAAAC,gBAAA,iBAAAC,aAAA;IACA;IACAC,MAAA,CAAAF,gBAAA,gBAAAG,kBAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACAL,QAAA,CAAAM,mBAAA,iBAAAJ,aAAA;IACA;IACAC,MAAA,CAAAG,mBAAA,gBAAAF,kBAAA;EACA;EAEAG,KAAA;IACA/B,OAAA;MACAgC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA;UACA;UACA,KAAAX,YAAA;UACA,KAAAV,WAAA;YACAC,OAAA;YACAC,QAAA;UACA;UACA,KAAAG,YAAA;YACAJ,OAAA;YACAC,QAAA;UACA;UACA,KAAAK,IAAA;UACA,KAAAe,OAAA;UACA,KAAAC,QAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,KAAAjB,OAAA;MACA,IAAAkB,2BAAA,MAAAC,cAAA,CAAApC,OAAA,MAAAoC,cAAA,CAAApC,OAAA,MACA,KAAAS,WAAA;QACA4B,SAAA;QACAC,QAAA,OAAAhC,OAAA;QACAiC,QAAA;QACAC,SAAA;MAAA,EACA,EAAAC,IAAA,WAAAC,GAAA;QACAR,KAAA,CAAA1B,UAAA,GAAAkC,GAAA,CAAAC,IAAA;QACAT,KAAA,CAAAtB,KAAA,GAAA8B,GAAA,CAAA9B,KAAA;QACAsB,KAAA,CAAAlB,IAAA;QACAkB,KAAA,CAAAjB,OAAA;MACA;IACA;IAEAe,QAAA,WAAAA,SAAA;MAAA,IAAAY,MAAA;MACA,KAAA1B,QAAA;MACA,IAAAiB,2BAAA,MAAAC,cAAA,CAAApC,OAAA,MAAAoC,cAAA,CAAApC,OAAA,MACA,KAAAc,YAAA;QACAuB,SAAA;QACAC,QAAA,OAAAhC,OAAA;QACAiC,QAAA;QACAC,SAAA;MAAA,EACA,EAAAC,IAAA,WAAAC,GAAA;QACAE,MAAA,CAAA/B,WAAA,GAAA6B,GAAA,CAAAC,IAAA;QACAC,MAAA,CAAA7B,MAAA,GAAA2B,GAAA,CAAA9B,KAAA;QACAgC,MAAA,CAAA5B,IAAA;QACA4B,MAAA,CAAA1B,QAAA;MACA;IACA;IAEA2B,wBAAA,WAAAA,yBAAAtC,IAAA;MACA,KAAAuC,KAAA,sBAAAvC,IAAA;IACA;IAEA;IACAwC,WAAA,WAAAA,YAAA;MACA,KAAAD,KAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACA,SAAA7C,gBAAA;QACA,KAAA4C,WAAA;MACA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAA9B,YAAA,SAAAA,YAAA;;MAEA;MACA,KAAA+B,SAAA;QACAC,UAAA;UACA;UACA;QAAA,CACA;MACA;IACA;IAEA;IACA5B,aAAA,WAAAA,cAAA6B,KAAA;MACA;MACA,IAAAA,KAAA,CAAAC,GAAA,sBAAAlC,YAAA,SAAAtB,OAAA;QACA,KAAAsB,YAAA;MACA;IACA;IAEA;IACAM,kBAAA,WAAAA,mBAAA;MACA,SAAAN,YAAA;QACA;QACA;MAAA;IAEA;EACA;AACA", "ignoreList": []}]}