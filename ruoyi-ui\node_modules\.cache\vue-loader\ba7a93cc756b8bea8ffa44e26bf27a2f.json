{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\secondLevel\\comparisonChart.vue", "mtime": 1753857010226}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYmFhcjNERWNoYXJ0cyBmcm9tICIuLi9jb21wb25lbnRzL2JhYXIzREVjaGFydHMiOw0KaW1wb3J0IHsgdGVjaG5pY2FsUmVwb3J0TGlzdCB9IGZyb20gIkAvYXBpL2JpZ1NjcmVlbi9zYW5oYW8uanMiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDb21wYXJpc29uQ2hhcnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgYmFhcjNERWNoYXJ0cywNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICB2aXNpYmxlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgICB0aXRsZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogIuiHquWumuS5ieW8ueeqlyIsDQogICAgfSwNCiAgICBjbG9zZU9uQ2xpY2tNYXNrOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgICB3aWR0aDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMTI4MCwNCiAgICB9LA0KICAgIHNjY2VuSWQ6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDEsDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcmVwb3J0RGF0YTogW3sgbmFtZTogIjEiIH1dLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgcmVwb3J0RGF0YTE6IFt7IG5hbWU6ICIxIiB9XSwNCiAgICAgIHF1ZXJ5UGFyYW1zMTogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9LA0KICAgICAgdG90YWwxOiAwLA0KICAgICAgc2hvdzogZmFsc2UsDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgbG9hZGluZzE6IHRydWUsDQogICAgICAvLyDlhajlsY/nirbmgIENCiAgICAgIGlzRnVsbHNjcmVlbjogZmFsc2UsDQogICAgfTsNCiAgfSwNCg0KICBtb3VudGVkKCkgew0KICAgIC8vIOa3u+WKoEVTQ+mUruebkeWQrA0KICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoImtleWRvd24iLCB0aGlzLmhhbmRsZUtleWRvd24pOw0KICAgIC8vIOa3u+WKoOeql+WPo+Wkp+Wwj+WPmOWMluebkeWQrA0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCJyZXNpemUiLCB0aGlzLmhhbmRsZVdpbmRvd1Jlc2l6ZSk7DQogIH0sDQoNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDnp7vpmaRFU0PplK7nm5HlkKwNCiAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCJrZXlkb3duIiwgdGhpcy5oYW5kbGVLZXlkb3duKTsNCiAgICAvLyDnp7vpmaTnqpflj6PlpKflsI/lj5jljJbnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigicmVzaXplIiwgdGhpcy5oYW5kbGVXaW5kb3dSZXNpemUpOw0KICB9LA0KDQogIHdhdGNoOiB7DQogICAgdmlzaWJsZTogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCkgew0KICAgICAgICAgIC8vIOmHjee9ruWFqOWxj+eKtuaAgQ0KICAgICAgICAgIHRoaXMuaXNGdWxsc2NyZWVuID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgfTsNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zMSA9IHsNCiAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgICAgfTsNCiAgICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLmdldExpc3QxKCk7DQogICAgICAgIH0NCiAgICAgIH0sDQogICAgfSwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgdGVjaG5pY2FsUmVwb3J0TGlzdCh7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMsDQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogdGhpcy5zY2NlbklkLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgICBpc0ZvcmVpZ246IDEsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yZXBvcnREYXRhID0gcmVzLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWw7DQogICAgICAgIHRoaXMuc2hvdyA9IHRydWU7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldExpc3QxKCkgew0KICAgICAgdGhpcy5sb2FkaW5nMSA9IHRydWU7DQogICAgICB0ZWNobmljYWxSZXBvcnRMaXN0KHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtczEsDQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogdGhpcy5zY2NlbklkLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgICBpc0ZvcmVpZ246IDAsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yZXBvcnREYXRhMSA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsMSA9IHJlcy50b3RhbDsNCiAgICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsNCiAgICAgICAgdGhpcy5sb2FkaW5nMSA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIG9wZW5Ib3RUZWNobm9sb2d5RGV0YWlscyhkYXRhKSB7DQogICAgICB0aGlzLiRlbWl0KCJvcGVuSG90VGVjaG5vbG9neSIsIGRhdGEpOw0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63lvLnnqpfnmoTmlrnms5UNCiAgICBjbG9zZURpYWxvZygpIHsNCiAgICAgIHRoaXMuJGVtaXQoInVwZGF0ZTp2aXNpYmxlIiwgZmFsc2UpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbpga7nvanlsYLngrnlh7vkuovku7YNCiAgICBoYW5kbGVNYXNrQ2xpY2soKSB7DQogICAgICBpZiAodGhpcy5jbG9zZU9uQ2xpY2tNYXNrKSB7DQogICAgICAgIHRoaXMuY2xvc2VEaWFsb2coKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YWo5bGP5YiH5o2iDQogICAgaGFuZGxlU2NyZWVuKCkgew0KICAgICAgdGhpcy5pc0Z1bGxzY3JlZW4gPSAhdGhpcy5pc0Z1bGxzY3JlZW47DQoNCiAgICAgIC8vIOW7tui/n+iwg+aVtOWbvuihqOWkp+Wwj++8jOehruS/nURPTeabtOaWsOWujOaIkA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAvLyDlpoLmnpzmnInlm77ooajnu4Tku7bvvIzosIPmlbTlhbblpKflsI8NCiAgICAgICAgICAvLyDov5nph4zlj6/ku6XmoLnmja7lrp7pmYXnmoTlm77ooajnu4Tku7blvJXnlKjov5vooYzosIPmlbQNCiAgICAgICAgfSwgMzAwKTsgLy8g562J5b6FQ1NT5Yqo55S75a6M5oiQDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g5aSE55CG6ZSu55uY5LqL5Lu2DQogICAgaGFuZGxlS2V5ZG93bihldmVudCkgew0KICAgICAgLy8g5oyJRVND6ZSu6YCA5Ye65YWo5bGPDQogICAgICBpZiAoZXZlbnQua2V5ID09PSAiRXNjYXBlIiAmJiB0aGlzLmlzRnVsbHNjcmVlbiAmJiB0aGlzLnZpc2libGUpIHsNCiAgICAgICAgdGhpcy5pc0Z1bGxzY3JlZW4gPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aSE55CG56qX5Y+j5aSn5bCP5Y+Y5YyWDQogICAgaGFuZGxlV2luZG93UmVzaXplKCkgew0KICAgICAgaWYgKHRoaXMuaXNGdWxsc2NyZWVuKSB7DQogICAgICAgIC8vIOmHjeaWsOiwg+aVtOWbvuihqOWkp+Wwjw0KICAgICAgICAvLyDov5nph4zlj6/ku6XmoLnmja7lrp7pmYXnmoTlm77ooajnu4Tku7blvJXnlKjov5vooYzosIPmlbQNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["comparisonChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+LA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "comparisonChart.vue", "sourceRoot": "src/views/bigScreenSanhao/secondLevel", "sourcesContent": ["<template>\r\n  <div v-if=\"visible\" class=\"custom-dialog-mask\" @click=\"handleMaskClick\">\r\n    <div\r\n      v-show=\"show\"\r\n      class=\"custom-dialog\"\r\n      :class=\"{ 'comparison-chart-fullscreen': isFullscreen }\"\r\n      :style=\"isFullscreen ? {} : { width: width + 'px' }\"\r\n      @click.stop\r\n    >\r\n      <div class=\"custom-dialog-header\">\r\n        <span>{{ title }}</span>\r\n        <div style=\"display: flex; align-items: center\">\r\n          <div\r\n            @click=\"handleScreen\"\r\n            :title=\"isFullscreen ? '退出全屏' : '全屏'\"\r\n            style=\"\r\n              margin-right: 20px;\r\n              cursor: pointer;\r\n              color: #ffffff;\r\n              font-size: 20px;\r\n            \"\r\n          >\r\n            <i\r\n              :class=\"isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'\"\r\n              style=\"width: 20px; height: 20px\"\r\n            ></i>\r\n          </div>\r\n          <div class=\"custom-dialog-close\" @click=\"closeDialog\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"custom-dialog-body\">\r\n        <div class=\"bg-box\" v-loading=\"loading\">\r\n          <div class=\"bg-box-title\">国内技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams.pageNum - 1) * queryParams.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total > 0\"\r\n              :total=\"total\"\r\n              :page.sync=\"queryParams.pageNum\"\r\n              :limit.sync=\"queryParams.pageSize\"\r\n              @pagination=\"getList\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\" v-loading=\"loading1\">\r\n          <div class=\"bg-box-title\">国外技术报告</div>\r\n          <div class=\"bg-box-content\">\r\n            <el-table\r\n              :data=\"reportData1\"\r\n              style=\"\r\n                width: calc(100% - 20px);\r\n                margin: 0 10px;\r\n                background-color: #00000000;\r\n              \"\r\n            >\r\n              <el-table-column\r\n                prop=\"reportSn\"\r\n                label=\"序号\"\r\n                width=\"60\"\r\n                align=\"center\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.$index +\r\n                    1 +\r\n                    (queryParams1.pageNum - 1) * queryParams1.pageSize\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishTime\"\r\n                label=\"发布时间\"\r\n                width=\"110\"\r\n                align=\"center\"\r\n              />\r\n              <el-table-column\r\n                prop=\"reportName\"\r\n                label=\"报告名称\"\r\n                show-overflow-tooltip\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <div\r\n                    style=\"color: #0ec2f4ff; cursor: pointer\"\r\n                    @click=\"\r\n                      openHotTechnologyDetails({\r\n                        ...scope.row,\r\n                        title: scope.row.reportName,\r\n                      })\r\n                    \"\r\n                  >\r\n                    {{ scope.row.reportName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"publishUnit\"\r\n                label=\"发布机构\"\r\n                width=\"240\"\r\n                show-overflow-tooltip\r\n              />\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total1 > 0\"\r\n              :total=\"total1\"\r\n              :page.sync=\"queryParams1.pageNum\"\r\n              :limit.sync=\"queryParams1.pageSize\"\r\n              @pagination=\"getList1\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国内热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"1\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n        <div class=\"bg-box\">\r\n          <div class=\"bg-box-title\">国外热点技术</div>\r\n          <div class=\"bg-box-content content-flex\">\r\n            <baar3DEcharts\r\n              :show=\"show\"\r\n              :sccenId=\"sccenId\"\r\n              :type=\"0\"\r\n              style=\"width: 1200px; height: 500px\"\r\n            ></baar3DEcharts>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport baar3DEcharts from \"../components/baar3DEcharts\";\r\nimport { technicalReportList } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"ComparisonChart\",\r\n  components: {\r\n    baar3DEcharts,\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"自定义弹窗\",\r\n    },\r\n    closeOnClickMask: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    width: {\r\n      type: Number,\r\n      default: 1280,\r\n    },\r\n    sccenId: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      reportData: [{ name: \"1\" }],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total: 0,\r\n      reportData1: [{ name: \"1\" }],\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      show: false,\r\n      loading: true,\r\n      loading1: true,\r\n      // 全屏状态\r\n      isFullscreen: false,\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    // 添加ESC键监听\r\n    document.addEventListener(\"keydown\", this.handleKeydown);\r\n    // 添加窗口大小变化监听\r\n    window.addEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除ESC键监听\r\n    document.removeEventListener(\"keydown\", this.handleKeydown);\r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleWindowResize);\r\n  },\r\n\r\n  watch: {\r\n    visible: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          // 重置全屏状态\r\n          this.isFullscreen = false;\r\n          this.queryParams = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.queryParams1 = {\r\n            pageNum: 1,\r\n            pageSize: 10,\r\n          };\r\n          this.show = false;\r\n          this.getList();\r\n          this.getList1();\r\n        }\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      technicalReportList({\r\n        ...this.queryParams,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 1,\r\n      }).then((res) => {\r\n        this.reportData = res.rows;\r\n        this.total = res.total;\r\n        this.show = true;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    getList1() {\r\n      this.loading1 = true;\r\n      technicalReportList({\r\n        ...this.queryParams1,\r\n        projectSn: \"1\",\r\n        screenSn: this.sccenId,\r\n        columnSn: \"1\",\r\n        isForeign: 0,\r\n      }).then((res) => {\r\n        this.reportData1 = res.rows;\r\n        this.total1 = res.total;\r\n        this.show = true;\r\n        this.loading1 = false;\r\n      });\r\n    },\r\n\r\n    openHotTechnologyDetails(data) {\r\n      this.$emit(\"openHotTechnology\", data);\r\n    },\r\n\r\n    // 关闭弹窗的方法\r\n    closeDialog() {\r\n      this.$emit(\"update:visible\", false);\r\n    },\r\n\r\n    // 处理遮罩层点击事件\r\n    handleMaskClick() {\r\n      if (this.closeOnClickMask) {\r\n        this.closeDialog();\r\n      }\r\n    },\r\n\r\n    // 全屏切换\r\n    handleScreen() {\r\n      this.isFullscreen = !this.isFullscreen;\r\n\r\n      // 延迟调整图表大小，确保DOM更新完成\r\n      this.$nextTick(() => {\r\n        setTimeout(() => {\r\n          // 如果有图表组件，调整其大小\r\n          // 这里可以根据实际的图表组件引用进行调整\r\n        }, 300); // 等待CSS动画完成\r\n      });\r\n    },\r\n\r\n    // 处理键盘事件\r\n    handleKeydown(event) {\r\n      // 按ESC键退出全屏\r\n      if (event.key === \"Escape\" && this.isFullscreen && this.visible) {\r\n        this.isFullscreen = false;\r\n      }\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleWindowResize() {\r\n      if (this.isFullscreen) {\r\n        // 重新调整图表大小\r\n        // 这里可以根据实际的图表组件引用进行调整\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.custom-dialog-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n\r\n  // 确保在所有分辨率下都能正确覆盖\r\n  min-width: 100%;\r\n  min-height: 100%;\r\n  overflow: hidden;\r\n\r\n  .custom-dialog {\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    width: 500px;\r\n    border: 10px solid;\r\n    border-right-width: 5px;\r\n    border-left-width: 5px;\r\n    border-image: url(\"../../../assets/bigScreenSanhao/dialogBg.png\") 27 round;\r\n    background-color: #000000d0;\r\n    padding-bottom: 20px;\r\n    transition: all 0.3s ease;\r\n\r\n    &.comparison-chart-fullscreen {\r\n      width: calc(100vw - 40px) !important;\r\n      height: calc(100vh - 40px) !important;\r\n      max-width: none !important;\r\n      max-height: none !important;\r\n      margin: 0 !important;\r\n      // 确保在所有分辨率下都能正确显示\r\n      min-width: calc(100% - 40px) !important;\r\n      min-height: calc(100% - 40px) !important;\r\n\r\n      .custom-dialog-body {\r\n        height: calc(100% - 80px); // 减去header高度和padding\r\n        max-height: calc(100% - 80px);\r\n        overflow-y: auto;\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 0 20px 0 5%;\r\n      margin: 10px -3px 20px;\r\n      background-image: url(\"../../../assets/bigScreenSanhao/dialogTitle.png\");\r\n      background-size: 100% 100%;\r\n      height: 50px;\r\n      font-weight: 600;\r\n      font-size: 22px;\r\n      color: #ffffff;\r\n      line-height: 50px;\r\n\r\n      span {\r\n        padding-right: 10px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .custom-dialog-close {\r\n        width: 20px;\r\n        height: 20px;\r\n        background-image: url(\"../../../assets/bigScreenSanhao/dialogClose.png\");\r\n        background-size: 100% 100%;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n\r\n    .custom-dialog-body {\r\n      height: 800px;\r\n      overflow-y: auto;\r\n      overflow-x: hidden;\r\n      padding: 0px 20px 0px;\r\n\r\n      .bg-box {\r\n        background: #1b283b;\r\n        border-radius: 8px 8px 8px 8px;\r\n        padding: 8px 16px 16px;\r\n        margin-bottom: 20px;\r\n\r\n        .bg-box-title {\r\n          font-weight: 800;\r\n          font-size: 18px;\r\n          color: #ffffff;\r\n          height: 30px;\r\n          line-height: 30px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .bg-box-content {\r\n          font-size: 16px;\r\n\r\n          .bg-box-content-list {\r\n            padding: 0 0 10px 15px;\r\n            font-size: 14px;\r\n            color: #ffffffc6;\r\n\r\n            span {\r\n              font-weight: 600;\r\n              color: #ffffff;\r\n              margin-right: 10px;\r\n            }\r\n          }\r\n\r\n          ::v-deep .el-table tr {\r\n            background-color: #1f3850ff !important;\r\n          }\r\n\r\n          ::v-deep .el-table__inner-wrapper:before {\r\n            height: 0px;\r\n            border-color: #00000000;\r\n          }\r\n\r\n          ::v-deep .el-table__header th {\r\n            background-color: #1f3850 !important;\r\n            color: rgba(255, 255, 255);\r\n            font-size: 16px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body td {\r\n            background-color: #1d3046;\r\n            color: rgba(255, 255, 255, 0.9);\r\n            font-size: 14px;\r\n            border-bottom-width: 0px;\r\n          }\r\n\r\n          ::v-deep .el-table__body tr:hover > td {\r\n            background-color: #132f56 !important;\r\n          }\r\n        }\r\n\r\n        .content-flex {\r\n          display: flex;\r\n          justify-content: center;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .pagination-container {\r\n  background-color: #2a304000;\r\n  color: #f2f2f2;\r\n  height: 55px;\r\n  margin: 0;\r\n\r\n  .el-select .el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n\r\n  .el-pagination__editor.el-input .el-input__inner {\r\n    background: #2a304000;\r\n    border-color: #ffffff;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n::v-deep .el-pagination__total,\r\n::v-deep .el-pagination__jump {\r\n  color: #f2f2f2;\r\n}\r\n\r\n::v-deep .el-pagination .btn-prev,\r\n::v-deep .el-pagination .btn-next,\r\n::v-deep .el-pagination button:disabled {\r\n  background-color: #ffffff00;\r\n  color: #fff;\r\n}\r\n\r\n::v-deep .el-pager li {\r\n  background: #ffffff00;\r\n  color: #fff;\r\n\r\n  &.active {\r\n    color: #1890ff;\r\n  }\r\n}\r\n</style>\r\n"]}]}