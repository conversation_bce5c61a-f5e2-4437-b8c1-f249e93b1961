{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=style&index=0&id=6cd40d78&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1753863308265}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudHdvIHsNCiAgaGVpZ2h0OiAxMDAlOw0KICB3aWR0aDogMTAwJTsNCiAgcGFkZGluZy1ib3R0b206IDEwcHg7DQoNCiAgLmxlZnQgew0KICAgIHdpZHRoOiA1MjBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICB9DQoNCiAgLmNlbnRlciB7DQogICAgbWFyZ2luOiAwIDExcHg7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogICAgLmNlbnRlci10b3Agew0KICAgICAgaGVpZ2h0OiAxNTBweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgIC50b3AtY29udGVudCB7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgd2lkdGg6IDMxNXB4Ow0KICAgICAgICBoZWlnaHQ6IDk4cHg7DQogICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby9jZW50ZXJCZzEucG5nIik7DQogICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgfQ0KDQogICAgICAuYmcxIHsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICB0b3A6IDE3cHg7DQogICAgICAgIGxlZnQ6IDQzcHg7DQogICAgICAgIHdpZHRoOiA2MHB4Ow0KICAgICAgICBoZWlnaHQ6IDYwcHg7DQogICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby9jZW50ZXJCZzIucG5nIik7DQogICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgfQ0KDQogICAgICAudG9wLWNvbnRlbnQtbnVtYmVyIHsNCiAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICBjb2xvcjogIzAwZTVmZjsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICBsZWZ0OiAxMzhweDsNCiAgICAgICAgdG9wOiA0NHB4Ow0KICAgICAgfQ0KDQogICAgICAudG9wLWNvbnRlbnQtbmFtZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgbGVmdDogMTM4cHg7DQogICAgICAgIHRvcDogMjJweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAucmlnaHQgew0KICAgIHdpZHRoOiA1MjBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogICAgLmJzQ29udGVudEJveDIgew0KICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi8uLi9hc3NldHMvYmlnU2NyZWVuU2FuaGFvL2NvbnRlbnRCZzEucG5nIik7DQoNCiAgICAgIC5ic0NvbnRlbnRDb250ZW50IHsNCiAgICAgICAgaGVpZ2h0OiBjYWxjKCgxMDAlIC0gNDNweCkgLyAyKTsNCiAgICAgIH0NCg0KICAgICAgLmtlamlkb25ndGFpLWJveCB7DQogICAgICAgIG1hcmdpbi10b3A6IDA7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmJzQ29udGVudEJveCwNCiAgLmJzQ29udGVudEJveDIgew0KICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby9jb250ZW50QmcucG5nIik7DQogICAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7DQoNCiAgICAuYnNDb250ZW50VGl0bGUgew0KICAgICAgaGVpZ2h0OiA0M3B4Ow0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAgIC5ic0NvbnRlbnRUaXRsZUljb24gew0KICAgICAgICB3aWR0aDogMjJweDsNCiAgICAgICAgaGVpZ2h0OiAyMnB4Ow0KICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby90aXRsZUxvZ28ucG5nIik7DQogICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgfQ0KDQogICAgICAuYnNDb250ZW50VGl0bGVOYW1lIHsNCiAgICAgICAgaGVpZ2h0OiA0M3B4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNDNweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDgwMDsNCiAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICBjb2xvcjogIzAwYWJmNDsNCiAgICAgIH0NCg0KICAgICAgLmJzQ29udGVudFRpdGxlSGVscCB7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgd2lkdGg6IDIxcHg7DQogICAgICAgIGhlaWdodDogMjFweDsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby90aXRsZUhlbHAucG5nIik7DQogICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgfQ0KDQogICAgICAuYnNDb250ZW50VGl0bGVNb3JlIHsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICByaWdodDogMTBweDsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICB3aWR0aDogNzBweDsNCiAgICAgICAgaGVpZ2h0OiA0M3B4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNjBweDsNCiAgICAgICAgZm9udC1zaXplOiAxN3B4Ow0KICAgICAgICBjb2xvcjogIzAwYzhmZjsNCg0KICAgICAgICAmOmFmdGVyIHsNCiAgICAgICAgICBjb250ZW50OiAiIjsNCiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgIHRvcDogMjJweDsNCiAgICAgICAgICB3aWR0aDogMThweDsNCiAgICAgICAgICBoZWlnaHQ6IDE4cHg7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDVweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uLy4uL2Fzc2V0cy9iaWdTY3JlZW5TYW5oYW8vamlhbnRvdS5wbmciKTsNCiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5ic0NvbnRlbnRDb250ZW50IHsNCiAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNDNweCk7DQogICAgfQ0KICB9DQoNCiAgLmJzQ29udGVudEJveDEgew0KICAgIGZsZXg6IDE7DQoNCiAgICAuYnNDb250ZW50VGl0bGUxIHsNCiAgICAgIGhlaWdodDogNDNweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi8uLi9hc3NldHMvYmlnU2NyZWVuU2FuaGFvL3RpdGxlMS5wbmciKTsNCiAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAuYnNDb250ZW50VGl0bGVJY29uIHsNCiAgICAgICAgd2lkdGg6IDIycHg7DQogICAgICAgIGhlaWdodDogMjJweDsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uLy4uL2Fzc2V0cy9iaWdTY3JlZW5TYW5oYW8vdGl0bGVMb2dvLnBuZyIpOw0KICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgIH0NCg0KICAgICAgLmJzQ29udGVudFRpdGxlTmFtZSB7DQogICAgICAgIGhlaWdodDogNDNweDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDQzcHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA4MDA7DQogICAgICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICAgICAgY29sb3I6ICMwMGFiZjQ7DQoNCiAgICAgICAgc3BhbiB7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgY29sb3I6IHJnYmEoMCwgMTcxLCAyNDQsIDAuNSk7DQogICAgICAgIH0NCg0KICAgICAgICAudGl0bGVDb2xvciB7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDgwMDsNCiAgICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuYnNDb250ZW50VGl0bGVIZWxwIHsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICB3aWR0aDogMjFweDsNCiAgICAgICAgaGVpZ2h0OiAyMXB4Ow0KICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi8uLi9hc3NldHMvYmlnU2NyZWVuU2FuaGFvL3RpdGxlSGVscC5wbmciKTsNCiAgICAgICAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7DQogICAgICB9DQoNCiAgICAgIC5ic0NvbnRlbnRUaXRsZU1vcmUgew0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIHJpZ2h0OiAxMHB4Ow0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgIHdpZHRoOiA3MHB4Ow0KICAgICAgICBoZWlnaHQ6IDQzcHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KICAgICAgICBmb250LXNpemU6IDE3cHg7DQogICAgICAgIGNvbG9yOiAjMDBjOGZmOw0KDQogICAgICAgICY6YWZ0ZXIgew0KICAgICAgICAgIGNvbnRlbnQ6ICIiOw0KICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgICAgdG9wOiAyMnB4Ow0KICAgICAgICAgIHdpZHRoOiAxOHB4Ow0KICAgICAgICAgIGhlaWdodDogMThweDsNCiAgICAgICAgICBtYXJnaW4tbGVmdDogNXB4Ow0KICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby9qaWFudG91LnBuZyIpOw0KICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmJzQ29udGVudENvbnRlbnQgew0KICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA0M3B4KTsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAudHJ1bXAtdmlldy1jb250YWluZXIgew0KICAgICAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgICAgfQ0KDQogICAgICAudmlldy10cmVlLWNvbnRhaW5lciB7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgICAgICAgJiA+IGRpdiB7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuYnNDb250ZW50VGl0bGVNb3JlIHsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICBib3R0b206IDA7DQogICAgICAgIHJpZ2h0OiAwOw0KICAgICAgICB6LWluZGV4OiA5OTsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICB3aWR0aDogNzBweDsNCiAgICAgICAgaGVpZ2h0OiA0M3B4Ow0KICAgICAgICBsaW5lLWhlaWdodDogNjBweDsNCiAgICAgICAgZm9udC1zaXplOiAxN3B4Ow0KICAgICAgICBjb2xvcjogIzAwYzhmZjsNCg0KICAgICAgICAmOmFmdGVyIHsNCiAgICAgICAgICBjb250ZW50OiAiIjsNCiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgIHRvcDogMjJweDsNCiAgICAgICAgICB3aWR0aDogMThweDsNCiAgICAgICAgICBoZWlnaHQ6IDE4cHg7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDVweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uLy4uL2Fzc2V0cy9iaWdTY3JlZW5TYW5oYW8vamlhbnRvdS5wbmciKTsNCiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyBUYWIg5oyJ6ZKu5qC35byPDQogICAgICAudGFiLWJ1dHRvbnMgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICAgIGdhcDogMTBweDsNCg0KICAgICAgICAudGFiLWJ1dHRvbiB7DQogICAgICAgICAgcGFkZGluZzogOHB4IDhweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDE3MSwgMjQ0LCAwLjIpOw0KICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMTcxLCAyNDQsIDAuNSk7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgIGNvbG9yOiByZ2JhKDAsIDE3MSwgMjQ0LCAwLjgpOw0KICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxNzEsIDI0NCwgMC4zKTsNCiAgICAgICAgICAgIGNvbG9yOiAjMDBhYmY0Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTcxLCAyNDQsIDAuNSk7DQogICAgICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogIzAwYWJmNDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8gVGFiIOWGheWuueagt+W8jw0KICAgICAgLnRhYi1jb250ZW50IHsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCg0KICAgICAgICAudHJ1bXAtdmlldy1jb250YWluZXIgew0KICAgICAgICAgIGhlaWdodDogMzAwcHg7DQogICAgICAgIH0NCg0KICAgICAgICAudmlldy10cmVlLWNvbnRhaW5lciB7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgICAgICAgICAmID4gZGl2IHsNCiAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLm1hcmttYXAtc3ZnIHsNCiAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDng63ngrnmjqjojZDmu5rliqjliJfooajmoLflvI8NCiAgLnJlbWVuZ3dlbnpoYW5nLWJveCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNiwgMjE2LCAyNTUsIDAuNCk7DQogICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjE1KTsNCiAgICBib3gtc2hhZG93OiAwcHggMHB4IDhweCAwcHggIzAwNTZhZDsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgIC5zY3JvbGwtd3JhcHBlciB7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICBvdmVyZmxvdy15OiBzY3JvbGw7DQogICAgICBvdmVyZmxvdy14OiBoaWRkZW47DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAgIHNjcm9sbGJhci13aWR0aDogbm9uZTsNCiAgICAgIC1tcy1vdmVyZmxvdy1zdHlsZTogbm9uZTsNCg0KICAgICAgJjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICAgICAgICBkaXNwbGF5OiBub25lOw0KICAgICAgfQ0KICAgIH0NCg0KICAgICY6OmFmdGVyIHsNCiAgICAgIGNvbnRlbnQ6ICIiOw0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgdG9wOiAyMHB4Ow0KICAgICAgcmlnaHQ6IDA7DQogICAgICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDQwcHgpOw0KICAgICAgd2lkdGg6IDZweDsNCiAgICAgIGJhY2tncm91bmQ6IHJnYmEoMTYsIDIxNiwgMjU1LCAwLjEpOw0KICAgICAgb3BhY2l0eTogMDsNCiAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zczsNCiAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOw0KICAgIH0NCg0KICAgIC5zY3JvbGwtYmFyIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogMjBweDsNCiAgICAgIHJpZ2h0OiAwOw0KICAgICAgd2lkdGg6IDZweDsNCiAgICAgIGhlaWdodDogdmFyKC0tc2Nyb2xsYmFyLWhlaWdodCwgMTAwcHgpOw0KICAgICAgYmFja2dyb3VuZDogcmdiYSgxNiwgMjE2LCAyNTUsIDAuNCk7DQogICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQogICAgICBvcGFjaXR5OiAwOw0KICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzOw0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKHZhcigtLXNjcm9sbGJhci10b3AsIDApKTsNCiAgICAgIHBvaW50ZXItZXZlbnRzOiBub25lOw0KICAgIH0NCg0KICAgICY6aG92ZXIgew0KICAgICAgJjo6YWZ0ZXIsDQogICAgICAuc2Nyb2xsLWJhciB7DQogICAgICAgIG9wYWNpdHk6IDE7DQogICAgICB9DQogICAgfQ0KDQogICAgLnJlbWVuZ3dlbnpoYW5nLWxpc3Qgew0KICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgcGFkZGluZy1sZWZ0OiAyMHB4Ow0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCg0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgd2lkdGg6IDMzMHB4Ow0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICBjb2xvcjogcmdiYSgyMTYsIDI0MCwgMjU1LCAwLjgpOw0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgICAgZm9udC1mYW1pbHk6ICJTb3VyY2UgSGFuIFNhbnMgQ04iOw0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4Ow0KICAgICAgfQ0KDQogICAgICAudGltZSwNCiAgICAgIC5zb3VyY2VOYW1lIHsNCiAgICAgICAgd2lkdGg6IDE1MHB4Ow0KICAgICAgICBjb2xvcjogcmdiYSgyMTYsIDI0MCwgMjU1LCAwLjgpOw0KICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgZm9udC1mYW1pbHk6ICJTb3VyY2UgSGFuIFNhbnMgQ04iOw0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4Ow0KICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgIH0NCg0KICAgICAgLmJsb2NrIHsNCiAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICBsZWZ0OiAwcHg7DQogICAgICAgIHRvcDogNnB4Ow0KICAgICAgICB3aWR0aDogMTBweDsNCiAgICAgICAgaGVpZ2h0OiAxMHB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiAxcHg7DQogICAgICAgIGJhY2tncm91bmQ6ICMxYmRjZmY7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnJlbWVuZ3dlbnpoYW5nLWJveDEgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMjMwcHg7DQogICAgcGFkZGluZzogMjBweDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDE2LCAyMTYsIDI1NSwgMC40KTsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KICAgIGJveC1zaGFkb3c6IDBweCAwcHggOHB4IDBweCAjMDA1NmFkOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgLnNjcm9sbC13cmFwcGVyIHsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIG92ZXJmbG93LXk6IHNjcm9sbDsNCiAgICAgIG92ZXJmbG93LXg6IGhpZGRlbjsNCiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgICAgc2Nyb2xsYmFyLXdpZHRoOiBub25lOw0KICAgICAgLW1zLW92ZXJmbG93LXN0eWxlOiBub25lOw0KDQogICAgICAmOjotd2Via2l0LXNjcm9sbGJhciB7DQogICAgICAgIGRpc3BsYXk6IG5vbmU7DQogICAgICB9DQogICAgfQ0KDQogICAgJjo6YWZ0ZXIgew0KICAgICAgY29udGVudDogIiI7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDIwcHg7DQogICAgICByaWdodDogMDsNCiAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gNDBweCk7DQogICAgICB3aWR0aDogNnB4Ow0KICAgICAgYmFja2dyb3VuZDogcmdiYSgxNiwgMjE2LCAyNTUsIDAuMSk7DQogICAgICBvcGFjaXR5OiAwOw0KICAgICAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzOw0KICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogICAgfQ0KDQogICAgLnNjcm9sbC1iYXIgew0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgdG9wOiAyMHB4Ow0KICAgICAgcmlnaHQ6IDA7DQogICAgICB3aWR0aDogNnB4Ow0KICAgICAgaGVpZ2h0OiB2YXIoLS1zY3JvbGxiYXItaGVpZ2h0LCAxMDBweCk7DQogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDE2LCAyMTYsIDI1NSwgMC40KTsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDNweDsNCiAgICAgIG9wYWNpdHk6IDA7DQogICAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3M7DQogICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkodmFyKC0tc2Nyb2xsYmFyLXRvcCwgMCkpOw0KICAgICAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogICAgfQ0KDQogICAgJjpob3ZlciB7DQogICAgICAmOjphZnRlciwNCiAgICAgIC5zY3JvbGwtYmFyIHsNCiAgICAgICAgb3BhY2l0eTogMTsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAucmVtZW5nd2VuemhhbmctbGlzdCB7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KDQogICAgICAudGl0bGUgew0KICAgICAgICB3aWR0aDogMzMwcHg7DQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIGNvbG9yOiByZ2JhKDIxNiwgMjQwLCAyNTUsIDAuOCk7DQogICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgICBmb250LWZhbWlseTogIlNvdXJjZSBIYW4gU2FucyBDTiI7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7DQogICAgICB9DQoNCiAgICAgIC50aW1lLA0KICAgICAgLnNvdXJjZU5hbWUgew0KICAgICAgICB3aWR0aDogMTUwcHg7DQogICAgICAgIGNvbG9yOiByZ2JhKDIxNiwgMjQwLCAyNTUsIDAuOCk7DQogICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICBmb250LWZhbWlseTogIlNvdXJjZSBIYW4gU2FucyBDTiI7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7DQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgfQ0KDQogICAgICAuYmxvY2sgew0KICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIGxlZnQ6IDBweDsNCiAgICAgICAgdG9wOiA2cHg7DQogICAgICAgIHdpZHRoOiAxMHB4Ow0KICAgICAgICBoZWlnaHQ6IDEwcHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDFweDsNCiAgICAgICAgYmFja2dyb3VuZDogIzFiZGNmZjsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLy8g5by556qX5qC35byPDQo6OnYtZGVlcCAuZWwtZGlhbG9nIHsNCiAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi9hc3NldHMvYmlnU2NyZWVuVHdvL2RpYWxvZ0JhY2tncm91bmQucG5nIikgbm8tcmVwZWF0Ow0KICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJSAhaW1wb3J0YW50Ow0KICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyOw0KICBoZWlnaHQ6IDgwMHB4Ow0KDQogIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogIzFkMjMzNDAwOw0KICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICBsaW5lLWhlaWdodDogMTAwcHg7DQogICAgdGV4dC1zaGFkb3c6IDBweCAwcHggMTBweCByZ2JhKDMwLCAxOTgsIDI1NSwgMC44KTsNCiAgICBoZWlnaHQ6IDEwMHB4Ow0KDQogICAgLmVsLWRpYWxvZ19fdGl0bGUgew0KICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgd2lkdGg6IGNhbGMoMTAwJSAtIDEwMHB4KTsNCiAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgfQ0KICB9DQoNCiAgLmVsLWRpYWxvZ19fYm9keSB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogIzJhMzA0MDAwOw0KICAgIGNvbG9yOiAjZjJmMmYyOw0KICAgIGhlaWdodDogY2FsYygxMDAlIC0gMTQwcHgpOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgcGFkZGluZzogMjBweCAzMHB4Ow0KICB9DQoNCiAgLmVsLWRpYWxvZ19fZm9vdGVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMWQyMzM0MDA7DQogICAgcGFkZGluZzogMThweCAyMHB4Ow0KICB9DQoNCiAgLmVsLWJ1dHRvbiB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogIzAwMjc2NjsNCiAgICBjb2xvcjogI2ZmZjsNCiAgICBib3JkZXI6IDBweDsNCiAgfQ0KDQogIC5lbC1kaWFsb2dfX2hlYWRlcmJ0biAuZWwtZGlhbG9nX19jbG9zZSB7DQogICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi9hc3NldHMvYmlnU2NyZWVuVHdvL+WFs+mXreWwjy5wbmciKSBuby1yZXBlYXQ7DQogICAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCUgIWltcG9ydGFudDsNCiAgICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyOw0KICAgIHdpZHRoOiAzMXB4Ow0KICAgIGhlaWdodDogMzFweDsNCiAgICB0b3A6IDE2cHg7DQoNCiAgICAmOjpiZWZvcmUgew0KICAgICAgY29udGVudDogbm9uZTsNCiAgICB9DQogIH0NCn0NCg0KLmRpYWxvZy1hcnQgew0KICBiYWNrZ3JvdW5kOiAjMWQyOTNiOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBoZWlnaHQ6IDU5MHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBsaW5lLWhlaWdodDogMS44ZW07DQogIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgc3lzdGVtLXVpLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsDQogICAgSGVsdmV0aWNhIE5ldWUsIEhpcmFnaW5vIFNhbnMgR0IsIE1pY3Jvc29mdCBZYUhlaSBVSSwgTWljcm9zb2Z0IFlhSGVpLCBBcmlhbCwNCiAgICBzYW5zLXNlcmlmOw0KDQogIDo6di1kZWVwIHAgew0KICAgIHRleHQtaW5kZW50OiAyZW07DQogIH0NCn0NCg0KLyog6Ieq5a6a5LmJ5rua5Yqo5p2h5qC35byPICovDQo6OnYtZGVlcCAuZGlhbG9nLWFydDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogOHB4Ow0KfQ0KDQo6OnYtZGVlcCAuZGlhbG9nLWFydDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KOjp2LWRlZXAgLmRpYWxvZy1hcnQ6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogcmdiYSgxNCwgMTk0LCAyNDQsIDAuNik7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KOjp2LWRlZXAgLmRpYWxvZy1hcnQ6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgYmFja2dyb3VuZDogcmdiYSgxNCwgMTk0LCAyNDQsIDAuOCk7DQp9DQoNCi5meiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgLnRleHQgew0KICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgZm9udC1zaXplOiAyMHB4Ow0KICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgfQ0KDQogIC5idG5zIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgYmFja2dyb3VuZDogIzFkMjkzYjsNCiAgICBib3JkZXItcmFkaXVzOiAxNHB4Ow0KICAgIHBhZGRpbmc6IDAgMTBweDsNCiAgICBoZWlnaHQ6IDI4cHg7DQoNCiAgICAuYnRuLW1pbnVzLA0KICAgIC5idG4tcGx1cyB7DQogICAgICB3aWR0aDogMjRweDsNCiAgICAgIGhlaWdodDogMjRweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCg0KICAgICAgJjpob3ZlciB7DQogICAgICAgIGNvbG9yOiAjMmY3Y2ZlOw0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5mb250LXNpemUgew0KICAgICAgbWFyZ2luOiAwIDE1cHg7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIG1pbi13aWR0aDogNDVweDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQogIH0NCn0NCg0KLmtlamlkb25ndGFpLWJveCB7DQogIHdpZHRoOiAxMDAlOw0KICAvLyBoZWlnaHQ6IDQ1cHg7DQogIC8vIHBhZGRpbmctdG9wOiAxMXB4Ow0KICAvLyBtYXJnaW46IDNweCAwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICAvLyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBjb2x1bW4tZ2FwOiAxMHB4Ow0KICByb3ctZ2FwOiAxMHB4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBtYXJnaW4tdG9wOiAxMHB4Ow0KDQogIC5rZWppZG9uZ3RhaS1idXR0b24gew0KICAgIC8vIHdpZHRoOiAxMTFweDsNCiAgICBoZWlnaHQ6IDMzcHg7DQogICAgbGluZS1oZWlnaHQ6IDMzcHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uLy4uL2Fzc2V0cy9iaWdTY3JlZW5TYW5oYW8va2VqaXFpYW55YW4xLnBuZyIpOw0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlOw0KICAgIHBhZGRpbmc6IDAgMTJweDsNCiAgfQ0KDQogIC5hY3RpdmUgew0KICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiLi4vLi4vYXNzZXRzL2JpZ1NjcmVlblNhbmhhby9rZWppcWlhbnlhbjIucG5nIik7DQogIH0NCn0NCg0KOmRlZXAoLm1hcmttYXAtbm9kZSkgew0KICBjdXJzb3I6IHBvaW50ZXI7DQoNCiAgJjpob3ZlciB7DQogICAgb3BhY2l0eTogMC44Ow0KICB9DQp9DQoNCjpkZWVwKC5tYXJrbWFwLW5vZGUtY2lyY2xlKSB7DQogIGZpbGw6IHRyYW5zcGFyZW50OyAvLyDkv67mlLnoioLngrnog4zmma/kuLrpgI/mmI4NCiAgc3Ryb2tlLXdpZHRoOiAycHg7DQp9DQoNCjpkZWVwKC5tYXJrbWFwLW5vZGUtdGV4dCkgew0KICBmb250LWZhbWlseTogLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8sDQogICAgIkhlbHZldGljYSBOZXVlIiwgQXJpYWw7DQoNCiAgdHNwYW4gew0KICAgIGZpbGw6ICMzMzMgIWltcG9ydGFudDsgLy8g5L+u5pS55paH5a2X6aKc6Imy5Li65rex6ImyDQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogIH0NCn0NCg0KOmRlZXAoLm1hcmttYXAtbGluaykgew0KICBmaWxsOiBub25lOw0KICBzdHJva2Utd2lkdGg6IDIuNXB4OyAvLyDliqDnspfov57nur8NCn0NCg0KLy8g5qC56IqC54K55qC35byPDQo6ZGVlcCgubWFya21hcC1ub2RlW2RhdGEtZGVwdGg9IjAiXSkgew0KICAubWFya21hcC1ub2RlLWNpcmNsZSB7DQogICAgc3Ryb2tlOiAjMDA1MmZmOyAvLyDkuq7ok53oibINCiAgICBzdHJva2Utd2lkdGg6IDNweDsNCiAgfQ0KDQogIC5tYXJrbWFwLW5vZGUtdGV4dCB0c3BhbiB7DQogICAgZm9udC1zaXplOiAyMHB4ICFpbXBvcnRhbnQ7DQogICAgZm9udC13ZWlnaHQ6IGJvbGQgIWltcG9ydGFudDsNCiAgICBmaWxsOiAjMzMzICFpbXBvcnRhbnQ7DQogIH0NCn0NCg0KLy8g5LqM57qn6IqC54K55qC35byPDQo6ZGVlcCgubWFya21hcC1ub2RlW2RhdGEtZGVwdGg9IjEiXSkgew0KICAubWFya21hcC1ub2RlLWNpcmNsZSB7DQogICAgc3Ryb2tlOiAjMDA5NjAwOyAvLyDkuq7nu7/oibINCiAgICBzdHJva2Utd2lkdGg6IDIuNXB4Ow0KICB9DQoNCiAgLm1hcmttYXAtbm9kZS10ZXh0IHRzcGFuIHsNCiAgICBmb250LXNpemU6IDE4cHggIWltcG9ydGFudDsNCiAgICBmb250LXdlaWdodDogNjAwICFpbXBvcnRhbnQ7DQogICAgZmlsbDogIzMzMyAhaW1wb3J0YW50Ow0KICB9DQp9DQoNCi8vIOS4iee6p+iKgueCueagt+W8jw0KOmRlZXAoLm1hcmttYXAtbm9kZVtkYXRhLWRlcHRoPSIyIl0pIHsNCiAgLm1hcmttYXAtbm9kZS1jaXJjbGUgew0KICAgIHN0cm9rZTogI2ZmNjYwMDsgLy8g5Lqu5qmZ6ImyDQogICAgc3Ryb2tlLXdpZHRoOiAycHg7DQogIH0NCg0KICAubWFya21hcC1ub2RlLXRleHQgdHNwYW4gew0KICAgIGZvbnQtc2l6ZTogMTZweCAhaW1wb3J0YW50Ow0KICAgIGZvbnQtd2VpZ2h0OiA1MDAgIWltcG9ydGFudDsNCiAgICBmaWxsOiAjMzMzICFpbXBvcnRhbnQ7DQogIH0NCn0NCg0KLy8g5YW25LuW5bGC57qn6IqC54K55qC35byPDQo6ZGVlcCgubWFya21hcC1ub2RlW2RhdGEtZGVwdGg9IjMiXSksDQo6ZGVlcCgubWFya21hcC1ub2RlW2RhdGEtZGVwdGg9IjQiXSkgew0KICAubWFya21hcC1ub2RlLWNpcmNsZSB7DQogICAgc3Ryb2tlOiAjODAwMGZmOyAvLyDkuq7ntKvoibINCiAgICBzdHJva2Utd2lkdGg6IDJweDsNCiAgfQ0KDQogIC5tYXJrbWFwLW5vZGUtdGV4dCB0c3BhbiB7DQogICAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7DQogICAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50Ow0KICAgIGZpbGw6ICMzMzMgIWltcG9ydGFudDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["tabOne.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgqDA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "tabOne.vue", "sourceRoot": "src/views/bigScreenSanhao", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%; display: flex\" class=\"two\">\r\n    <div class=\"left\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">热点推荐</div>\r\n          <!-- <div class=\"bsContentTitleHelp\"></div> -->\r\n\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('MonitorUse?id=1')\"\r\n            style=\"right: 80px\"\r\n          >\r\n            更多\r\n          </div>\r\n          <div class=\"bsContentTitleMore\" @click=\"openSina\">舆情通</div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <div class=\"remengwenzhang-box\" :style=\"remengwenzhangBoxStyle\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper\"\r\n              @mouseenter=\"handleMouseEnter\"\r\n              @mouseleave=\"handleMouseLeave\"\r\n              @scroll=\"updateScrollbar\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView(item)\"\r\n                >\r\n                  <div\r\n                    class=\"block\"\r\n                    :style=\"{\r\n                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',\r\n                    }\"\r\n                  ></div>\r\n                  <div class=\"title\">{{ item.title }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"scroll-bar\"></div>\r\n          </div>\r\n\r\n          <!-- 文章通知组件 -->\r\n          <article-notification\r\n            ref=\"articleNotification\"\r\n            :articles=\"notificationArticles\"\r\n            :visible=\"showNotification\"\r\n            @close=\"handleNotificationClose\"\r\n            @view-article=\"handleViewArticle\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">政策风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getPolicyRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <usaMap\r\n            style=\"width: 516px; height: 247px\"\r\n            :external-data=\"usaMapData\"\r\n          ></usaMap>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">打压风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <timeLine\r\n            :timelineEvents=\"suppressListData\"\r\n            @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n            style=\"width: 516px; height: 247px\"\r\n          ></timeLine>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <div class=\"center-top\">\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherTotal, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">有效采集量</div>\r\n        </div>\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherDayNumber, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">当日采集数量</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"bsContentBox1\"\r\n        @mouseenter=\"handleMouseEnter2\"\r\n        @mouseleave=\"handleMouseLeave2\"\r\n      >\r\n        <div class=\"bsContentTitle1\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\" style=\"display: flex\">\r\n            <div\r\n              @click=\"zhikuActive = 0\"\r\n              :style=\"{ fontWeight: zhikuActive === 0 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              重点人物分析\r\n            </div>\r\n            <div style=\"margin: 0 4px\">/</div>\r\n            <div\r\n              @click=\"zhikuActive = 1\"\r\n              :style=\"{ fontWeight: zhikuActive === 1 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              产业与技术专题分析\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"bsContentTitleHelp\" ></div> -->\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          style=\"display: flex; flex-direction: column; gap: 8px; padding: 8px\"\r\n        >\r\n          <!-- Tab 切换按钮 -->\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 0\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'trump' }\"\r\n              @click=\"switchTab('trump')\"\r\n            >\r\n              特朗普\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'msk' }\"\r\n              @click=\"switchTab('msk')\"\r\n            >\r\n              埃隆·里夫·马斯克\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'ws' }\"\r\n              @click=\"switchTab('ws')\"\r\n            >\r\n              詹姆斯·唐纳德·万斯\r\n            </div>\r\n          </div>\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 1\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'bdt' }\"\r\n              @click=\"switchTab('bdt', 7)\"\r\n            >\r\n              半导体领域\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'gdzb' }\"\r\n              @click=\"switchTab('gdzb', 8)\"\r\n            >\r\n              高端装备与材料\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'xnyqc' }\"\r\n              @click=\"switchTab('xnyqc', 9)\"\r\n            >\r\n              新能源汽车与电池\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'szhzx' }\"\r\n              @click=\"switchTab('szhzx', 10)\"\r\n            >\r\n              数字化转型与工业软件\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'lszz' }\"\r\n              @click=\"switchTab('lszz', 11)\"\r\n            >\r\n              绿色制造与新能源\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'swyy' }\"\r\n              @click=\"switchTab('swyy', 12)\"\r\n            >\r\n              生物医药与医疗器械\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 人物观点内容（特朗普/马斯克公用） -->\r\n          <div\r\n            v-show=\"\r\n              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <!-- 上方人物观点树状图 -->\r\n            <div class=\"trump-view-container\">\r\n              <trumpViewTree\r\n                ref=\"characterViewTree\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n                style=\"width: 100%; height: 300px\"\r\n                :move=\"isHovered2\"\r\n                :currentCharacter=\"activeTab\"\r\n              >\r\n              </trumpViewTree>\r\n            </div>\r\n            <div class=\"view-tree-container\">\r\n              <viewTree\r\n                :treeData=\"characterViewData\"\r\n                :title=\"'renwu'\"\r\n                :visible=\"\r\n                  activeTab === 'trump' ||\r\n                  activeTab === 'msk' ||\r\n                  activeTab === 'ws'\r\n                \"\r\n                style=\"width: 100%; height: 100%\"\r\n                @openNewView=\"openNewView1\"\r\n                @openbaarTreeEcharts=\"openbaarTreeEcharts()\"\r\n              ></viewTree>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中国制造短板分析内容 -->\r\n          <div\r\n            v-show=\"\r\n              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <svg ref=\"markmap\" class=\"markmap-svg\"></svg>\r\n          </div>\r\n\r\n          <div\r\n            style=\"\"\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openbaarTreeEcharts()\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">前沿科技动态</div>\r\n          <div class=\"bsContentTitleMore\" @click=\"qykjdtOpenNewTab()\">更多</div>\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          @mouseleave=\"handleMouseLeave1\"\r\n          @scroll=\"updateScrollbar1\"\r\n        >\r\n          <div class=\"kejidongtai-box\">\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '脑机接口' }\"\r\n              @click=\"handleButtonClick('脑机接口')\"\r\n            >\r\n              脑机接口\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '量子信息' }\"\r\n              @click=\"handleButtonClick('量子信息')\"\r\n            >\r\n              量子信息\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '人形机器人' }\"\r\n              @click=\"handleButtonClick('人形机器人')\"\r\n            >\r\n              人形机器人\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生成式人工智能' }\"\r\n              @click=\"handleButtonClick('生成式人工智能')\"\r\n            >\r\n              生成式人工智能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生物制造' }\"\r\n              @click=\"handleButtonClick('生物制造')\"\r\n            >\r\n              生物制造\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来显示' }\"\r\n              @click=\"handleButtonClick('未来显示')\"\r\n            >\r\n              未来显示\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来网络' }\"\r\n              @click=\"handleButtonClick('未来网络')\"\r\n            >\r\n              未来网络\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '新型储能' }\"\r\n              @click=\"handleButtonClick('新型储能')\"\r\n            >\r\n              新型储能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '其他' }\"\r\n              @click=\"handleButtonClick('其他')\"\r\n            >\r\n              其他\r\n            </div>\r\n          </div>\r\n          <div class=\"remengwenzhang-box1\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper1\"\r\n              @mouseenter=\"handleMouseEnter1\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent1\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList1\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView1(item)\"\r\n                >\r\n                  <div class=\"block\"></div>\r\n                  <div class=\"title\">{{ item.cnTitle }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"scroll-bar\"></div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox2\" style=\"width: 516px; height: 600px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">国内外前沿热点技术</div>\r\n          <div\r\n            class=\"bsContentTitleHelp\"\r\n            @click=\"comparisonChartShowModal = true\"\r\n          ></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"\r\n              openNewTab(\r\n                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'\r\n              )\r\n            \"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"kejidongtai-box\">\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '11' }\"\r\n            @click=\"handleTechButtonClick('11', '新能源')\"\r\n          >\r\n            新能源\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '12' }\"\r\n            @click=\"handleTechButtonClick('12', '新材料')\"\r\n          >\r\n            新材料\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '13' }\"\r\n            @click=\"handleTechButtonClick('13', '高端装备')\"\r\n          >\r\n            高端装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '14' }\"\r\n            @click=\"handleTechButtonClick('14', '新能源汽车')\"\r\n          >\r\n            新能源汽车\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '17' }\"\r\n            @click=\"handleTechButtonClick('17', '船舶与海洋工程装备')\"\r\n          >\r\n            船舶与海洋工程装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '16' }\"\r\n            @click=\"handleTechButtonClick('16', '民用航空')\"\r\n          >\r\n            民用航空\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '15' }\"\r\n            @click=\"handleTechButtonClick('15', '绿色环保')\"\r\n          >\r\n            绿色环保\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '18' }\"\r\n            @click=\"handleTechButtonClick('18', '新一代信息技术')\"\r\n          >\r\n            新一代信息技术\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"bsContentContent\" style=\"height: 188px\">\r\n          <technologyArticles\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openHotTechnology=\"openHotTechnology\"\r\n          ></technologyArticles>\r\n        </div> -->\r\n        <div class=\"bsContentContent\" style=\"padding-top: 0px; height: 450px\">\r\n          <graphEcharts\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openTechnologyDetails=\"openTechnologyDetails\"\r\n          ></graphEcharts>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <policyRisk\r\n      :visible=\"policyRiskShowModal\"\r\n      :list1=\"policyRiskList1\"\r\n      :list2=\"policyRiskList2\"\r\n      :total=\"policyRiskList1Total\"\r\n      :usa-map-data=\"usaMapData\"\r\n      title=\"美国相关提案\"\r\n      @update:visible=\"policyRiskShowModal = $event\"\r\n      @pagination=\"policyRiskPagination\"\r\n      @openArticleDetail=\"openArticleDetails('policyRisk-news', $event)\"\r\n    >\r\n    </policyRisk>\r\n    <suppressionOfRisks\r\n      :visible=\"suppressionOfRisksShowModal\"\r\n      :levelCount=\"riskBarChartData\"\r\n      :enterpriseList=\"riskEnterpriseList\"\r\n      :total=\"riskEnterpriseListTotal\"\r\n      title=\"打压风险\"\r\n      @update:visible=\"suppressionOfRisksShowModal = $event\"\r\n      @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n      @pagination=\"riskEnterpriseListPagination\"\r\n    >\r\n    </suppressionOfRisks>\r\n    <technologyDetails\r\n      :visible=\"technologyDetailsShowModal\"\r\n      @update:visible=\"technologyDetailsShowModal = $event\"\r\n      @openArticleDetail=\"(e) => openArticleDetails('technology-article', e)\"\r\n      :title=\"technologyDetailsTitle\"\r\n      :item=\"technologyDetailsItem\"\r\n    ></technologyDetails>\r\n    <articleDetails\r\n      :visible=\"articleDetailsShowModal\"\r\n      :title=\"articleDetailsTitle\"\r\n      :content=\"articleDetailsContent\"\r\n      :contentEn=\"articleDetailsContentEn\"\r\n      :item=\"articleDetailsItem\"\r\n      @update:visible=\"articleDetailsShowModal = $event\"\r\n    >\r\n    </articleDetails>\r\n    <enterpriseInformation\r\n      :visible=\"enterpriseInformationShowModal\"\r\n      :title=\"enterpriseInformationTitle\"\r\n      :content=\"enterpriseInformationContent\"\r\n      :patentList=\"patentList\"\r\n      :softwareList=\"softwareList\"\r\n      :total1=\"patentTotal\"\r\n      :total2=\"softwareTotal\"\r\n      @update:visible=\"enterpriseInformationShowModal = $event\"\r\n      @pagination1=\"patentPagination\"\r\n      @pagination2=\"softwarePagination\"\r\n      @openArticleDetail=\"\r\n        (e) => openArticleDetails('enterpriseInformation-news', e)\r\n      \"\r\n    >\r\n    </enterpriseInformation>\r\n    <comparisonChart\r\n      :visible=\"comparisonChartShowModal\"\r\n      @update:visible=\"comparisonChartShowModal = $event\"\r\n      @openHotTechnology=\"openHotTechnology\"\r\n      title=\"前沿技术热点对比图详情\"\r\n    ></comparisonChart>\r\n    <hotTechnology\r\n      :visible=\"hotTechnologyShowModal\"\r\n      :title=\"hotTechnologytTitle\"\r\n      :id=\"hotTechnologytID\"\r\n      @update:visible=\"hotTechnologyShowModal = $event\"\r\n    ></hotTechnology>\r\n    <baarTreeEcharts\r\n      :visible=\"baarTreeEchartsShowModal\"\r\n      :type=\"baarTreeEchartsType\"\r\n      title=\"智库观点\"\r\n      @update:visible=\"baarTreeEchartsShowModal = $event\"\r\n      @openNewView=\"openNewView1\"\r\n    ></baarTreeEcharts>\r\n\r\n    <!-- 热点推荐文章详情弹窗 -->\r\n    <el-dialog\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"articleDialogVisible\"\r\n      width=\"65%\"\r\n      append-to-body\r\n      :before-close=\"handleClose\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"fz\">\r\n        <div class=\"text\">字号：</div>\r\n        <div class=\"btns\">\r\n          <div class=\"btn-minus\" @click=\"decreaseFontSize\">-</div>\r\n          <div class=\"font-size\">{{ fontSize }}px</div>\r\n          <div class=\"btn-plus\" @click=\"increaseFontSize\">+</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"dialog-art\"\r\n        :style=\"{ fontSize: fontSize + 'px' }\"\r\n        v-html=\"drawerInfo.cnContent\"\r\n      ></div>\r\n      <el-empty\r\n        description=\"当前文章暂无数据\"\r\n        v-if=\"!drawerInfo.cnContent\"\r\n      ></el-empty>\r\n    </el-dialog>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"aiLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n\r\n    <!-- 技术领域泡泡图弹窗 -->\r\n    <techBubbleDialog\r\n      :visible=\"techBubbleDialogVisible\"\r\n      :title=\"techBubbleDialogTitle\"\r\n      :screenSn=\"techBubbleDialogScreenSn\"\r\n      @update:visible=\"techBubbleDialogVisible = $event\"\r\n      @openTechnologyDetails=\"openTechnologyDetails\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport usaMap from \"./components/usaMap\";\r\nimport timeLine from \"./components/timeLine\";\r\nimport graphEcharts from \"./components/graphEcharts\";\r\nimport technologyArticles from \"./components/technologyArticles\";\r\nimport trumpViewTree from \"./components/trumpViewTree\";\r\nimport viewTree from \"./components/viewTree\";\r\nimport policyRisk from \"./secondLevel/policyRisk\";\r\nimport articleDetails from \"./secondLevel/articleDetails\";\r\nimport suppressionOfRisks from \"./secondLevel/suppressionOfRisks\";\r\nimport enterpriseInformation from \"./secondLevel/enterpriseInformation\";\r\nimport comparisonChart from \"./secondLevel/comparisonChart\";\r\nimport hotTechnology from \"./secondLevel/hotTechnology\";\r\nimport baarTreeEcharts from \"./components/baarTreeEcharts\";\r\nimport technologyDetails from \"./secondLevel/technologyDetails\";\r\nimport techBubbleDialog from \"./secondLevel/techBubbleDialog\";\r\nimport ArticleNotification from \"@/components/ArticleNotification\";\r\nimport {\r\n  technicalArticleDetail,\r\n  suppressData,\r\n  suppressLevelCount,\r\n  suppressEnterpriseList,\r\n  suppressPatentList,\r\n  suppressSoftwareList,\r\n  proposalsList,\r\n  proposalsToChinaData,\r\n  proposalsCount,\r\n  kjdtArticleList,\r\n  loginTCES,\r\n  loginSINA,\r\n} from \"@/api/bigScreen/sanhao.js\";\r\nimport {\r\n  largeHotQueryById,\r\n  largeGatherQueryGatherData,\r\n  getLargeFTT,\r\n  largeHotList2,\r\n} from \"@/api/bigScreen/index1\";\r\nimport { markObj } from \"./data/zhiku.js\";\r\nimport { treeData2, markdownData } from \"./data/renwu.js\";\r\nimport MarkmapDialog from \"../bigScreenThree/components/MarkmapDialog.vue\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { Transformer } from \"markmap-lib\";\r\nimport { Markmap } from \"markmap-view\";\r\n\r\nexport default {\r\n  name: \"TabOne\",\r\n  components: {\r\n    usaMap,\r\n    timeLine,\r\n    graphEcharts,\r\n    technologyArticles,\r\n    trumpViewTree,\r\n    viewTree,\r\n    policyRisk,\r\n    articleDetails,\r\n    suppressionOfRisks,\r\n    enterpriseInformation,\r\n    comparisonChart,\r\n    hotTechnology,\r\n    baarTreeEcharts,\r\n    technologyDetails,\r\n    techBubbleDialog,\r\n    MarkmapDialog,\r\n    ArticleNotification,\r\n  },\r\n  props: {\r\n    notificationArticles: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    showNotification: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      policyRiskShowModal: false,\r\n      comparisonChartShowModal: false,\r\n      hotTechnologyShowModal: false,\r\n      hotTechnologytTitle: \"\",\r\n      hotTechnologytID: null,\r\n      baarTreeEchartsShowModal: false,\r\n      baarTreeEchartsType: null,\r\n      technologyDetailsShowModal: false,\r\n      technologyDetailsTitle: \"\",\r\n      technologyDetailsItem: null,\r\n      suppressionOfRisksShowModal: false,\r\n      enterpriseInformationShowModal: false,\r\n      enterpriseInformationTitle: \"\",\r\n      articleDetailsShowModal: false,\r\n      articleDetailsTitle: \"\",\r\n      articleDetailsContent: \"\",\r\n      articleDetailsContentEn: \"\",\r\n      suppressListData: [],\r\n      riskBarChartData: [],\r\n      riskEnterpriseList: [],\r\n      riskEnterpriseListTotal: 0,\r\n      enterpriseInformationContent: {},\r\n      patentList: [],\r\n      softwareList: [],\r\n      patentTotal: 0,\r\n      softwareTotal: 0,\r\n      policyRiskList1: [],\r\n      policyRiskList2: [],\r\n      policyRiskList1Total: 0,\r\n      // 美国地图数据\r\n      usaMapData: null,\r\n      articleDetailsItem: {},\r\n      // 热点推荐相关数据\r\n      remengwenzhangList: [],\r\n      scrollTimer: null,\r\n      scrollTimer1: null,\r\n      scrollTimer2: null,\r\n      isHovered: false,\r\n      isHovered1: false,\r\n      isHovered2: false,\r\n      scrollStep: 1,\r\n      drawerInfo: {},\r\n      articleDialogVisible: false,\r\n      fontSize: 16,\r\n      oriFontSize: 20,\r\n      // 人物观点数据\r\n      characterViewData: [],\r\n      // 智库观点数据\r\n      thinkTankViewData: [],\r\n      remengwenzhangList1: [],\r\n      activeButton: null,\r\n      qianyankejiList: [],\r\n      gatherTotal: 0,\r\n      gatherDayNumber: 0,\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"智库观点\",\r\n      aiLoading: false,\r\n      frontLoginParams: {\r\n        username: \"guanliyuan\",\r\n        password: \"123456\",\r\n      },\r\n      frontToken: \"\",\r\n      // 技术领域相关\r\n      activeTechButton: \"11\", // 默认选中新能源\r\n      currentTechScreenSn: \"11\", // 当前技术领域的screenSn\r\n      // 技术领域泡泡图弹窗相关\r\n      techBubbleDialogVisible: false,\r\n      techBubbleDialogTitle: \"\",\r\n      techBubbleDialogScreenSn: \"\",\r\n      // 智库观点tab切换\r\n      activeTab: \"trump\", // 默认显示特朗普tab\r\n      domainMarkdown: \"\",\r\n      sinaUrl: \"\",\r\n      zhikuActive: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算热门文章列表框的样式\r\n    remengwenzhangBoxStyle() {\r\n      const notificationHeight = 110; // 通知组件的高度\r\n\r\n      if (this.showNotification) {\r\n        return {\r\n          height: `calc(100% - ${notificationHeight}px)`,\r\n        };\r\n      } else {\r\n        return {\r\n          height: `100%`,\r\n        };\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    // 调用登录TCES接口\r\n    loginTCES()\r\n      .then(() => {\r\n        console.log(\"TCES登录成功\");\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"TCES登录失败:\", error);\r\n      });\r\n\r\n    // 调用登录新浪接口\r\n    loginSINA()\r\n      .then((res) => {\r\n        console.log(\"新浪登录成功\");\r\n        this.sinaUrl = res;\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"新浪登录失败:\", error);\r\n      });\r\n\r\n    this.getSuppressData();\r\n    this.initHotList();\r\n    this.initHotList1();\r\n    this.updateScrollbar();\r\n    this.updateScrollbar1();\r\n    this.fetchUsaMapData();\r\n    largeGatherQueryGatherData({}).then((res) => {\r\n      this.gatherTotal = res.data.gatherTotal;\r\n      this.gatherDayNumber = res.data.gatherDayNumber;\r\n    });\r\n  },\r\n  beforeDestroy() {\r\n    this.clearScrollTimer();\r\n    this.clearScrollTimer1();\r\n    this.clearScrollTimer2();\r\n    this.handleMarkmapClose();\r\n  },\r\n  methods: {\r\n    // 获取美国地图数据\r\n    async fetchUsaMapData() {\r\n      try {\r\n        const response = await proposalsCount({\r\n          projectSn: \"1\",\r\n          screenSn: \"1\",\r\n          columnSn: \"1\",\r\n        });\r\n        this.usaMapData = response;\r\n      } catch (error) {\r\n        console.error(\"获取美国地图数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    openArticleDetails(type, item) {\r\n      this.articleDetailsItem = item;\r\n      switch (type) {\r\n        case \"technology-article\":\r\n          technicalArticleDetail({ id: item.id }).then((res) => {\r\n            this.articleDetailsTitle = item.title;\r\n            this.articleDetailsContent = res.data.content;\r\n            this.articleDetailsContentEn = res.data.enContent;\r\n            this.articleDetailsShowModal = true;\r\n          });\r\n          break;\r\n        case \"enterpriseInformation-news\":\r\n          this.openNewView(item);\r\n          break;\r\n        case \"policyRisk-news\":\r\n          this.openNewView(item);\r\n          break;\r\n      }\r\n    },\r\n    openEnterpriseInformation(item) {\r\n      suppressPatentList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n        this.patentTotal = res.total;\r\n      });\r\n      suppressSoftwareList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n        this.softwareTotal = res.total;\r\n      });\r\n      this.enterpriseInformationContent = { ...item };\r\n      this.enterpriseInformationTitle = item.enterpriseName;\r\n      this.enterpriseInformationShowModal = true;\r\n    },\r\n\r\n    patentPagination(suppressSn, queryParams) {\r\n      suppressPatentList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n      });\r\n    },\r\n\r\n    softwarePagination(suppressSn, queryParams) {\r\n      suppressSoftwareList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n      });\r\n    },\r\n\r\n    getSuppressData() {\r\n      suppressData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        let data = [];\r\n        Object.keys(res.data).forEach((key) => {\r\n          data.push({\r\n            date: key,\r\n            description:\r\n              res.data[key].length <= 3\r\n                ? res.data[key]\r\n                : res.data[key].slice(\r\n                    res.data[key].length - 3,\r\n                    res.data[key].length\r\n                  ),\r\n          });\r\n        });\r\n        this.suppressListData = data.reverse();\r\n      });\r\n    },\r\n\r\n    getRiskDetail() {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n        this.riskEnterpriseListTotal = res.total;\r\n      });\r\n      suppressLevelCount({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        // 将对象格式转换为数组格式\r\n        const data = Object.keys(res.data).map((year) => ({\r\n          product: year,\r\n          严重: res.data[year].严重,\r\n          一般: res.data[year].一般,\r\n          较轻: res.data[year].较轻,\r\n        }));\r\n        this.riskBarChartData = data;\r\n        this.suppressionOfRisksShowModal = true;\r\n      });\r\n    },\r\n\r\n    riskEnterpriseListPagination(queryParams) {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n      });\r\n    },\r\n\r\n    getPolicyRiskDetail() {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n        this.policyRiskList1Total = res.total;\r\n      });\r\n      proposalsToChinaData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        this.policyRiskList2 = res.data;\r\n        this.policyRiskShowModal = true;\r\n      });\r\n    },\r\n\r\n    policyRiskPagination(queryParams) {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n      });\r\n    },\r\n\r\n    openHotTechnology(data) {\r\n      this.hotTechnologyShowModal = true;\r\n      this.hotTechnologytTitle = data.title;\r\n      this.hotTechnologytID = data.reportSn;\r\n    },\r\n    openbaarTreeEcharts(data) {\r\n      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\\D+/g, ''), 10)\r\n      // this.baarTreeEchartsShowModal = true;\r\n\r\n      // 根据当前选中的tab来决定使用哪个markObj\r\n      if (this.activeTab === \"trump\") {\r\n        // 使用导入的markdownData\r\n        this.markmapContent =\r\n          markdownData.trump[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"特朗普\";\r\n      } else if (this.activeTab === \"msk\") {\r\n        // 马斯克的markdownData\r\n        this.markmapContent =\r\n          markdownData.msk[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"埃隆·里夫·马斯克\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        // 万斯的markdownData\r\n        this.markmapContent =\r\n          markdownData.ws[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"詹姆斯·唐纳德·万斯\";\r\n      } else {\r\n        this.markmapContent = this.domainMarkdown;\r\n        switch (this.activeTab) {\r\n          case \"bdt\":\r\n            this.markmapTitle = \"半导体领域\";\r\n            break;\r\n          case \"gdzb\":\r\n            this.markmapTitle = \"高端装备与材料\";\r\n            break;\r\n          case \"xnyqc\":\r\n            this.markmapTitle = \"新能源汽车与电池\";\r\n            break;\r\n          case \"szhzx\":\r\n            this.markmapTitle = \"数字化转型与工业软件\";\r\n            break;\r\n          case \"lszz\":\r\n            this.markmapTitle = \"绿色制造与新能源\";\r\n            break;\r\n          case \"swyy\":\r\n            this.markmapTitle = \"生物医药与医疗器械\";\r\n            break;\r\n        }\r\n      }\r\n      this.aiLoading = false;\r\n      this.markmapVisible = true;\r\n    },\r\n\r\n    openTechnologyDetails(data) {\r\n      this.technologyDetailsShowModal = true;\r\n      this.technologyDetailsTitle = data.name;\r\n      this.technologyDetailsItem = data.data;\r\n    },\r\n\r\n    // 热点推荐相关方法\r\n    initHotList() {\r\n      // 使用bigScreenThree相同的API接口\r\n      largeHotList2()\r\n        .then((res) => {\r\n          this.remengwenzhangList = res.data || [];\r\n          this.$nextTick(() => {\r\n            this.startScroll();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取热点推荐数据失败:\", error);\r\n          // 如果API调用失败，使用空数组\r\n          this.remengwenzhangList = [];\r\n        });\r\n    },\r\n    initHotList1() {\r\n      this.handleButtonClick(\"脑机接口\");\r\n      this.$nextTick(() => {\r\n        this.startScroll2();\r\n      });\r\n    },\r\n\r\n    startScroll() {\r\n      this.clearScrollTimer();\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      const content = this.$refs.scrollContent;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer = setInterval(() => {\r\n        if (this.isHovered) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar();\r\n      }, 40);\r\n    },\r\n\r\n    clearScrollTimer() {\r\n      if (this.scrollTimer) {\r\n        clearInterval(this.scrollTimer);\r\n        this.scrollTimer = null;\r\n      }\r\n    },\r\n\r\n    handleMouseEnter() {\r\n      this.isHovered = true;\r\n    },\r\n\r\n    handleMouseLeave() {\r\n      this.isHovered = false;\r\n      this.startScroll();\r\n    },\r\n    startScroll1() {\r\n      this.clearScrollTimer1();\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      const content = this.$refs.scrollContent1;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer1 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar1();\r\n      }, 20);\r\n    },\r\n    startScroll2() {\r\n      this.clearScrollTimer2();\r\n      this.scrollTimer2 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        // 定义所有tab标签的顺序\r\n        const tabOrder = [\r\n          \"脑机接口\",\r\n          \"量子信息\",\r\n          \"人形机器人\",\r\n          \"生成式人工智能\",\r\n          \"生物制造\",\r\n          \"未来显示\",\r\n          \"未来网络\",\r\n          \"新型储能\",\r\n          \"其他\",\r\n        ];\r\n\r\n        // 找到当前活跃标签的索引\r\n        const currentIndex = tabOrder.indexOf(this.activeButton);\r\n        // 计算下一个标签的索引，如果到最后一个则回到第一个\r\n        const nextIndex = (currentIndex + 1) % tabOrder.length;\r\n        // 切换到下一个标签\r\n        this.handleButtonClick(tabOrder[nextIndex]);\r\n      }, 8000);\r\n    },\r\n    clearScrollTimer1() {\r\n      if (this.scrollTimer1) {\r\n        clearInterval(this.scrollTimer1);\r\n        this.scrollTimer1 = null;\r\n      }\r\n    },\r\n    clearScrollTimer2() {\r\n      if (this.scrollTimer2) {\r\n        clearInterval(this.scrollTimer2);\r\n        this.scrollTimer2 = null;\r\n      }\r\n    },\r\n    handleMouseEnter1() {\r\n      this.isHovered1 = true;\r\n    },\r\n\r\n    handleMouseLeave1() {\r\n      this.isHovered1 = false;\r\n      // this.startScroll1();\r\n      // this.startScroll2();\r\n    },\r\n    handleMouseEnter2() {\r\n      this.isHovered2 = true;\r\n    },\r\n\r\n    handleMouseLeave2() {\r\n      this.isHovered2 = false;\r\n    },\r\n    updateScrollbar() {\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n    updateScrollbar1() {\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n\r\n    async openNewView(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await largeHotQueryById(item.id);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    async openNewView1(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await getLargeFTT(item.sn);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          // content = content.replace(/\\${[^}]+}/g, \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          // 移除带样式的标签，保留内容\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n\r\n    handleClose() {\r\n      this.drawerInfo = {};\r\n      this.articleDialogVisible = false;\r\n    },\r\n\r\n    increaseFontSize() {\r\n      if (this.fontSize < 30) {\r\n        this.fontSize += 2;\r\n      }\r\n    },\r\n\r\n    decreaseFontSize() {\r\n      if (this.fontSize > 16) {\r\n        this.fontSize -= 2;\r\n      }\r\n    },\r\n    handleNodeClick(type) {\r\n      // 根据当前activeTab获取对应人物的数据\r\n      let currentCharacter = \"trump\"; // 默认特朗普\r\n      if (this.activeTab === \"msk\") {\r\n        currentCharacter = \"msk\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        currentCharacter = \"ws\";\r\n      }\r\n      let rawData = JSON.parse(\r\n        JSON.stringify(treeData2[currentCharacter][type] || [])\r\n      );\r\n      this.characterViewData = this.limitLevel3Children(rawData);\r\n    },\r\n    limitLevel3Children(data) {\r\n      if (!data || !Array.isArray(data)) return data;\r\n      return data.map((item) => {\r\n        if (\r\n          (item.type == \"level2-1\" ||\r\n            item.type == \"level2-2\" ||\r\n            item.type == \"level2-3\") &&\r\n          Array.isArray(item.children)\r\n        ) {\r\n          item.children = item.children.slice(0, 2); // 只保留前两个\r\n        }\r\n\r\n        if (item.children) {\r\n          item.children = this.limitLevel3Children(item.children);\r\n        }\r\n\r\n        return item;\r\n      });\r\n    },\r\n    handleButtonClick(type) {\r\n      let obj = {\r\n        脑机接口: \"3\",\r\n        量子信息: \"4\",\r\n        人形机器人: \"6\",\r\n        生成式人工智能: \"1\",\r\n        生物制造: \"7\",\r\n        未来显示: \"8\",\r\n        未来网络: \"9\",\r\n        新型储能: \"10\",\r\n        其他: \"2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      this.activeButton = type;\r\n\r\n      // 重置轮播时间\r\n      this.startScroll2();\r\n\r\n      kjdtArticleList({\r\n        labelSn: obj[type],\r\n      }).then((res) => {\r\n        // 对数据进行去重处理，基于cnTitle去除空格后判断\r\n        const deduplicatedData = this.deduplicateArticles(res || []);\r\n        this.remengwenzhangList1 = deduplicatedData;\r\n        this.$nextTick(() => {\r\n          const wrapper = this.$refs.scrollWrapper1;\r\n          wrapper.scrollTop = 0;\r\n        });\r\n      });\r\n    },\r\n    qykjdtOpenNewTab() {\r\n      let obj = {\r\n        脑机接口: \"/qianyankejidongtai/naojijiekou?id=1&domain=3\",\r\n        量子信息: \"/qianyankejidongtai/liangzixinxi?id=1&domain=4\",\r\n        人形机器人: \"/qianyankejidongtai/renxingjiqiren?id=1&domain=6\",\r\n        生成式人工智能: \"/qianyankejidongtai/rengongzhineng?id=1&domain=1\",\r\n        生物制造: \"/qianyankejidongtai/shengwuzhizao?id=1&domain=7\",\r\n        未来显示: \"/qianyankejidongtai/weilaixianshi?id=1&domain=8\",\r\n        未来网络: \"/qianyankejidongtai/weilaiwangluo?id=1&domain=9\",\r\n        新型储能: \"/qianyankejidongtai/xinxingchuneng?id=1&domain=10\",\r\n        其他: \"/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      window.open(obj[this.activeButton], \"_blank\");\r\n    },\r\n    // 文章去重方法，基于cnTitle去除空格后判断\r\n    deduplicateArticles(articles) {\r\n      if (!Array.isArray(articles)) {\r\n        return [];\r\n      }\r\n\r\n      const seen = new Set();\r\n      const result = [];\r\n\r\n      articles.forEach((article) => {\r\n        if (article && article.cnTitle) {\r\n          // 去除cnTitle中的所有空格\r\n          const normalizedTitle = article.cnTitle.replace(/\\s+/g, \"\");\r\n\r\n          if (!seen.has(normalizedTitle)) {\r\n            seen.add(normalizedTitle);\r\n            result.push(article);\r\n          }\r\n        } else {\r\n          // 如果没有cnTitle，也保留这条记录\r\n          result.push(article);\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    padWithZeros(num, targetLength) {\r\n      const numStr = num.toString();\r\n      const padding = \"0\".repeat(targetLength - numStr.length);\r\n      return `${padding}${numStr}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    openNewTab(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    handleMarkmapClose() {\r\n      this.markmapContent = \"\";\r\n      this.aiLoading = false;\r\n      this.markmapVisible = false;\r\n    },\r\n\r\n    // 更新热点推荐文章列表\r\n    async updateHotArticlesList() {\r\n      try {\r\n        const response = await largeHotList2();\r\n        if (response && response.data) {\r\n          const newArticles = response.data;\r\n          // 对比数据是否一致\r\n          if (this.isArticleDataChanged(newArticles)) {\r\n            this.remengwenzhangList = newArticles;\r\n          } else {\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"更新热点推荐文章列表失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 检查文章数据是否发生变化\r\n    isArticleDataChanged(newArticles) {\r\n      // 如果当前列表为空，直接返回true\r\n      if (this.remengwenzhangList.length === 0) {\r\n        return newArticles.length > 0;\r\n      }\r\n\r\n      // 如果数量不同，说明有变化\r\n      if (this.remengwenzhangList.length !== newArticles.length) {\r\n        return true;\r\n      }\r\n\r\n      // 对比每篇文章的关键信息\r\n      for (let i = 0; i < newArticles.length; i++) {\r\n        const newArticle = newArticles[i];\r\n        const oldArticle = this.remengwenzhangList[i];\r\n\r\n        // 对比文章ID、标题、发布时间等关键字段\r\n        if (\r\n          newArticle.id !== oldArticle.id ||\r\n          newArticle.title !== oldArticle.title ||\r\n          newArticle.publishTime !== oldArticle.publishTime ||\r\n          newArticle.sourceName !== oldArticle.sourceName\r\n        ) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 所有数据都一致\r\n      return false;\r\n    },\r\n\r\n    // 处理技术领域按钮点击\r\n    handleTechButtonClick(screenSn, buttonName) {\r\n      console.log(\"切换技术领域:\", buttonName, \"screenSn:\", screenSn);\r\n      this.activeTechButton = screenSn;\r\n      this.currentTechScreenSn = screenSn;\r\n      // 弹出技术领域泡泡图弹窗\r\n      this.techBubbleDialogVisible = true;\r\n      this.techBubbleDialogTitle = buttonName;\r\n      this.techBubbleDialogScreenSn = screenSn;\r\n\r\n      // 通知子组件更新数据\r\n      this.$nextTick(() => {\r\n        // 可以通过ref直接调用子组件的方法来刷新数据\r\n        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新\r\n      });\r\n    },\r\n\r\n    // 关闭技术领域泡泡图弹窗\r\n    handleTechBubbleDialogClose() {\r\n      this.techBubbleDialogVisible = false;\r\n      this.techBubbleDialogTitle = \"\";\r\n      this.techBubbleDialogScreenSn = \"\";\r\n    },\r\n\r\n    // 处理通知关闭\r\n    handleNotificationClose() {\r\n      this.$emit(\"notification-close\");\r\n    },\r\n\r\n    // 处理查看单篇文章\r\n    handleViewArticle(article) {\r\n      this.$emit(\"notification-view-article\", article);\r\n    },\r\n\r\n    // 切换智库观点tab\r\n    switchTab(tabName, markdownType) {\r\n      if (markdownType) {\r\n        this.activeTab = tabName;\r\n        this.domainMarkdown = markObj[\"type\" + markdownType];\r\n        this.renderMarkmap();\r\n      } else if (tabName === \"trump\" || tabName === \"msk\" || tabName === \"ws\") {\r\n        // 如果点击的是当前已激活的人物tab，需要重新触发数据加载\r\n        if (this.activeTab === tabName) {\r\n          // 清空数据\r\n          this.characterViewData = [];\r\n          // 通过ref直接调用trumpViewTree组件的方法来重新初始化\r\n          this.$nextTick(() => {\r\n            if (\r\n              this.$refs.characterViewTree &&\r\n              this.$refs.characterViewTree.allTypes.length > 0\r\n            ) {\r\n              this.$refs.characterViewTree.handleNodeClick(\r\n                this.$refs.characterViewTree.allTypes[0]\r\n              );\r\n            }\r\n          });\r\n        } else {\r\n          // 切换到不同的人物tab时，设置activeTab，让watcher自动处理\r\n          this.activeTab = tabName;\r\n          this.characterViewData = [];\r\n        }\r\n      }\r\n    },\r\n    async renderMarkmap() {\r\n      if (!this.domainMarkdown) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$nextTick();\r\n        const svg = this.$refs.markmap;\r\n        if (!svg) {\r\n          throw new Error(\"SVG element not found\");\r\n        }\r\n\r\n        // 清空之前的内容\r\n        svg.innerHTML = \"\";\r\n\r\n        // 处理内容，移除 markdown 标记\r\n        let processedContent = this.domainMarkdown\r\n          .replace(/^```markdown\\s*/i, \"\") // 移除开头的 ```markdown\r\n          .replace(/\\s*```\\s*$/, \"\"); // 移除结尾的 ```\r\n\r\n        const transformer = new Transformer();\r\n        const { root } = transformer.transform(processedContent);\r\n\r\n        // 创建思维导图\r\n        const mm = Markmap.create(\r\n          svg,\r\n          {\r\n            autoFit: true,\r\n            duration: 0,\r\n            nodeMinHeight: 20,\r\n            spacingVertical: 10,\r\n            spacingHorizontal: 100,\r\n            paddingX: 20,\r\n            color: (node) => {\r\n              const colors = {\r\n                0: \"#0052ff\", // 亮蓝色\r\n                1: \"#009600\", // 亮绿色\r\n                2: \"#ff6600\", // 亮橙色\r\n                3: \"#8000ff\", // 亮紫色\r\n                4: \"#ff0066\", // 亮粉色\r\n              };\r\n              return colors[node.depth] || \"#0052ff\";\r\n            },\r\n            nodeFont: (node) => {\r\n              const fonts = {\r\n                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n              };\r\n              return (\r\n                fonts[node.depth] ||\r\n                '400 14px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto'\r\n              );\r\n            },\r\n            maxWidth: 400,\r\n            initialExpandLevel: -1,\r\n            zoom: true,\r\n            pan: true,\r\n            linkShape: \"diagonal\",\r\n            linkWidth: (node) => 2.5 - node.depth * 0.5,\r\n            linkColor: (node) => {\r\n              const colors = {\r\n                0: \"rgba(0, 82, 255, 0.8)\", // 亮蓝色\r\n                1: \"rgba(0, 150, 0, 0.8)\", // 亮绿色\r\n                2: \"rgba(255, 102, 0, 0.8)\", // 亮橙色\r\n              };\r\n              return colors[node.depth] || \"rgba(128, 0, 255, 0.8)\";\r\n            },\r\n          },\r\n          root\r\n        );\r\n\r\n        // 修改初始化动画部分\r\n        setTimeout(() => {\r\n          mm.fit(); // 适应视图大小\r\n\r\n          // 重新设置数据以触发重绘\r\n          const fitRatio = 0.95; // 留出一些边距\r\n          const { minX, maxX, minY, maxY } = mm.state;\r\n          const width = maxX - minX;\r\n          const height = maxY - minY;\r\n          const containerWidth = svg.clientWidth;\r\n          const containerHeight = svg.clientHeight;\r\n\r\n          // 计算合适的缩放比例\r\n          const scale = Math.min(\r\n            (containerWidth / width) * fitRatio,\r\n            (containerHeight / height) * fitRatio\r\n          );\r\n\r\n          // 更新数据以应用新的缩放\r\n          mm.setData(root, {\r\n            initialScale: scale,\r\n            initialPosition: [\r\n              (containerWidth - width * scale) / 2,\r\n              (containerHeight - height * scale) / 2,\r\n            ],\r\n          });\r\n        }, 100);\r\n\r\n        // 监听窗口大小变化\r\n        const resizeHandler = () => mm.fit();\r\n        window.addEventListener(\"resize\", resizeHandler);\r\n\r\n        // 组件销毁时清理\r\n        this.$once(\"hook:beforeDestroy\", () => {\r\n          window.removeEventListener(\"resize\", resizeHandler);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Markmap rendering error:\", error);\r\n        this.$message.error(\"思维导图渲染失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    // 打开新浪舆情通\r\n    openSina() {\r\n      window.open(this.sinaUrl, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.two {\r\n  height: 100%;\r\n  width: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .left {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .center {\r\n    margin: 0 11px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .center-top {\r\n      height: 150px;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      align-items: center;\r\n\r\n      .top-content {\r\n        position: relative;\r\n        width: 315px;\r\n        height: 98px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg1.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bg1 {\r\n        position: absolute;\r\n        top: 17px;\r\n        left: 43px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg2.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .top-content-number {\r\n        font-size: 24px;\r\n        color: #00e5ff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 44px;\r\n      }\r\n\r\n      .top-content-name {\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .bsContentBox2 {\r\n      background-image: url(\"../../assets/bigScreenSanhao/contentBg1.png\");\r\n\r\n      .bsContentContent {\r\n        height: calc((100% - 43px) / 2);\r\n      }\r\n\r\n      .kejidongtai-box {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .bsContentBox,\r\n  .bsContentBox2 {\r\n    background-image: url(\"../../assets/bigScreenSanhao/contentBg.png\");\r\n    background-size: 100% 100%;\r\n\r\n    .bsContentTitle {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n    }\r\n  }\r\n\r\n  .bsContentBox1 {\r\n    flex: 1;\r\n\r\n    .bsContentTitle1 {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      background-image: url(\"../../assets/bigScreenSanhao/title1.png\");\r\n      background-size: 100% 100%;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n\r\n        span {\r\n          font-weight: normal;\r\n          cursor: pointer;\r\n          color: rgba(0, 171, 244, 0.5);\r\n        }\r\n\r\n        .titleColor {\r\n          font-weight: 800;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n\r\n      .trump-view-container {\r\n        height: 300px;\r\n      }\r\n\r\n      .view-tree-container {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        & > div {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        z-index: 99;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      // Tab 按钮样式\r\n      .tab-buttons {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .tab-button {\r\n          padding: 8px 8px;\r\n          background: rgba(0, 171, 244, 0.2);\r\n          border: 1px solid rgba(0, 171, 244, 0.5);\r\n          border-radius: 4px;\r\n          color: rgba(0, 171, 244, 0.8);\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 171, 244, 0.3);\r\n            color: #00abf4;\r\n          }\r\n\r\n          &.active {\r\n            background: rgba(0, 171, 244, 0.5);\r\n            color: #ffffff;\r\n            border-color: #00abf4;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tab 内容样式\r\n      .tab-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .trump-view-container {\r\n          height: 300px;\r\n        }\r\n\r\n        .view-tree-container {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          & > div {\r\n            flex: 1;\r\n          }\r\n        }\r\n\r\n        .markmap-svg {\r\n          width: 100%;\r\n          height: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 热点推荐滚动列表样式\r\n  .remengwenzhang-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remengwenzhang-box1 {\r\n    width: 100%;\r\n    height: 230px;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: url(\"../../assets/bigScreenTwo/dialogBackground.png\") no-repeat;\r\n  background-size: 100% 100% !important;\r\n  background-size: cover;\r\n  height: 800px;\r\n\r\n  .el-dialog__header {\r\n    background-color: #1d233400;\r\n    font-size: 30px;\r\n    color: #ffffff;\r\n    line-height: 100px;\r\n    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);\r\n    height: 100px;\r\n\r\n    .el-dialog__title {\r\n      display: inline-block;\r\n      width: calc(100% - 100px);\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    background-color: #2a304000;\r\n    color: #f2f2f2;\r\n    height: calc(100% - 140px);\r\n    overflow: hidden;\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    background-color: #1d233400;\r\n    padding: 18px 20px;\r\n  }\r\n\r\n  .el-button {\r\n    background-color: #002766;\r\n    color: #fff;\r\n    border: 0px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    background: url(\"../../assets/bigScreenTwo/关闭小.png\") no-repeat;\r\n    background-size: 100% 100% !important;\r\n    background-size: cover;\r\n    width: 31px;\r\n    height: 31px;\r\n    top: 16px;\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-art {\r\n  background: #1d293b;\r\n  padding: 20px;\r\n  height: 590px;\r\n  overflow-y: auto;\r\n  line-height: 1.8em;\r\n  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,\r\n    sans-serif;\r\n\r\n  ::v-deep p {\r\n    text-indent: 2em;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::v-deep .dialog-art::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n\r\n.fz {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20px;\r\n\r\n  .text {\r\n    font-weight: 400;\r\n    font-size: 20px;\r\n    color: #ffffff;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #1d293b;\r\n    border-radius: 14px;\r\n    padding: 0 10px;\r\n    height: 28px;\r\n\r\n    .btn-minus,\r\n    .btn-plus {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 20px;\r\n      color: #ffffff;\r\n\r\n      &:hover {\r\n        color: #2f7cfe;\r\n      }\r\n    }\r\n\r\n    .font-size {\r\n      margin: 0 15px;\r\n      color: #ffffff;\r\n      font-size: 16px;\r\n      min-width: 45px;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.kejidongtai-box {\r\n  width: 100%;\r\n  // height: 45px;\r\n  // padding-top: 11px;\r\n  // margin: 3px 0;\r\n  display: flex;\r\n  // justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  column-gap: 10px;\r\n  row-gap: 10px;\r\n  padding: 10px;\r\n  margin-top: 10px;\r\n\r\n  .kejidongtai-button {\r\n    // width: 111px;\r\n    height: 33px;\r\n    line-height: 33px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan1.png\");\r\n    background-size: 100% 100%;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .active {\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan2.png\");\r\n  }\r\n}\r\n\r\n:deep(.markmap-node) {\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n:deep(.markmap-node-circle) {\r\n  fill: transparent; // 修改节点背景为透明\r\n  stroke-width: 2px;\r\n}\r\n\r\n:deep(.markmap-node-text) {\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\r\n    \"Helvetica Neue\", Arial;\r\n\r\n  tspan {\r\n    fill: #333 !important; // 修改文字颜色为深色\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.markmap-link) {\r\n  fill: none;\r\n  stroke-width: 2.5px; // 加粗连线\r\n}\r\n\r\n// 根节点样式\r\n:deep(.markmap-node[data-depth=\"0\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #0052ff; // 亮蓝色\r\n    stroke-width: 3px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 20px !important;\r\n    font-weight: bold !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 二级节点样式\r\n:deep(.markmap-node[data-depth=\"1\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #009600; // 亮绿色\r\n    stroke-width: 2.5px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 三级节点样式\r\n:deep(.markmap-node[data-depth=\"2\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #ff6600; // 亮橙色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 16px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 其他层级节点样式\r\n:deep(.markmap-node[data-depth=\"3\"]),\r\n:deep(.markmap-node[data-depth=\"4\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #8000ff; // 亮紫色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 14px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}