{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=template&id=6cd40d78&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1753863065927}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}