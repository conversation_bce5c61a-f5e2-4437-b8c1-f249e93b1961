{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\KeMonitor\\keMonitor.vue", "mtime": 1753859599660}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_topSeach", "_MainArticle", "_articleHistory", "_splitpanes", "_index2", "components", "topSeach", "MainArticle", "Splitpanes", "Pane", "TreeTable", "dicts", "data", "width", "isReSize", "currentPage", "pageSize", "total", "ArticleList", "treeDataTransfer", "checkList", "treeCurrentPage", "treePageSize", "treeTotal", "SeachData", "metaMode", "keyword", "timeRange", "customDay", "collectionDateType", "collectionTime", "isTechnology", "sortMode", "emotion", "<PERSON><PERSON><PERSON>", "buttonDisabled", "ActiveData", "seniorSerchFlag", "areaList", "countryList", "KeList", "funEsSeach", "tree<PERSON>uery", "filterwords", "domainList", "industryList", "showHistory", "historyList", "historyTimeout", "dialogVisible1", "historyLoading", "queryParams1", "pageNum", "total1", "historyList1", "initializationCompleted", "loading", "tableLoading", "<PERSON><PERSON><PERSON><PERSON>", "queryDebounceTimer", "isRightFilter", "isLeftReset", "selectedClassify", "selectedCountry", "savedCheckboxData", "globalLoading", "created", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "EsSeach", "getArticleHistory", "initializeData", "t0", "console", "error", "$message", "stop", "mounted", "watch", "handler", "newVal", "oldVal", "handleFilterChange", "methods", "_this2", "_callee2", "_callee2$", "_context2", "queryArticleList", "queryTreeData", "$nextTick", "handleSelectionChange", "selectedData", "operationType", "_toConsumableArray2", "currentPageIds", "map", "item", "sourceSn", "filteredCheckList", "filter", "includes", "filteredSavedData", "combinedCheckList", "concat", "combinedSavedData", "deduplicateBySourceSn", "scrollToTopImmediately", "dataArray", "seen", "Set", "has", "add", "handleReset", "queryTreeAndList", "_this3", "_callee3", "savedData", "_callee3$", "_context3", "length", "Promise", "all", "restoreFromSavedCheckboxData", "setTimeout", "_this4", "matchedItems", "for<PERSON>ach", "savedItem", "foundItem", "find", "treeItem", "push", "$refs", "treeTable", "restoreSelectionSilently", "queryTreeDataWithRestoreFromSaved", "_this5", "_callee4", "_callee4$", "_context4", "handleCurrentChange", "current", "handleSizeChange", "size", "_this6", "_callee5", "params", "res", "dataList", "mapData", "_callee5$", "_context5", "platformType", "id", "$route", "query", "menuType", "m", "dateType", "startTime", "endTime", "collectionStartTime", "collectionEndTime", "keywords", "thinkTankClassification", "countryOf<PERSON><PERSON>in", "api", "monitoringMedium", "sent", "code", "rows", "index", "Date", "now", "Math", "random", "toString", "substring", "label", "cnName", "count", "articleCount", "orderNum", "country", "url", "finish", "flag", "_this7", "_callee7", "_callee7$", "_context7", "abrupt", "clearTimeout", "_callee6", "qykjdtParams", "articleList", "_callee6$", "_context6", "isSort", "weChatName", "String", "addArticleHistory", "type", "then", "domain", "qykjdtArticleList", "_objectSpread2", "KeIntegration", "list", "trim", "deduplicateArticles", "max", "ceil", "msg", "t1", "_this8", "scrollBoxElement", "document", "querySelector", "scrollTop", "mainArticle", "scroll", "rightMain", "getArea", "_this9", "_callee8", "Response", "_callee8$", "_context8", "getAreaList", "warn", "_this10", "_callee9", "_callee9$", "_context9", "listArticleHistory", "filterNode", "value", "indexOf", "resetting", "removeHistory", "_this11", "_callee10", "_callee10$", "_context10", "delArticleHistory", "focus", "getArticleHistory1", "showHistoryList", "hideHistoryList", "_this12", "keywordsChange", "clearHistory", "_this13", "_callee11", "_callee11$", "_context11", "cleanArticleHistory", "moreHistory", "_this14", "_callee12", "response", "_callee12$", "_context12", "openUrl", "window", "open", "handleFilterSearch", "handleClassifyChange", "classifyValue", "handleCountryChange", "countryValue", "handleTreeCurrentChange", "page", "handleTreePageSizeChange", "openNewView", "docId", "sourceType", "handleHistoryPagination", "articles", "titleMap", "Map", "result", "article", "cleanTitle", "title", "replace", "get", "set", "originalTitle", "_ref2"], "sources": ["src/views/KeMonitor/keMonitor.vue"], "sourcesContent": ["<template>\r\n  <div\r\n    v-loading=\"globalLoading\"\r\n    element-loading-text=\"数据加载中\"\r\n    v-if=\"funEsSeach\"\r\n  >\r\n    <splitpanes class=\"default-theme\">\r\n      <pane\r\n        class=\"leftLink\"\r\n        ref=\"leftLink\"\r\n        min-size=\"20\"\r\n        max-size=\"50\"\r\n        size=\"28\"\r\n        v-if=\"!$route.query.domain\"\r\n      >\r\n        <TreeTable\r\n          ref=\"treeTable\"\r\n          :data=\"treeDataTransfer\"\r\n          :total=\"treeTotal\"\r\n          :current-page=\"treeCurrentPage\"\r\n          :page-size=\"treePageSize\"\r\n          :loading=\"loading\"\r\n          :selected-sources=\"savedCheckboxData\"\r\n          row-key=\"id\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          @reset=\"handleReset\"\r\n          @size-change=\"handleTreePageSizeChange\"\r\n          @current-change=\"handleTreeCurrentChange\"\r\n          @filter-search=\"handleFilterSearch\"\r\n          @classify-change=\"handleClassifyChange\"\r\n          @country-change=\"handleCountryChange\"\r\n        />\r\n      </pane>\r\n      <pane\r\n        :min-size=\"$route.query.domain ? '100' : '50'\"\r\n        :max-size=\"$route.query.domain ? '100' : '80'\"\r\n        :size=\"$route.query.domain ? '100' : '72'\"\r\n      >\r\n        <div\r\n          class=\"rightMain\"\r\n          style=\"margin-left: 0; overflow-y: auto\"\r\n          ref=\"rightMain\"\r\n          v-loading=\"tableLoading\"\r\n          element-loading-text=\"数据加载中\"\r\n        >\r\n          <div class=\"toolBox\" v-if=\"!$route.query.domain\">\r\n            <!-- <div class=\"title\" :style=\"{ height: ActiveData.title ? '' : '50px' }\">\r\n              <p v-if=\"ActiveData.title\">{{ ActiveData.title }}</p>\r\n              <p v-else></p>\r\n            </div> -->\r\n            <div class=\"mainTool\">\r\n              <p>\r\n                发布日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 10 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 10\"\r\n                  >全部</el-button\r\n                >\r\n                <!-- <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 7 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 7\"\r\n                  >近三个月</el-button\r\n                >\r\n                <el-button\r\n                  v-if=\"$route.query.menuType && $route.query.menuType === '8'\"\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 8 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 8\"\r\n                  >近半年</el-button\r\n                > -->\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.timeRange == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.timeRange = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.customDay\"\r\n                  v-if=\"SeachData.timeRange == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <p>\r\n                采集日期:\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 0 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 0\"\r\n                  >24小时</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 1 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 1\"\r\n                  >今天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 2 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 2\"\r\n                  >近2天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 4 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 4\"\r\n                  >近7天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 5 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 5\"\r\n                  >近30天</el-button\r\n                >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  :type=\"SeachData.collectionDateType == 6 ? 'primary' : ''\"\r\n                  @click=\"SeachData.collectionDateType = 6\"\r\n                  >自定义</el-button\r\n                >\r\n                <el-date-picker\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  v-model=\"SeachData.collectionTime\"\r\n                  v-if=\"SeachData.collectionDateType == 6\"\r\n                  style=\"display: inline-block; width: 320px; margin-left: 10px\"\r\n                  size=\"mini\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  unlink-panels\r\n                  clearable\r\n                ></el-date-picker>\r\n              </p>\r\n              <div style=\"display: flex\">\r\n                <p style=\"margin-right: 30px; margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信优选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.isTechnology\" size=\"small\">\r\n                    <el-radio-button\r\n                      v-for=\"dict in dict.type.is_technology\"\r\n                      :label=\"dict.value\"\r\n                      :key=\"'is_technology' + dict.value\"\r\n                      >{{ dict.label }}</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"null\" :key=\"'is_technology3'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n                <p style=\"margin-top: 0\">\r\n                  <span\r\n                    style=\"\r\n                      width: 60px;\r\n                      display: inline-block;\r\n                      text-align: right;\r\n                      margin-right: 5px;\r\n                    \"\r\n                    >小信精选:</span\r\n                  >\r\n                  <el-radio-group v-model=\"SeachData.emotion\" size=\"small\">\r\n                    <el-radio-button :label=\"'1'\" :key=\"'is_emotion1'\"\r\n                      >选中</el-radio-button\r\n                    >\r\n                    <el-radio-button :label=\"'0'\" :key=\"'is_emotion0'\"\r\n                      >全部</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </p>\r\n              </div>\r\n              <div class=\"keyword\">\r\n                <span\r\n                  style=\"\r\n                    width: 60px;\r\n                    display: inline-block;\r\n                    text-align: right;\r\n                    margin-right: 5px;\r\n                  \"\r\n                  >关键词:</span\r\n                >\r\n                <el-input\r\n                  ref=\"keywordRef\"\r\n                  placeholder=\"请输入关键词,使用逗号分割(英文)\"\r\n                  style=\"width: 430px\"\r\n                  v-model=\"SeachData.keyword\"\r\n                  @focus=\"showHistoryList()\"\r\n                  @blur=\"hideHistoryList()\"\r\n                >\r\n                </el-input>\r\n                <el-button\r\n                  type=\"primary\"\r\n                  size=\"mini\"\r\n                  @click=\"funEsSeach()\"\r\n                  :loading=\"buttonDisabled\"\r\n                  style=\"margin-left: 10px; height: 36px\"\r\n                  >搜索</el-button\r\n                >\r\n                <div class=\"history\" v-show=\"showHistory\">\r\n                  <div\r\n                    class=\"historyItem\"\r\n                    v-for=\"(history, index) in historyList\"\r\n                    :key=\"index\"\r\n                    v-loading=\"historyLoading\"\r\n                  >\r\n                    <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n                      {{ history.keyword }}\r\n                    </div>\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"removeHistory(history, 1)\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >删除</el-button\r\n                    >\r\n                  </div>\r\n                  <div class=\"historyItem\">\r\n                    <el-button type=\"text\" @click=\"moreHistory()\"\r\n                      >更多</el-button\r\n                    >\r\n                    <el-button\r\n                      type=\"text\"\r\n                      @click=\"clearHistory()\"\r\n                      style=\"color: #999; font-size: 12px\"\r\n                      >清空</el-button\r\n                    >\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"keyword-tip\">\r\n                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"btn\">\r\n              <el-button size=\"mini\" @click=\"resetting\">重置</el-button>\r\n              <el-button\r\n                type=\"primary\"\r\n                size=\"mini\"\r\n                @click=\"funEsSeach('filter')\"\r\n                :loading=\"buttonDisabled\"\r\n                >搜索</el-button\r\n              >\r\n            </div> -->\r\n          </div>\r\n          <MainArticle\r\n            v-if=\"$route.query.menuType\"\r\n            :flag=\"'artificialIntelligence'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n          <MainArticle\r\n            v-else\r\n            :flag=\"'MonitorUse'\"\r\n            :currentPage=\"currentPage\"\r\n            :pageSize=\"pageSize\"\r\n            :total=\"total\"\r\n            :ArticleList=\"ArticleList\"\r\n            :keywords=\"SeachData.keyword\"\r\n            @handleCurrentChange=\"handleCurrentChange\"\r\n            @handleSizeChange=\"handleSizeChange\"\r\n            @Refresh=\"funEsSeach()\"\r\n            :SeachData=\"SeachData\"\r\n            ref=\"mainArticle\"\r\n          ></MainArticle>\r\n        </div>\r\n      </pane>\r\n    </splitpanes>\r\n\r\n    <el-dialog\r\n      title=\"关键词历史\"\r\n      :visible.sync=\"dialogVisible1\"\r\n      width=\"570px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"history\" v-loading=\"historyLoading\">\r\n        <div\r\n          class=\"historyItem\"\r\n          v-for=\"(history, index) in historyList1\"\r\n          :key=\"index\"\r\n        >\r\n          <div @click=\"keywordsChange(history)\" class=\"historyText\">\r\n            {{ history.keyword }}\r\n          </div>\r\n          <el-button type=\"text\" @click=\"removeHistory(history, 2)\"\r\n            >删除</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n      <pagination\r\n        v-show=\"total1 > 0\"\r\n        :total=\"total1\"\r\n        :page.sync=\"queryParams1.pageNum\"\r\n        :limit.sync=\"queryParams1.pageSize\"\r\n        :background=\"false\"\r\n        @pagination=\"getArticleHistory1\"\r\n        :layout=\"'total, prev, pager, next'\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport api from \"@/api/ScienceApi/index.js\";\r\nimport topSeach from \"@/views/components/topSeach.vue\";\r\nimport MainArticle from \"../components/MainArticle.vue\";\r\nimport {\r\n  listArticleHistory,\r\n  delArticleHistory,\r\n  addArticleHistory,\r\n  cleanArticleHistory,\r\n} from \"@/api/article/articleHistory\";\r\nimport { Splitpanes, Pane } from \"splitpanes\";\r\nimport \"splitpanes/dist/splitpanes.css\";\r\nimport TreeTable from \"@/components/TreeTable/index.vue\";\r\n\r\nexport default {\r\n  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },\r\n  dicts: [\"is_technology\"],\r\n  data() {\r\n    return {\r\n      width: \"258\",\r\n      isReSize: false,\r\n      /* 文章主体组件数据 */\r\n      currentPage: 1,\r\n      pageSize: 50,\r\n      total: 0,\r\n      ArticleList: [],\r\n      treeDataTransfer: [],\r\n      checkList: [],\r\n      /* 树形分页数据 */\r\n      treeCurrentPage: 1,\r\n      treePageSize: 100,\r\n      treeTotal: 0,\r\n\r\n      /* 搜索组件数据 */\r\n      SeachData: {\r\n        metaMode: \"\" /* 匹配模式 */,\r\n        keyword: \"\" /* 关键词 */,\r\n        timeRange: 4 /* 时间范围 */,\r\n        customDay: [] /* 自定义天 */,\r\n        collectionDateType: null /* 时间范围 */,\r\n        collectionTime: [] /* 自定义天 */,\r\n        isTechnology: \"1\",\r\n        sortMode: \"4\",\r\n        emotion: \"0\",\r\n        hasCache: \"0\",\r\n      } /* 搜索条件 */,\r\n      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */\r\n      buttonDisabled: false /* 按钮防抖 */,\r\n      ActiveData: {},\r\n      seniorSerchFlag: false /* 普通检索或高级检索 */,\r\n      areaList: [] /* 国内地区 */,\r\n      countryList: [] /* 国家或地区 */,\r\n      KeList: [],\r\n      funEsSeach: null,\r\n      treeQuery: {\r\n        filterwords: \"\", // 添加树搜索关键字\r\n      },\r\n      domainList: [],\r\n      industryList: [],\r\n      showHistory: false,\r\n      historyList: [],\r\n      historyTimeout: null,\r\n      dialogVisible1: false,\r\n      historyLoading: false,\r\n      queryParams1: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      total1: 0,\r\n      historyList1: [],\r\n      initializationCompleted: false, // 标记初始化是否完成\r\n      // 从Wechat.vue同步的属性\r\n      loading: false, // 树组件loading状态\r\n      tableLoading: false, // 表格loading状态\r\n      isQuerying: false, // 查询防抖\r\n      queryDebounceTimer: null, // 查询防抖定时器\r\n      isRightFilter: false, // 标记右侧筛选条件是否发生变化\r\n      isLeftReset: false, // 标记左侧树是否重置\r\n      selectedClassify: null, // 选中的数据源分类\r\n      selectedCountry: null, // 选中的国家\r\n      /* 保存的勾选数据（永久保存，只有特定操作才更新） */\r\n      savedCheckboxData: [],\r\n      globalLoading: false,\r\n    };\r\n  },\r\n  async created() {\r\n    try {\r\n      // 初始化funEsSeach方法\r\n      this.funEsSeach = this.EsSeach;\r\n\r\n      // 先加载基础数据\r\n      this.getArticleHistory();\r\n\r\n      // if (this.$route.query.menuType && this.$route.query.menuType === \"8\") {\r\n      //   this.SeachData.timeRange = 7;\r\n      // }\r\n\r\n      // 加载树数据和内容数据\r\n      await this.initializeData();\r\n\r\n      // 标记初始化完成，这样watch监听器才会开始工作\r\n      this.initializationCompleted = true;\r\n    } catch (error) {\r\n      console.error(\"组件初始化失败:\", error);\r\n      this.$message.error(\"初始化失败，请刷新页面重试\");\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    // TreeTable 组件不需要特殊的状态检查\r\n  },\r\n  watch: {\r\n    // 监听筛选条件变化\r\n    \"SeachData.timeRange\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.collectionDateType\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.isTechnology\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n    \"SeachData.emotion\": {\r\n      handler(newVal, oldVal) {\r\n        if (!this.initializationCompleted || newVal === oldVal) return;\r\n        this.handleFilterChange();\r\n      },\r\n    },\r\n  },\r\n  methods: {\r\n    // 初始化数据\r\n    async initializeData() {\r\n      try {\r\n        // 加载文章列表（内部已经处理了 tableLoading）\r\n        this.queryArticleList();\r\n        // 加载树数据\r\n        await this.queryTreeData();\r\n        // 等待树组件完全渲染\r\n        await this.$nextTick();\r\n      } catch (error) {\r\n        console.error(\"初始化数据失败:\", error);\r\n        this.$message.error(\"初始化失败，请刷新页面重试\");\r\n      }\r\n    },\r\n\r\n    // TreeTable 组件事件处理方法\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selectedData, operationType) {\r\n      if (operationType === \"row-click\" || operationType === \"clear-all\") {\r\n        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      } else if (\r\n        operationType === \"checkbox-change\" ||\r\n        operationType === \"select-all\"\r\n      ) {\r\n        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中\r\n        // 先从保存的数据中移除当前页面的所有数据\r\n        const currentPageIds = this.treeDataTransfer.map(\r\n          (item) => item.sourceSn\r\n        );\r\n        const filteredCheckList = this.checkList.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n        const filteredSavedData = this.savedCheckboxData.filter(\r\n          (item) => !currentPageIds.includes(item.sourceSn)\r\n        );\r\n\r\n        // 然后添加当前页面新选中的数据\r\n        const combinedCheckList = [...filteredCheckList, ...selectedData];\r\n        const combinedSavedData = [...filteredSavedData, ...selectedData];\r\n\r\n        // 对合并后的数据进行去重处理\r\n        this.checkList = this.deduplicateBySourceSn(combinedCheckList);\r\n        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);\r\n      } else {\r\n        // 默认情况：直接替换（兼容性处理）\r\n        this.checkList = [...selectedData];\r\n        this.savedCheckboxData = [...selectedData];\r\n      }\r\n\r\n      // 重置页码并查询内容\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      if (!this.isRightFilter) {\r\n        this.queryArticleList(\"sourceItemChanged\");\r\n      }\r\n    },\r\n\r\n    // 根据sourceSn去重的辅助方法\r\n    deduplicateBySourceSn(dataArray) {\r\n      const seen = new Set();\r\n      return dataArray.filter((item) => {\r\n        if (seen.has(item.sourceSn)) {\r\n          return false;\r\n        }\r\n        seen.add(item.sourceSn);\r\n        return true;\r\n      });\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      // 先清空过滤关键字，避免触发 handleFilterSearch\r\n      this.treeQuery.filterwords = \"\";\r\n      this.selectedClassify = null;\r\n      this.selectedCountry = null;\r\n\r\n      // 然后设置重置标记\r\n      this.isLeftReset = true;\r\n\r\n      // 清空选中状态\r\n      this.checkList = [];\r\n\r\n      // 清空保存的勾选数据（永久保存）\r\n      this.savedCheckboxData = [];\r\n\r\n      // 重置页码并查询列表数据\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.scrollToTopImmediately();\r\n\r\n      // 重新查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 处理筛选条件变化 - 来自右侧筛选条件的变化\r\n    handleFilterChange() {\r\n      this.isRightFilter = true; // 标记右侧筛选条件发生变化\r\n\r\n      // 不再保存当前选中状态，使用永久保存的勾选数据\r\n      // 永久保存的勾选数据会在查询后自动恢复\r\n\r\n      // 重置分页到第一页\r\n      this.currentPage = 1;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"0\";\r\n\r\n      // 滚动到顶部\r\n      this.scrollToTopImmediately();\r\n\r\n      // 同时查询树和列表\r\n      this.queryTreeAndList();\r\n    },\r\n\r\n    // 同时查询树和列表 - 直接从Wechat.vue复制\r\n    async queryTreeAndList() {\r\n      try {\r\n        // 保存当前的永久勾选数据，避免在查询过程中丢失\r\n        const savedData = [...this.savedCheckboxData];\r\n\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (savedData && savedData.length > 0) {\r\n          this.checkList = [...savedData];\r\n        } else {\r\n          // 如果没有永久保存的勾选数据，清空选中状态\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 同时查询树数据和右侧列表（保持性能优势）\r\n        await Promise.all([\r\n          this.queryTreeData(),\r\n          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading\r\n        ]);\r\n\r\n        // 确保永久保存的勾选数据不会丢失\r\n        this.savedCheckboxData = savedData;\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n\r\n        // 查询完成后重置右侧筛选标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      } catch (error) {\r\n        console.error(\"同时查询树和列表失败:\", error);\r\n        this.$message.error(\"查询失败，请重试\");\r\n        // 即使出错也要重置标记\r\n        this.isRightFilter = false;\r\n        setTimeout(() => {\r\n          this.isLeftReset = false;\r\n        }, 300);\r\n      }\r\n    },\r\n\r\n    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）\r\n    restoreFromSavedCheckboxData() {\r\n      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // 在当前树数据中查找匹配的项\r\n      const matchedItems = [];\r\n      this.savedCheckboxData.forEach((savedItem) => {\r\n        const foundItem = this.treeDataTransfer.find(\r\n          (treeItem) => treeItem.sourceSn === savedItem.sourceSn\r\n        );\r\n        if (foundItem) {\r\n          matchedItems.push(foundItem);\r\n        }\r\n      });\r\n\r\n      if (matchedItems.length > 0) {\r\n        // 更新选中列表（此时 checkList 已经在查询前恢复过了）\r\n        this.checkList = matchedItems;\r\n        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）\r\n        this.$nextTick(() => {\r\n          if (this.$refs.treeTable) {\r\n            this.$refs.treeTable.restoreSelectionSilently(matchedItems);\r\n          }\r\n        });\r\n      } else {\r\n        // 如果没有匹配项，清空选中状态\r\n        this.checkList = [];\r\n      }\r\n    },\r\n\r\n    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据\r\n\r\n    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）\r\n    async queryTreeDataWithRestoreFromSaved() {\r\n      try {\r\n        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.checkList = [...this.savedCheckboxData];\r\n        } else {\r\n          this.checkList = [];\r\n        }\r\n\r\n        // 查询树数据\r\n        await this.queryTreeData();\r\n\r\n        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态\r\n        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n          this.restoreFromSavedCheckboxData();\r\n        }\r\n      } catch (error) {\r\n        console.error(\r\n          \"查询树数据并从永久保存的勾选数据恢复选中状态失败:\",\r\n          error\r\n        );\r\n      }\r\n    },\r\n\r\n    // 分页处理\r\n    handleCurrentChange(current) {\r\n      this.currentPage = current;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    handleSizeChange(size) {\r\n      this.pageSize = size;\r\n      this.currentPage = 1;\r\n      this.scrollToTopImmediately();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    // 查询树数据\r\n    async queryTreeData() {\r\n      this.loading = true;\r\n      try {\r\n        const params = {\r\n          pageNum: this.treeCurrentPage,\r\n          pageSize: this.treePageSize,\r\n          platformType: 1,\r\n          id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n          m: 1,\r\n          dateType:\r\n            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n          startTime: this.SeachData.customDay\r\n            ? this.SeachData.customDay[0]\r\n            : \"\",\r\n          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : \"\",\r\n          collectionDateType:\r\n            this.SeachData.collectionDateType != 6\r\n              ? this.SeachData.collectionDateType\r\n              : \"\",\r\n          collectionStartTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[0]\r\n            : \"\",\r\n          collectionEndTime: this.SeachData.collectionTime\r\n            ? this.SeachData.collectionTime[1]\r\n            : \"\",\r\n          keywords: this.SeachData.keyword,\r\n          isTechnology: this.SeachData.isTechnology,\r\n          emotion: this.SeachData.emotion,\r\n          // 添加关键字过滤参数\r\n          filterwords: this.treeQuery.filterwords || \"\",\r\n          // 添加数据源分类参数\r\n          thinkTankClassification: this.selectedClassify,\r\n          // 添加国家筛选参数\r\n          countryOfOrigin: this.selectedCountry,\r\n          hasCache: this.SeachData.hasCache,\r\n        };\r\n\r\n        if (this.$route.query.menuType) {\r\n          params.menuType = this.$route.query.menuType;\r\n        }\r\n\r\n        const res = await api.monitoringMedium(params);\r\n\r\n        if (res.code === 200) {\r\n          const dataList = res.rows || [];\r\n          const total = res.total || 0;\r\n\r\n          const mapData = (data) =>\r\n            data.map((item, index) => ({\r\n              id: `${\r\n                item.sourceSn || \"unknown\"\r\n              }_${index}_${Date.now()}_${Math.random()\r\n                .toString(36)\r\n                .substring(2, 11)}`, // 确保绝对唯一性\r\n              label: item.cnName,\r\n              count: item.articleCount || 0,\r\n              orderNum: item.orderNum,\r\n              country: item.countryOfOrigin || null,\r\n              sourceSn: item.sourceSn,\r\n              url: item.url || null,\r\n            }));\r\n\r\n          this.treeDataTransfer = mapData(dataList);\r\n          this.treeTotal = total;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"查询树数据失败:\", error);\r\n        this.$message.error(\"获取数据源失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 查询文章列表（带防抖）\r\n    async queryArticleList(flag) {\r\n      // 防止重复查询\r\n      if (this.isQuerying) {\r\n        return;\r\n      }\r\n\r\n      if (!flag) {\r\n        this.tableLoading = true;\r\n      }\r\n\r\n      // 清除之前的防抖定时器\r\n      if (this.queryDebounceTimer) {\r\n        clearTimeout(this.queryDebounceTimer);\r\n      }\r\n\r\n      // 设置防抖，300ms后执行查询\r\n      this.queryDebounceTimer = setTimeout(async () => {\r\n        try {\r\n          if (flag === \"sourceItemChanged\") {\r\n            this.globalLoading = true;\r\n          }\r\n\r\n          this.isQuerying = true;\r\n\r\n          const params = {\r\n            m: 1,\r\n            pageNum: this.currentPage,\r\n            pageSize: this.pageSize,\r\n            id: this.$route.query.menuType ? \"1\" : this.$route.query.id,\r\n            isSort: this.SeachData.sortMode,\r\n            dateType:\r\n              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : \"\",\r\n            startTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[0]\r\n              : \"\",\r\n            endTime: this.SeachData.customDay\r\n              ? this.SeachData.customDay[1]\r\n              : \"\",\r\n            collectionDateType:\r\n              this.SeachData.collectionDateType != 6\r\n                ? this.SeachData.collectionDateType\r\n                : \"\",\r\n            collectionStartTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[0]\r\n              : \"\",\r\n            collectionEndTime: this.SeachData.collectionTime\r\n              ? this.SeachData.collectionTime[1]\r\n              : \"\",\r\n            keywords: this.SeachData.keyword,\r\n            isTechnology: this.SeachData.isTechnology,\r\n            emotion: this.SeachData.emotion,\r\n            platformType: 1,\r\n          };\r\n\r\n          if (this.$route.query.menuType) {\r\n            params.menuType = this.$route.query.menuType;\r\n          }\r\n\r\n          // 使用永久保存的勾选数据构建查询参数\r\n          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n            const data = this.savedCheckboxData.map((item) => item.label);\r\n            const sourceSn = this.savedCheckboxData.map(\r\n              (item) => item.sourceSn\r\n            );\r\n\r\n            params.weChatName = String(data);\r\n            params.sourceSn = String(sourceSn);\r\n          }\r\n\r\n          // 记录关键词历史\r\n          if (params.keywords) {\r\n            addArticleHistory({ keyword: params.keywords, type: 2 }).then(\r\n              () => {\r\n                this.getArticleHistory();\r\n              }\r\n            );\r\n          }\r\n\r\n          let qykjdtParams;\r\n\r\n          if (this.$route.query.domain) {\r\n            qykjdtParams = {\r\n              pageNum: this.currentPage,\r\n              pageSize: this.pageSize,\r\n              sourceSn: this.$route.query.domain,\r\n              isSort: this.SeachData.sortMode,\r\n            };\r\n          }\r\n\r\n          const res = this.$route.query.domain\r\n            ? await api.qykjdtArticleList({ ...qykjdtParams })\r\n            : await api.KeIntegration({ ...params });\r\n\r\n          if (res.code == 200) {\r\n            let articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              articleList = res.rows || [];\r\n            } else {\r\n              articleList = res.data.list || [];\r\n            }\r\n\r\n            // 去重逻辑：只有在没有关键词搜索时才进行去重\r\n            if (\r\n              !this.SeachData.keyword ||\r\n              this.SeachData.keyword.trim() === \"\"\r\n            ) {\r\n              articleList = this.deduplicateArticles(articleList);\r\n            }\r\n\r\n            this.ArticleList = articleList;\r\n\r\n            if (this.$route.query.domain) {\r\n              this.total = res.total || 0;\r\n            } else {\r\n              this.total = res.data.total || 0;\r\n            }\r\n\r\n            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）\r\n            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {\r\n              this.restoreFromSavedCheckboxData();\r\n            }\r\n\r\n            // 处理分页为空的情况\r\n            if (\r\n              this.ArticleList.length == 0 &&\r\n              this.pageSize * (this.currentPage - 1) >= this.total &&\r\n              this.total != 0\r\n            ) {\r\n              this.currentPage = Math.max(\r\n                1,\r\n                Math.ceil(this.total / this.pageSize)\r\n              );\r\n              // 重新查询\r\n              await this.queryArticleList();\r\n              return; // 重新查询时不要关闭loading\r\n            }\r\n          } else {\r\n            this.$message.error(res.msg || \"获取数据失败\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"查询文章列表失败:\", error);\r\n          this.$message.error(\"查询失败，请重试\");\r\n        } finally {\r\n          this.isQuerying = false;\r\n          this.globalLoading = false;\r\n          this.tableLoading = false; // 查询完成后关闭loading\r\n          this.buttonDisabled = false;\r\n        }\r\n      }, 1000);\r\n    },\r\n\r\n    // 滚动到顶部\r\n    scrollToTopImmediately() {\r\n      this.$nextTick(() => {\r\n        // 尝试多种滚动方式确保滚动成功\r\n        const scrollBoxElement = document.querySelector(\".scollBox\");\r\n        if (scrollBoxElement) {\r\n          scrollBoxElement.scrollTop = 0;\r\n        }\r\n\r\n        // 如果MainArticle组件有scroll引用，也尝试滚动它\r\n        if (\r\n          this.$refs.mainArticle &&\r\n          this.$refs.mainArticle.$refs &&\r\n          this.$refs.mainArticle.$refs.scroll\r\n        ) {\r\n          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;\r\n        }\r\n\r\n        // 滚动整个右侧区域\r\n        const rightMain = document.querySelector(\".rightMain\");\r\n        if (rightMain) {\r\n          rightMain.scrollTop = 0;\r\n        }\r\n      });\r\n    },\r\n    async getArea() {\r\n      try {\r\n        const Response = await api.getAreaList();\r\n        if (Response && Response.code == 200 && Response.data) {\r\n          this.areaList = Response.data[0] || [];\r\n          this.countryList = Response.data[1] || [];\r\n        } else {\r\n          console.warn(\"获取地区数据失败或数据为空\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"获取区域数据失败:\", err);\r\n        this.$message.error(\"地区数据获取失败\");\r\n      }\r\n    },\r\n\r\n    async getArticleHistory() {\r\n      try {\r\n        const res = await listArticleHistory({\r\n          pageNum: 1,\r\n          pageSize: 5,\r\n          type: 2,\r\n        });\r\n        if (res && res.code === 200) {\r\n          this.historyList = res.rows || [];\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取历史记录失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 树节点过滤方法\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n\r\n    // 添加EsSeach方法以兼容原有调用\r\n    EsSeach(flag) {\r\n      if (flag === \"filter\") {\r\n        this.scrollToTopImmediately();\r\n        // 筛选变化时同时查询树和列表\r\n        this.queryTreeAndList();\r\n      } else {\r\n        // 其他情况只查询列表\r\n        this.scrollToTopImmediately();\r\n        this.queryArticleList();\r\n      }\r\n    },\r\n\r\n    // 重置搜索条件 - 简化版本\r\n    resetting() {\r\n      try {\r\n        this.SeachData = {\r\n          metaMode: \"\" /* 匹配模式 */,\r\n          keyword: \"\" /* 关键词 */,\r\n          timeRange: \"\" /* 时间范围 */,\r\n          customDay: [] /* 自定义天 */,\r\n          collectionDateType: null /* 时间范围 */,\r\n          collectionTime: [] /* 自定义天 */,\r\n          isTechnology: \"1\",\r\n          sortMode: \"4\",\r\n          emotion: \"0\",\r\n        };\r\n\r\n        this.currentPage = 1;\r\n        this.scrollToTopImmediately();\r\n        this.queryTreeAndList();\r\n      } catch (error) {\r\n        console.error(\"重置搜索条件时出错:\", error);\r\n        this.$message.error(\"重置搜索条件失败\");\r\n      }\r\n    },\r\n\r\n    async removeHistory(item, type) {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (item && item.id) {\r\n          await delArticleHistory([item.id]);\r\n\r\n          if (type == 1) {\r\n            if (this.$refs[\"keywordRef\"]) {\r\n              this.$refs[\"keywordRef\"].focus();\r\n            }\r\n            await this.getArticleHistory();\r\n          } else {\r\n            await this.getArticleHistory();\r\n            await this.getArticleHistory1();\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"删除历史记录时出错:\", error);\r\n        this.$message.error(\"删除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    showHistoryList() {\r\n      try {\r\n        this.showHistory = true;\r\n      } catch (error) {\r\n        console.error(\"显示历史列表时出错:\", error);\r\n      }\r\n    },\r\n\r\n    hideHistoryList() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        this.historyTimeout = setTimeout(() => {\r\n          this.showHistory = false;\r\n        }, 500);\r\n      } catch (error) {\r\n        console.error(\"隐藏历史列表时出错:\", error);\r\n        this.showHistory = false; // 确保在出错时也能隐藏列表\r\n      }\r\n    },\r\n\r\n    // 关键词历史选择 - 直接从Wechat.vue复制\r\n    keywordsChange(item) {\r\n      this.SeachData.keyword = item.keyword;\r\n      this.dialogVisible1 = false;\r\n      this.scrollToTopImmediately();\r\n      this.currentPage = 1;\r\n      // this.queryTreeAndList();\r\n      this.queryArticleList();\r\n    },\r\n\r\n    async clearHistory() {\r\n      try {\r\n        if (this.historyTimeout) {\r\n          clearTimeout(this.historyTimeout);\r\n        }\r\n\r\n        if (this.$refs[\"keywordRef\"]) {\r\n          this.$refs[\"keywordRef\"].focus();\r\n        }\r\n\r\n        await cleanArticleHistory(2);\r\n        await this.getArticleHistory();\r\n      } catch (error) {\r\n        console.error(\"清除历史记录时出错:\", error);\r\n        this.$message.error(\"清除历史记录失败\");\r\n      }\r\n    },\r\n\r\n    moreHistory() {\r\n      try {\r\n        this.historyLoading = true;\r\n        this.getArticleHistory1();\r\n        this.dialogVisible1 = true;\r\n      } catch (error) {\r\n        console.error(\"加载更多历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n      }\r\n    },\r\n\r\n    async getArticleHistory1() {\r\n      try {\r\n        this.historyLoading = true;\r\n        const response = await listArticleHistory({\r\n          ...this.queryParams1,\r\n          type: 2,\r\n        });\r\n\r\n        if (response) {\r\n          this.historyList1 = response.rows || [];\r\n          this.total1 = response.total || 0;\r\n        }\r\n\r\n        this.historyLoading = false;\r\n      } catch (error) {\r\n        console.error(\"获取文章历史记录时出错:\", error);\r\n        this.historyLoading = false;\r\n        this.$message.error(\"获取搜索历史失败\");\r\n      }\r\n    },\r\n\r\n    openUrl(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n\r\n    // 处理过滤搜索（来自 TreeTable 组件）\r\n    handleFilterSearch(keyword) {\r\n      if (this.isLeftReset) {\r\n        return;\r\n      }\r\n\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新查询参数中的 filterwords\r\n      this.treeQuery.filterwords = keyword || \"\";\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理数据源分类变化（来自 TreeTable 组件）\r\n    handleClassifyChange(classifyValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的分类\r\n      this.selectedClassify = classifyValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理国家筛选变化（来自 TreeTable 组件）\r\n    handleCountryChange(countryValue) {\r\n      // 不保存当前选中状态，使用永久保存的勾选数据\r\n\r\n      // 更新选中的国家\r\n      this.selectedCountry = countryValue;\r\n\r\n      // 重置到第一页\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n\r\n      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页页码变化\r\n    handleTreeCurrentChange(page) {\r\n      this.treeCurrentPage = page;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 处理树形分页每页大小变化\r\n    handleTreePageSizeChange(size) {\r\n      this.treePageSize = size;\r\n      this.treeCurrentPage = 1;\r\n      this.SeachData.hasCache = \"1\";\r\n      this.queryTreeDataWithRestoreFromSaved();\r\n    },\r\n\r\n    // 添加缺失的openNewView方法\r\n    openNewView(item) {\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n    },\r\n\r\n    // 添加缺失的handleHistoryPagination方法\r\n    handleHistoryPagination() {\r\n      this.getArticleHistory1();\r\n    },\r\n\r\n    // 文章去重方法\r\n    deduplicateArticles(articles) {\r\n      if (!articles || articles.length === 0) {\r\n        return articles;\r\n      }\r\n\r\n      const titleMap = new Map();\r\n      const result = [];\r\n\r\n      // 统计相同标题的文章数量\r\n      articles.forEach((article) => {\r\n        // 去除HTML标签和所有空格来比较标题\r\n        const cleanTitle = article.title\r\n          ? article.title.replace(/<[^>]*>/g, \"\").replace(/\\s+/g, \"\")\r\n          : \"\";\r\n\r\n        if (titleMap.has(cleanTitle)) {\r\n          titleMap.get(cleanTitle).count++;\r\n        } else {\r\n          titleMap.set(cleanTitle, {\r\n            article: { ...article },\r\n            count: 1,\r\n            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）\r\n          });\r\n        }\r\n      });\r\n\r\n      // 生成去重后的文章列表\r\n      titleMap.forEach(({ article, count, originalTitle }) => {\r\n        if (count > 1) {\r\n          // 如果有重复，在标题后面加上数量标记\r\n          // 使用原始标题（保持HTML格式）\r\n          article.title = `${originalTitle || \"\"}（${count}）`;\r\n        }\r\n        result.push(article);\r\n      });\r\n\r\n      return result;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.treeBox {\r\n  width: 100%;\r\n  height: calc(100vh - 176px);\r\n  overflow-y: auto;\r\n}\r\n\r\n.tree-pagination {\r\n  padding: 10px;\r\n  border-top: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  text-align: center;\r\n\r\n  ::v-deep .el-pagination {\r\n    .el-pagination__sizes {\r\n      margin-top: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.treeMain {\r\n  position: relative;\r\n}\r\n\r\n.treeQuery {\r\n  ::v-deep .el-input--mini .el-input__inner {\r\n    height: 24px;\r\n    line-height: 24px;\r\n    padding: 0 4px;\r\n  }\r\n\r\n  ::v-deep .el-input__suffix {\r\n    // height: 20px;\r\n    right: -2px;\r\n    // top: 5px;\r\n  }\r\n}\r\n\r\n.toolBox {\r\n  min-height: 130px;\r\n  height: auto;\r\n  padding-bottom: 15px;\r\n  background-color: rgb(255, 255, 255);\r\n  // box-shadow: -1px 2px 15px #cecdcd;\r\n  border-left: solid 1px rgb(221, 219, 219);\r\n\r\n  .title {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    height: 70px;\r\n    padding: 0 30px;\r\n    font-size: 19px;\r\n  }\r\n\r\n  .mainTool {\r\n    padding: 0 28px;\r\n    font-size: 14px;\r\n    color: rgb(58, 58, 58);\r\n  }\r\n\r\n  .mainToolOne {\r\n    margin-top: 15px;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: flex-start;\r\n    // align-items: center;\r\n  }\r\n\r\n  .mainToolTwo {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 40px;\r\n\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n  }\r\n\r\n  .btn {\r\n    margin: 15px 0 0 25px;\r\n  }\r\n}\r\n\r\n.keyword {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n\r\n  .history {\r\n    width: 430px;\r\n    position: absolute;\r\n    background: #fff;\r\n    z-index: 9999;\r\n    left: 65px;\r\n    border: 1px solid rgb(221, 219, 219);\r\n\r\n    .historyItem {\r\n      padding-left: 20px;\r\n\r\n      .historyText {\r\n        width: 450px;\r\n        height: 34px;\r\n        line-height: 34px;\r\n      }\r\n\r\n      &:nth-last-of-type(1) {\r\n        padding-left: 0;\r\n\r\n        ::v-deep .el-button--text {\r\n          padding: 10px 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.keyword-tip {\r\n  font-size: 14px;\r\n  color: #999;\r\n  margin-left: 65px;\r\n  line-height: 1;\r\n}\r\n\r\n.history {\r\n  width: 530px;\r\n\r\n  .historyItem {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    padding: 0 10px;\r\n    overflow: hidden;\r\n\r\n    .historyText {\r\n      width: 350px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8VA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAH,OAAA;AAMA,IAAAI,WAAA,GAAAJ,OAAA;AACAA,OAAA;AACA,IAAAK,OAAA,GAAAN,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,IAAA,EAAAA,gBAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,WAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,SAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,SAAA;MAEA;MACAC,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,SAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MACA;MACAC,cAAA;MACAC,UAAA;MACAC,eAAA;MACAC,QAAA;MACAC,WAAA;MACAC,MAAA;MACAC,UAAA;MACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,cAAA;MACAC,cAAA;MACAC,cAAA;MACAC,YAAA;QACAC,OAAA;QACApC,QAAA;MACA;MACAqC,MAAA;MACAC,YAAA;MACAC,uBAAA;MAAA;MACA;MACAC,OAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,iBAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAEA;YACAT,KAAA,CAAA1B,UAAA,GAAA0B,KAAA,CAAAW,OAAA;;YAEA;YACAX,KAAA,CAAAY,iBAAA;;YAEA;YACA;YACA;;YAEA;YAAAJ,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAa,cAAA;UAAA;YAEA;YACAb,KAAA,CAAAZ,uBAAA;YAAAoB,QAAA,CAAAE,IAAA;YAAA;UAAA;YAAAF,QAAA,CAAAC,IAAA;YAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;YAEAO,OAAA,CAAAC,KAAA,aAAAR,QAAA,CAAAM,EAAA;YACAd,KAAA,CAAAiB,QAAA,CAAAD,KAAA;UAAA;UAAA;YAAA,OAAAR,QAAA,CAAAU,IAAA;QAAA;MAAA,GAAAb,OAAA;IAAA;EAEA;EAEAc,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;IACA;MACAC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;IACA;MACAH,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA,UAAAnC,uBAAA,IAAAkC,MAAA,KAAAC,MAAA;QACA,KAAAC,kBAAA;MACA;IACA;EACA;EACAC,OAAA;IACA;IACAZ,cAAA,WAAAA,eAAA;MAAA,IAAAa,MAAA;MAAA,WAAAzB,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuB,SAAA;QAAA,WAAAxB,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApB,IAAA,GAAAoB,SAAA,CAAAnB,IAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAEA;cACAiB,MAAA,CAAAI,gBAAA;cACA;cAAAD,SAAA,CAAAnB,IAAA;cAAA,OACAgB,MAAA,CAAAK,aAAA;YAAA;cAAAF,SAAA,CAAAnB,IAAA;cAAA,OAEAgB,MAAA,CAAAM,SAAA;YAAA;cAAAH,SAAA,CAAAnB,IAAA;cAAA;YAAA;cAAAmB,SAAA,CAAApB,IAAA;cAAAoB,SAAA,CAAAf,EAAA,GAAAe,SAAA;cAEAd,OAAA,CAAAC,KAAA,aAAAa,SAAA,CAAAf,EAAA;cACAY,MAAA,CAAAT,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAa,SAAA,CAAAX,IAAA;UAAA;QAAA,GAAAS,QAAA;MAAA;IAEA;IAEA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,YAAA,EAAAC,aAAA;MACA,IAAAA,aAAA,oBAAAA,aAAA;QACA;QACA,KAAAlF,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,KAAArC,iBAAA,OAAAuC,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;MACA,WACAC,aAAA,0BACAA,aAAA,mBACA;QACA;QACA;QACA,IAAAE,cAAA,QAAArF,gBAAA,CAAAsF,GAAA,CACA,UAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAC,iBAAA,QAAAxF,SAAA,CAAAyF,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAI,iBAAA,QAAA/C,iBAAA,CAAA6C,MAAA,CACA,UAAAH,IAAA;UAAA,QAAAF,cAAA,CAAAM,QAAA,CAAAJ,IAAA,CAAAC,QAAA;QAAA,CACA;;QAEA;QACA,IAAAK,iBAAA,MAAAC,MAAA,KAAAV,mBAAA,CAAAlC,OAAA,EAAAuC,iBAAA,OAAAL,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,IAAAa,iBAAA,MAAAD,MAAA,KAAAV,mBAAA,CAAAlC,OAAA,EAAA0C,iBAAA,OAAAR,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;;QAEA;QACA,KAAAjF,SAAA,QAAA+F,qBAAA,CAAAH,iBAAA;QACA,KAAAhD,iBAAA,QAAAmD,qBAAA,CAAAD,iBAAA;MACA;QACA;QACA,KAAA9F,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;QACA,KAAArC,iBAAA,OAAAuC,mBAAA,CAAAlC,OAAA,EAAAgC,YAAA;MACA;;MAEA;MACA,KAAAtF,WAAA;MACA,KAAAqG,sBAAA;MACA,UAAAxD,aAAA;QACA,KAAAqC,gBAAA;MACA;IACA;IAEA;IACAkB,qBAAA,WAAAA,sBAAAE,SAAA;MACA,IAAAC,IAAA,OAAAC,GAAA;MACA,OAAAF,SAAA,CAAAR,MAAA,WAAAH,IAAA;QACA,IAAAY,IAAA,CAAAE,GAAA,CAAAd,IAAA,CAAAC,QAAA;UACA;QACA;QACAW,IAAA,CAAAG,GAAA,CAAAf,IAAA,CAAAC,QAAA;QACA;MACA;IACA;IAEA;IACAe,WAAA,WAAAA,YAAA;MACA;MACA,KAAAhF,SAAA,CAAAC,WAAA;MACA,KAAAmB,gBAAA;MACA,KAAAC,eAAA;;MAEA;MACA,KAAAF,WAAA;;MAEA;MACA,KAAAzC,SAAA;;MAEA;MACA,KAAA4C,iBAAA;;MAEA;MACA,KAAAjD,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAAkF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAhC,kBAAA,WAAAA,mBAAA;MACA,KAAA/B,aAAA;;MAEA;MACA;;MAEA;MACA,KAAA7C,WAAA;MACA,KAAAM,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAAkF,sBAAA;;MAEA;MACA,KAAAO,gBAAA;IACA;IAEA;IACAA,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsD,SAAA;QAAA,IAAAC,SAAA;QAAA,WAAAxD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApD,IAAA,GAAAoD,SAAA,CAAAnD,IAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAEA;cACAkD,SAAA,OAAAvB,mBAAA,CAAAlC,OAAA,EAAAuD,MAAA,CAAA5D,iBAAA,GAEA;cACA,IAAA8D,SAAA,IAAAA,SAAA,CAAAG,MAAA;gBACAL,MAAA,CAAAxG,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAAyD,SAAA;cACA;gBACA;gBACAF,MAAA,CAAAxG,SAAA;cACA;;cAEA;cAAA4G,SAAA,CAAAnD,IAAA;cAAA,OACAqD,OAAA,CAAAC,GAAA,EACAP,MAAA,CAAA1B,aAAA,IACA0B,MAAA,CAAA3B,gBAAA;cAAA,CACA;YAAA;cAEA;cACA2B,MAAA,CAAA5D,iBAAA,GAAA8D,SAAA;;cAEA;cACA,IAAAF,MAAA,CAAA5D,iBAAA,IAAA4D,MAAA,CAAA5D,iBAAA,CAAAiE,MAAA;gBACAL,MAAA,CAAAQ,4BAAA;cACA;;cAEA;cACAR,MAAA,CAAAhE,aAAA;cACAyE,UAAA;gBACAT,MAAA,CAAA/D,WAAA;cACA;cAAAmE,SAAA,CAAAnD,IAAA;cAAA;YAAA;cAAAmD,SAAA,CAAApD,IAAA;cAAAoD,SAAA,CAAA/C,EAAA,GAAA+C,SAAA;cAEA9C,OAAA,CAAAC,KAAA,gBAAA6C,SAAA,CAAA/C,EAAA;cACA2C,MAAA,CAAAxC,QAAA,CAAAD,KAAA;cACA;cACAyC,MAAA,CAAAhE,aAAA;cACAyE,UAAA;gBACAT,MAAA,CAAA/D,WAAA;cACA;YAAA;YAAA;cAAA,OAAAmE,SAAA,CAAA3C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IAEA;IACAO,4BAAA,WAAAA,6BAAA;MAAA,IAAAE,MAAA;MACA,UAAAtE,iBAAA,SAAAA,iBAAA,CAAAiE,MAAA;QACA;MACA;;MAEA;MACA,IAAAM,YAAA;MACA,KAAAvE,iBAAA,CAAAwE,OAAA,WAAAC,SAAA;QACA,IAAAC,SAAA,GAAAJ,MAAA,CAAAnH,gBAAA,CAAAwH,IAAA,CACA,UAAAC,QAAA;UAAA,OAAAA,QAAA,CAAAjC,QAAA,KAAA8B,SAAA,CAAA9B,QAAA;QAAA,CACA;QACA,IAAA+B,SAAA;UACAH,YAAA,CAAAM,IAAA,CAAAH,SAAA;QACA;MACA;MAEA,IAAAH,YAAA,CAAAN,MAAA;QACA;QACA,KAAA7G,SAAA,GAAAmH,YAAA;QACA;QACA,KAAApC,SAAA;UACA,IAAAmC,MAAA,CAAAQ,KAAA,CAAAC,SAAA;YACAT,MAAA,CAAAQ,KAAA,CAAAC,SAAA,CAAAC,wBAAA,CAAAT,YAAA;UACA;QACA;MACA;QACA;QACA,KAAAnH,SAAA;MACA;IACA;IAEA;IAEA;IACA6H,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9E,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4E,SAAA;QAAA,WAAA7E,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cAAAwE,SAAA,CAAAzE,IAAA;cAEA;cACA,IAAAsE,MAAA,CAAAlF,iBAAA,IAAAkF,MAAA,CAAAlF,iBAAA,CAAAiE,MAAA;gBACAiB,MAAA,CAAA9H,SAAA,OAAAmF,mBAAA,CAAAlC,OAAA,EAAA6E,MAAA,CAAAlF,iBAAA;cACA;gBACAkF,MAAA,CAAA9H,SAAA;cACA;;cAEA;cAAAiI,SAAA,CAAAxE,IAAA;cAAA,OACAqE,MAAA,CAAAhD,aAAA;YAAA;cAEA;cACA,IAAAgD,MAAA,CAAAlF,iBAAA,IAAAkF,MAAA,CAAAlF,iBAAA,CAAAiE,MAAA;gBACAiB,MAAA,CAAAd,4BAAA;cACA;cAAAiB,SAAA,CAAAxE,IAAA;cAAA;YAAA;cAAAwE,SAAA,CAAAzE,IAAA;cAAAyE,SAAA,CAAApE,EAAA,GAAAoE,SAAA;cAEAnE,OAAA,CAAAC,KAAA,CACA,6BAAAkE,SAAA,CAAApE,EAEA;YAAA;YAAA;cAAA,OAAAoE,SAAA,CAAAhE,IAAA;UAAA;QAAA,GAAA8D,QAAA;MAAA;IAEA;IAEA;IACAG,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAxI,WAAA,GAAAwI,OAAA;MACA,KAAAnC,sBAAA;MACA,KAAAnB,gBAAA;IACA;IAEAuD,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAzI,QAAA,GAAAyI,IAAA;MACA,KAAA1I,WAAA;MACA,KAAAqG,sBAAA;MACA,KAAAnB,gBAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAwD,MAAA;MAAA,WAAAtF,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoF,SAAA;QAAA,IAAAC,MAAA,EAAAC,GAAA,EAAAC,QAAA,EAAA7I,KAAA,EAAA8I,OAAA;QAAA,WAAAzF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuF,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArF,IAAA,GAAAqF,SAAA,CAAApF,IAAA;YAAA;cACA6E,MAAA,CAAAlG,OAAA;cAAAyG,SAAA,CAAArF,IAAA;cAEAgF,MAAA;gBACAxG,OAAA,EAAAsG,MAAA,CAAArI,eAAA;gBACAL,QAAA,EAAA0I,MAAA,CAAApI,YAAA;gBACA4I,YAAA;gBACAC,EAAA,EAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAF,EAAA;gBACAI,CAAA;gBACAC,QAAA,EACAd,MAAA,CAAAlI,SAAA,CAAAG,SAAA,QAAA+H,MAAA,CAAAlI,SAAA,CAAAG,SAAA;gBACA8I,SAAA,EAAAf,MAAA,CAAAlI,SAAA,CAAAI,SAAA,GACA8H,MAAA,CAAAlI,SAAA,CAAAI,SAAA,MACA;gBACA8I,OAAA,EAAAhB,MAAA,CAAAlI,SAAA,CAAAI,SAAA,GAAA8H,MAAA,CAAAlI,SAAA,CAAAI,SAAA;gBACAC,kBAAA,EACA6H,MAAA,CAAAlI,SAAA,CAAAK,kBAAA,QACA6H,MAAA,CAAAlI,SAAA,CAAAK,kBAAA,GACA;gBACA8I,mBAAA,EAAAjB,MAAA,CAAAlI,SAAA,CAAAM,cAAA,GACA4H,MAAA,CAAAlI,SAAA,CAAAM,cAAA,MACA;gBACA8I,iBAAA,EAAAlB,MAAA,CAAAlI,SAAA,CAAAM,cAAA,GACA4H,MAAA,CAAAlI,SAAA,CAAAM,cAAA,MACA;gBACA+I,QAAA,EAAAnB,MAAA,CAAAlI,SAAA,CAAAE,OAAA;gBACAK,YAAA,EAAA2H,MAAA,CAAAlI,SAAA,CAAAO,YAAA;gBACAE,OAAA,EAAAyH,MAAA,CAAAlI,SAAA,CAAAS,OAAA;gBACA;gBACAU,WAAA,EAAA+G,MAAA,CAAAhH,SAAA,CAAAC,WAAA;gBACA;gBACAmI,uBAAA,EAAApB,MAAA,CAAA5F,gBAAA;gBACA;gBACAiH,eAAA,EAAArB,MAAA,CAAA3F,eAAA;gBACA7B,QAAA,EAAAwH,MAAA,CAAAlI,SAAA,CAAAU;cACA;cAEA,IAAAwH,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;gBACAV,MAAA,CAAAU,QAAA,GAAAZ,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAAC,QAAA;cACA;cAAAL,SAAA,CAAApF,IAAA;cAAA,OAEAmG,cAAA,CAAAC,gBAAA,CAAArB,MAAA;YAAA;cAAAC,GAAA,GAAAI,SAAA,CAAAiB,IAAA;cAEA,IAAArB,GAAA,CAAAsB,IAAA;gBACArB,QAAA,GAAAD,GAAA,CAAAuB,IAAA;gBACAnK,KAAA,GAAA4I,GAAA,CAAA5I,KAAA;gBAEA8I,OAAA,YAAAA,QAAAnJ,IAAA;kBAAA,OACAA,IAAA,CAAA6F,GAAA,WAAAC,IAAA,EAAA2E,KAAA;oBAAA;sBACAlB,EAAA,KAAAlD,MAAA,CACAP,IAAA,CAAAC,QAAA,oBAAAM,MAAA,CACAoE,KAAA,OAAApE,MAAA,CAAAqE,IAAA,CAAAC,GAAA,SAAAtE,MAAA,CAAAuE,IAAA,CAAAC,MAAA,GACAC,QAAA,KACAC,SAAA;sBAAA;sBACAC,KAAA,EAAAlF,IAAA,CAAAmF,MAAA;sBACAC,KAAA,EAAApF,IAAA,CAAAqF,YAAA;sBACAC,QAAA,EAAAtF,IAAA,CAAAsF,QAAA;sBACAC,OAAA,EAAAvF,IAAA,CAAAqE,eAAA;sBACApE,QAAA,EAAAD,IAAA,CAAAC,QAAA;sBACAuF,GAAA,EAAAxF,IAAA,CAAAwF,GAAA;oBACA;kBAAA;gBAAA;gBAEAxC,MAAA,CAAAvI,gBAAA,GAAA4I,OAAA,CAAAD,QAAA;gBACAJ,MAAA,CAAAnI,SAAA,GAAAN,KAAA;cACA;cAAAgJ,SAAA,CAAApF,IAAA;cAAA;YAAA;cAAAoF,SAAA,CAAArF,IAAA;cAAAqF,SAAA,CAAAhF,EAAA,GAAAgF,SAAA;cAEA/E,OAAA,CAAAC,KAAA,aAAA8E,SAAA,CAAAhF,EAAA;cACAyE,MAAA,CAAAtE,QAAA,CAAAD,KAAA;YAAA;cAAA8E,SAAA,CAAArF,IAAA;cAEA8E,MAAA,CAAAlG,OAAA;cAAA,OAAAyG,SAAA,CAAAkC,MAAA;YAAA;YAAA;cAAA,OAAAlC,SAAA,CAAA5E,IAAA;UAAA;QAAA,GAAAsE,QAAA;MAAA;IAEA;IAEA;IACA1D,gBAAA,WAAAA,iBAAAmG,IAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjI,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+H,SAAA;QAAA,WAAAhI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,IAAA,GAAA4H,SAAA,CAAA3H,IAAA;YAAA;cAAA,KAEAwH,MAAA,CAAA3I,UAAA;gBAAA8I,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAA,OAAA2H,SAAA,CAAAC,MAAA;YAAA;cAIA,KAAAL,IAAA;gBACAC,MAAA,CAAA5I,YAAA;cACA;;cAEA;cACA,IAAA4I,MAAA,CAAA1I,kBAAA;gBACA+I,YAAA,CAAAL,MAAA,CAAA1I,kBAAA;cACA;;cAEA;cACA0I,MAAA,CAAA1I,kBAAA,GAAA0E,UAAA,kBAAAjE,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoI,SAAA;gBAAA,IAAA/C,MAAA,EAAAhJ,IAAA,EAAA+F,QAAA,EAAAiG,YAAA,EAAA/C,GAAA,EAAAgD,WAAA;gBAAA,WAAAvI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAqI,UAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAnI,IAAA,GAAAmI,SAAA,CAAAlI,IAAA;oBAAA;sBAAAkI,SAAA,CAAAnI,IAAA;sBAEA,IAAAwH,IAAA;wBACAC,MAAA,CAAApI,aAAA;sBACA;sBAEAoI,MAAA,CAAA3I,UAAA;sBAEAkG,MAAA;wBACAW,CAAA;wBACAnH,OAAA,EAAAiJ,MAAA,CAAAtL,WAAA;wBACAC,QAAA,EAAAqL,MAAA,CAAArL,QAAA;wBACAmJ,EAAA,EAAAkC,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA,SAAA+B,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAF,EAAA;wBACA6C,MAAA,EAAAX,MAAA,CAAA7K,SAAA,CAAAQ,QAAA;wBACAwI,QAAA,EACA6B,MAAA,CAAA7K,SAAA,CAAAG,SAAA,QAAA0K,MAAA,CAAA7K,SAAA,CAAAG,SAAA;wBACA8I,SAAA,EAAA4B,MAAA,CAAA7K,SAAA,CAAAI,SAAA,GACAyK,MAAA,CAAA7K,SAAA,CAAAI,SAAA,MACA;wBACA8I,OAAA,EAAA2B,MAAA,CAAA7K,SAAA,CAAAI,SAAA,GACAyK,MAAA,CAAA7K,SAAA,CAAAI,SAAA,MACA;wBACAC,kBAAA,EACAwK,MAAA,CAAA7K,SAAA,CAAAK,kBAAA,QACAwK,MAAA,CAAA7K,SAAA,CAAAK,kBAAA,GACA;wBACA8I,mBAAA,EAAA0B,MAAA,CAAA7K,SAAA,CAAAM,cAAA,GACAuK,MAAA,CAAA7K,SAAA,CAAAM,cAAA,MACA;wBACA8I,iBAAA,EAAAyB,MAAA,CAAA7K,SAAA,CAAAM,cAAA,GACAuK,MAAA,CAAA7K,SAAA,CAAAM,cAAA,MACA;wBACA+I,QAAA,EAAAwB,MAAA,CAAA7K,SAAA,CAAAE,OAAA;wBACAK,YAAA,EAAAsK,MAAA,CAAA7K,SAAA,CAAAO,YAAA;wBACAE,OAAA,EAAAoK,MAAA,CAAA7K,SAAA,CAAAS,OAAA;wBACAiI,YAAA;sBACA;sBAEA,IAAAmC,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA;wBACAV,MAAA,CAAAU,QAAA,GAAA+B,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAC,QAAA;sBACA;;sBAEA;sBACA,IAAA+B,MAAA,CAAArI,iBAAA,IAAAqI,MAAA,CAAArI,iBAAA,CAAAiE,MAAA;wBACArH,IAAA,GAAAyL,MAAA,CAAArI,iBAAA,CAAAyC,GAAA,WAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAkF,KAAA;wBAAA;wBACAjF,QAAA,GAAA0F,MAAA,CAAArI,iBAAA,CAAAyC,GAAA,CACA,UAAAC,IAAA;0BAAA,OAAAA,IAAA,CAAAC,QAAA;wBAAA,CACA;wBAEAiD,MAAA,CAAAqD,UAAA,GAAAC,MAAA,CAAAtM,IAAA;wBACAgJ,MAAA,CAAAjD,QAAA,GAAAuG,MAAA,CAAAvG,QAAA;sBACA;;sBAEA;sBACA,IAAAiD,MAAA,CAAAiB,QAAA;wBACA,IAAAsC,iCAAA;0BAAAzL,OAAA,EAAAkI,MAAA,CAAAiB,QAAA;0BAAAuC,IAAA;wBAAA,GAAAC,IAAA,CACA;0BACAhB,MAAA,CAAAtH,iBAAA;wBACA,CACA;sBACA;sBAIA,IAAAsH,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAV,YAAA;0BACAxJ,OAAA,EAAAiJ,MAAA,CAAAtL,WAAA;0BACAC,QAAA,EAAAqL,MAAA,CAAArL,QAAA;0BACA2F,QAAA,EAAA0F,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;0BACAN,MAAA,EAAAX,MAAA,CAAA7K,SAAA,CAAAQ;wBACA;sBACA;sBAAA,KAEAqK,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBAAAP,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAAAkI,SAAA,CAAAlI,IAAA;sBAAA,OACAmG,cAAA,CAAAuC,iBAAA,KAAAC,cAAA,CAAAnJ,OAAA,MAAAuI,YAAA;oBAAA;sBAAAG,SAAA,CAAA9H,EAAA,GAAA8H,SAAA,CAAA7B,IAAA;sBAAA6B,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAAAkI,SAAA,CAAAlI,IAAA;sBAAA,OACAmG,cAAA,CAAAyC,aAAA,KAAAD,cAAA,CAAAnJ,OAAA,MAAAuF,MAAA;oBAAA;sBAAAmD,SAAA,CAAA9H,EAAA,GAAA8H,SAAA,CAAA7B,IAAA;oBAAA;sBAFArB,GAAA,GAAAkD,SAAA,CAAA9H,EAAA;sBAAA,MAIA4E,GAAA,CAAAsB,IAAA;wBAAA4B,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAGA,IAAAwH,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAT,WAAA,GAAAhD,GAAA,CAAAuB,IAAA;sBACA;wBACAyB,WAAA,GAAAhD,GAAA,CAAAjJ,IAAA,CAAA8M,IAAA;sBACA;;sBAEA;sBACA,IACA,CAAArB,MAAA,CAAA7K,SAAA,CAAAE,OAAA,IACA2K,MAAA,CAAA7K,SAAA,CAAAE,OAAA,CAAAiM,IAAA,WACA;wBACAd,WAAA,GAAAR,MAAA,CAAAuB,mBAAA,CAAAf,WAAA;sBACA;sBAEAR,MAAA,CAAAnL,WAAA,GAAA2L,WAAA;sBAEA,IAAAR,MAAA,CAAAjC,MAAA,CAAAC,KAAA,CAAAiD,MAAA;wBACAjB,MAAA,CAAApL,KAAA,GAAA4I,GAAA,CAAA5I,KAAA;sBACA;wBACAoL,MAAA,CAAApL,KAAA,GAAA4I,GAAA,CAAAjJ,IAAA,CAAAK,KAAA;sBACA;;sBAEA;sBACA,IAAAoL,MAAA,CAAArI,iBAAA,IAAAqI,MAAA,CAAArI,iBAAA,CAAAiE,MAAA;wBACAoE,MAAA,CAAAjE,4BAAA;sBACA;;sBAEA;sBAAA,MAEAiE,MAAA,CAAAnL,WAAA,CAAA+G,MAAA,SACAoE,MAAA,CAAArL,QAAA,IAAAqL,MAAA,CAAAtL,WAAA,SAAAsL,MAAA,CAAApL,KAAA,IACAoL,MAAA,CAAApL,KAAA;wBAAA8L,SAAA,CAAAlI,IAAA;wBAAA;sBAAA;sBAEAwH,MAAA,CAAAtL,WAAA,GAAAyK,IAAA,CAAAqC,GAAA,CACA,GACArC,IAAA,CAAAsC,IAAA,CAAAzB,MAAA,CAAApL,KAAA,GAAAoL,MAAA,CAAArL,QAAA,CACA;sBACA;sBAAA+L,SAAA,CAAAlI,IAAA;sBAAA,OACAwH,MAAA,CAAApG,gBAAA;oBAAA;sBAAA,OAAA8G,SAAA,CAAAN,MAAA;oBAAA;sBAAAM,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAIAwH,MAAA,CAAAjH,QAAA,CAAAD,KAAA,CAAA0E,GAAA,CAAAkE,GAAA;oBAAA;sBAAAhB,SAAA,CAAAlI,IAAA;sBAAA;oBAAA;sBAAAkI,SAAA,CAAAnI,IAAA;sBAAAmI,SAAA,CAAAiB,EAAA,GAAAjB,SAAA;sBAGA7H,OAAA,CAAAC,KAAA,cAAA4H,SAAA,CAAAiB,EAAA;sBACA3B,MAAA,CAAAjH,QAAA,CAAAD,KAAA;oBAAA;sBAAA4H,SAAA,CAAAnI,IAAA;sBAEAyH,MAAA,CAAA3I,UAAA;sBACA2I,MAAA,CAAApI,aAAA;sBACAoI,MAAA,CAAA5I,YAAA;sBACA4I,MAAA,CAAAlK,cAAA;sBAAA,OAAA4K,SAAA,CAAAZ,MAAA;oBAAA;oBAAA;sBAAA,OAAAY,SAAA,CAAA1H,IAAA;kBAAA;gBAAA,GAAAsH,QAAA;cAAA,CAEA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAnH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IAEA;IACAlF,sBAAA,WAAAA,uBAAA;MAAA,IAAA6G,MAAA;MACA,KAAA9H,SAAA;QACA;QACA,IAAA+H,gBAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,gBAAA;UACAA,gBAAA,CAAAG,SAAA;QACA;;QAEA;QACA,IACAJ,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,IACAL,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,IACAmF,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,CAAAyF,MAAA,EACA;UACAN,MAAA,CAAAnF,KAAA,CAAAwF,WAAA,CAAAxF,KAAA,CAAAyF,MAAA,CAAAF,SAAA;QACA;;QAEA;QACA,IAAAG,SAAA,GAAAL,QAAA,CAAAC,aAAA;QACA,IAAAI,SAAA;UACAA,SAAA,CAAAH,SAAA;QACA;MACA;IACA;IACAI,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtK,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoK,SAAA;QAAA,IAAAC,QAAA;QAAA,WAAAtK,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlK,IAAA,GAAAkK,SAAA,CAAAjK,IAAA;YAAA;cAAAiK,SAAA,CAAAlK,IAAA;cAAAkK,SAAA,CAAAjK,IAAA;cAAA,OAEAmG,cAAA,CAAA+D,WAAA;YAAA;cAAAH,QAAA,GAAAE,SAAA,CAAA5D,IAAA;cACA,IAAA0D,QAAA,IAAAA,QAAA,CAAAzD,IAAA,WAAAyD,QAAA,CAAAhO,IAAA;gBACA8N,MAAA,CAAApM,QAAA,GAAAsM,QAAA,CAAAhO,IAAA;gBACA8N,MAAA,CAAAnM,WAAA,GAAAqM,QAAA,CAAAhO,IAAA;cACA;gBACAsE,OAAA,CAAA8J,IAAA;cACA;cAAAF,SAAA,CAAAjK,IAAA;cAAA;YAAA;cAAAiK,SAAA,CAAAlK,IAAA;cAAAkK,SAAA,CAAA7J,EAAA,GAAA6J,SAAA;cAEA5J,OAAA,CAAAC,KAAA,cAAA2J,SAAA,CAAA7J,EAAA;cACAyJ,MAAA,CAAAtJ,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA2J,SAAA,CAAAzJ,IAAA;UAAA;QAAA,GAAAsJ,QAAA;MAAA;IAEA;IAEA5J,iBAAA,WAAAA,kBAAA;MAAA,IAAAkK,OAAA;MAAA,WAAA7K,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA2K,SAAA;QAAA,IAAArF,GAAA;QAAA,WAAAvF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxK,IAAA,GAAAwK,SAAA,CAAAvK,IAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAvK,IAAA;cAAA,OAEA,IAAAwK,kCAAA;gBACAjM,OAAA;gBACApC,QAAA;gBACAoM,IAAA;cACA;YAAA;cAJAvD,GAAA,GAAAuF,SAAA,CAAAlE,IAAA;cAKA,IAAArB,GAAA,IAAAA,GAAA,CAAAsB,IAAA;gBACA8D,OAAA,CAAAlM,WAAA,GAAA8G,GAAA,CAAAuB,IAAA;cACA;cAAAgE,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAxK,IAAA;cAAAwK,SAAA,CAAAnK,EAAA,GAAAmK,SAAA;cAEAlK,OAAA,CAAAC,KAAA,cAAAiK,SAAA,CAAAnK,EAAA;YAAA;YAAA;cAAA,OAAAmK,SAAA,CAAA/J,IAAA;UAAA;QAAA,GAAA6J,QAAA;MAAA;IAEA;IAEA;IACAI,UAAA,WAAAA,WAAAC,KAAA,EAAA3O,IAAA;MACA,KAAA2O,KAAA;MACA,OAAA3O,IAAA,CAAAgL,KAAA,CAAA4D,OAAA,CAAAD,KAAA;IACA;IAEA;IACAzK,OAAA,WAAAA,QAAAsH,IAAA;MACA,IAAAA,IAAA;QACA,KAAAhF,sBAAA;QACA;QACA,KAAAO,gBAAA;MACA;QACA;QACA,KAAAP,sBAAA;QACA,KAAAnB,gBAAA;MACA;IACA;IAEA;IACAwJ,SAAA,WAAAA,UAAA;MACA;QACA,KAAAjO,SAAA;UACAC,QAAA;UACAC,OAAA;UACAC,SAAA;UACAC,SAAA;UACAC,kBAAA;UACAC,cAAA;UACAC,YAAA;UACAC,QAAA;UACAC,OAAA;QACA;QAEA,KAAAlB,WAAA;QACA,KAAAqG,sBAAA;QACA,KAAAO,gBAAA;MACA,SAAAxC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;MACA;IACA;IAEAuK,aAAA,WAAAA,cAAAhJ,IAAA,EAAA0G,IAAA;MAAA,IAAAuC,OAAA;MAAA,WAAAvL,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqL,UAAA;QAAA,WAAAtL,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoL,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlL,IAAA,GAAAkL,UAAA,CAAAjL,IAAA;YAAA;cAAAiL,UAAA,CAAAlL,IAAA;cAEA,IAAA+K,OAAA,CAAA3M,cAAA;gBACA0J,YAAA,CAAAiD,OAAA,CAAA3M,cAAA;cACA;cAAA,MAEA0D,IAAA,IAAAA,IAAA,CAAAyD,EAAA;gBAAA2F,UAAA,CAAAjL,IAAA;gBAAA;cAAA;cAAAiL,UAAA,CAAAjL,IAAA;cAAA,OACA,IAAAkL,iCAAA,GAAArJ,IAAA,CAAAyD,EAAA;YAAA;cAAA,MAEAiD,IAAA;gBAAA0C,UAAA,CAAAjL,IAAA;gBAAA;cAAA;cACA,IAAA8K,OAAA,CAAA7G,KAAA;gBACA6G,OAAA,CAAA7G,KAAA,eAAAkH,KAAA;cACA;cAAAF,UAAA,CAAAjL,IAAA;cAAA,OACA8K,OAAA,CAAA5K,iBAAA;YAAA;cAAA+K,UAAA,CAAAjL,IAAA;cAAA;YAAA;cAAAiL,UAAA,CAAAjL,IAAA;cAAA,OAEA8K,OAAA,CAAA5K,iBAAA;YAAA;cAAA+K,UAAA,CAAAjL,IAAA;cAAA,OACA8K,OAAA,CAAAM,kBAAA;YAAA;cAAAH,UAAA,CAAAjL,IAAA;cAAA;YAAA;cAAAiL,UAAA,CAAAlL,IAAA;cAAAkL,UAAA,CAAA7K,EAAA,GAAA6K,UAAA;cAIA5K,OAAA,CAAAC,KAAA,eAAA2K,UAAA,CAAA7K,EAAA;cACA0K,OAAA,CAAAvK,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA2K,UAAA,CAAAzK,IAAA;UAAA;QAAA,GAAAuK,SAAA;MAAA;IAEA;IAEAM,eAAA,WAAAA,gBAAA;MACA;QACA,KAAApN,WAAA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;MACA;IACA;IAEAgL,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;QACA,SAAApN,cAAA;UACA0J,YAAA,MAAA1J,cAAA;QACA;QAEA,KAAAA,cAAA,GAAAqF,UAAA;UACA+H,OAAA,CAAAtN,WAAA;QACA;MACA,SAAAqC,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAArC,WAAA;MACA;IACA;IAEA;IACAuN,cAAA,WAAAA,eAAA3J,IAAA;MACA,KAAAlF,SAAA,CAAAE,OAAA,GAAAgF,IAAA,CAAAhF,OAAA;MACA,KAAAuB,cAAA;MACA,KAAAmE,sBAAA;MACA,KAAArG,WAAA;MACA;MACA,KAAAkF,gBAAA;IACA;IAEAqK,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiM,UAAA;QAAA,WAAAlM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAgM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9L,IAAA,GAAA8L,UAAA,CAAA7L,IAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAEA,IAAA2L,OAAA,CAAAvN,cAAA;gBACA0J,YAAA,CAAA6D,OAAA,CAAAvN,cAAA;cACA;cAEA,IAAAuN,OAAA,CAAAzH,KAAA;gBACAyH,OAAA,CAAAzH,KAAA,eAAAkH,KAAA;cACA;cAAAU,UAAA,CAAA7L,IAAA;cAAA,OAEA,IAAA8L,mCAAA;YAAA;cAAAD,UAAA,CAAA7L,IAAA;cAAA,OACA0L,OAAA,CAAAxL,iBAAA;YAAA;cAAA2L,UAAA,CAAA7L,IAAA;cAAA;YAAA;cAAA6L,UAAA,CAAA9L,IAAA;cAAA8L,UAAA,CAAAzL,EAAA,GAAAyL,UAAA;cAEAxL,OAAA,CAAAC,KAAA,eAAAuL,UAAA,CAAAzL,EAAA;cACAsL,OAAA,CAAAnL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAuL,UAAA,CAAArL,IAAA;UAAA;QAAA,GAAAmL,SAAA;MAAA;IAEA;IAEAI,WAAA,WAAAA,YAAA;MACA;QACA,KAAA1N,cAAA;QACA,KAAA+M,kBAAA;QACA,KAAAhN,cAAA;MACA,SAAAkC,KAAA;QACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACA,KAAAjC,cAAA;MACA;IACA;IAEA+M,kBAAA,WAAAA,mBAAA;MAAA,IAAAY,OAAA;MAAA,WAAAzM,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuM,UAAA;QAAA,IAAAC,QAAA;QAAA,WAAAzM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuM,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArM,IAAA,GAAAqM,UAAA,CAAApM,IAAA;YAAA;cAAAoM,UAAA,CAAArM,IAAA;cAEAiM,OAAA,CAAA3N,cAAA;cAAA+N,UAAA,CAAApM,IAAA;cAAA,OACA,IAAAwK,kCAAA,MAAA7B,cAAA,CAAAnJ,OAAA,MAAAmJ,cAAA,CAAAnJ,OAAA,MACAwM,OAAA,CAAA1N,YAAA;gBACAiK,IAAA;cAAA,EACA;YAAA;cAHA2D,QAAA,GAAAE,UAAA,CAAA/F,IAAA;cAKA,IAAA6F,QAAA;gBACAF,OAAA,CAAAvN,YAAA,GAAAyN,QAAA,CAAA3F,IAAA;gBACAyF,OAAA,CAAAxN,MAAA,GAAA0N,QAAA,CAAA9P,KAAA;cACA;cAEA4P,OAAA,CAAA3N,cAAA;cAAA+N,UAAA,CAAApM,IAAA;cAAA;YAAA;cAAAoM,UAAA,CAAArM,IAAA;cAAAqM,UAAA,CAAAhM,EAAA,GAAAgM,UAAA;cAEA/L,OAAA,CAAAC,KAAA,iBAAA8L,UAAA,CAAAhM,EAAA;cACA4L,OAAA,CAAA3N,cAAA;cACA2N,OAAA,CAAAzL,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAA8L,UAAA,CAAA5L,IAAA;UAAA;QAAA,GAAAyL,SAAA;MAAA;IAEA;IAEAI,OAAA,WAAAA,QAAAhF,GAAA;MACAiF,MAAA,CAAAC,IAAA,CAAAlF,GAAA;IACA;IAEA;IACAmF,kBAAA,WAAAA,mBAAA3P,OAAA;MACA,SAAAmC,WAAA;QACA;MACA;;MAEA;;MAEA;MACA,KAAAnB,SAAA,CAAAC,WAAA,GAAAjB,OAAA;;MAEA;MACA,KAAAL,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAqI,oBAAA,WAAAA,qBAAAC,aAAA;MACA;;MAEA;MACA,KAAAzN,gBAAA,GAAAyN,aAAA;;MAEA;MACA,KAAAlQ,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAuI,mBAAA,WAAAA,oBAAAC,YAAA;MACA;;MAEA;MACA,KAAA1N,eAAA,GAAA0N,YAAA;;MAEA;MACA,KAAApQ,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;;MAEA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACAyI,uBAAA,WAAAA,wBAAAC,IAAA;MACA,KAAAtQ,eAAA,GAAAsQ,IAAA;MACA,KAAAnQ,SAAA,CAAAU,QAAA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACA2I,wBAAA,WAAAA,yBAAAnI,IAAA;MACA,KAAAnI,YAAA,GAAAmI,IAAA;MACA,KAAApI,eAAA;MACA,KAAAG,SAAA,CAAAU,QAAA;MACA,KAAA+G,iCAAA;IACA;IAEA;IACA4I,WAAA,WAAAA,YAAAnL,IAAA;MACAyK,MAAA,CAAAC,IAAA,uBAAAnK,MAAA,CACAP,IAAA,CAAAyD,EAAA,aAAAlD,MAAA,CAAAP,IAAA,CAAAoL,KAAA,kBAAA7K,MAAA,CAAAP,IAAA,CAAAqL,UAAA,GACA,QACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MACA,KAAA/B,kBAAA;IACA;IAEA;IACArC,mBAAA,WAAAA,oBAAAqE,QAAA;MACA,KAAAA,QAAA,IAAAA,QAAA,CAAAhK,MAAA;QACA,OAAAgK,QAAA;MACA;MAEA,IAAAC,QAAA,OAAAC,GAAA;MACA,IAAAC,MAAA;;MAEA;MACAH,QAAA,CAAAzJ,OAAA,WAAA6J,OAAA;QACA;QACA,IAAAC,UAAA,GAAAD,OAAA,CAAAE,KAAA,GACAF,OAAA,CAAAE,KAAA,CAAAC,OAAA,iBAAAA,OAAA,eACA;QAEA,IAAAN,QAAA,CAAA1K,GAAA,CAAA8K,UAAA;UACAJ,QAAA,CAAAO,GAAA,CAAAH,UAAA,EAAAxG,KAAA;QACA;UACAoG,QAAA,CAAAQ,GAAA,CAAAJ,UAAA;YACAD,OAAA,MAAA7E,cAAA,CAAAnJ,OAAA,MAAAgO,OAAA;YACAvG,KAAA;YACA6G,aAAA,EAAAN,OAAA,CAAAE,KAAA;UACA;QACA;MACA;;MAEA;MACAL,QAAA,CAAA1J,OAAA,WAAAoK,KAAA;QAAA,IAAAP,OAAA,GAAAO,KAAA,CAAAP,OAAA;UAAAvG,KAAA,GAAA8G,KAAA,CAAA9G,KAAA;UAAA6G,aAAA,GAAAC,KAAA,CAAAD,aAAA;QACA,IAAA7G,KAAA;UACA;UACA;UACAuG,OAAA,CAAAE,KAAA,MAAAtL,MAAA,CAAA0L,aAAA,kBAAA1L,MAAA,CAAA6E,KAAA;QACA;QACAsG,MAAA,CAAAvJ,IAAA,CAAAwJ,OAAA;MACA;MAEA,OAAAD,MAAA;IACA;EACA;AACA", "ignoreList": []}]}