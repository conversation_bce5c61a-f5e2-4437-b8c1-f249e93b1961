{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\tabOne.vue", "mtime": 1753863065927}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgdXNhTWFwIGZyb20gIi4vY29tcG9uZW50cy91c2FNYXAiOw0KaW1wb3J0IHRpbWVMaW5lIGZyb20gIi4vY29tcG9uZW50cy90aW1lTGluZSI7DQppbXBvcnQgZ3JhcGhFY2hhcnRzIGZyb20gIi4vY29tcG9uZW50cy9ncmFwaEVjaGFydHMiOw0KaW1wb3J0IHRlY2hub2xvZ3lBcnRpY2xlcyBmcm9tICIuL2NvbXBvbmVudHMvdGVjaG5vbG9neUFydGljbGVzIjsNCmltcG9ydCB0cnVtcFZpZXdUcmVlIGZyb20gIi4vY29tcG9uZW50cy90cnVtcFZpZXdUcmVlIjsNCmltcG9ydCB2aWV3VHJlZSBmcm9tICIuL2NvbXBvbmVudHMvdmlld1RyZWUiOw0KaW1wb3J0IHBvbGljeVJpc2sgZnJvbSAiLi9zZWNvbmRMZXZlbC9wb2xpY3lSaXNrIjsNCmltcG9ydCBhcnRpY2xlRGV0YWlscyBmcm9tICIuL3NlY29uZExldmVsL2FydGljbGVEZXRhaWxzIjsNCmltcG9ydCBzdXBwcmVzc2lvbk9mUmlza3MgZnJvbSAiLi9zZWNvbmRMZXZlbC9zdXBwcmVzc2lvbk9mUmlza3MiOw0KaW1wb3J0IGVudGVycHJpc2VJbmZvcm1hdGlvbiBmcm9tICIuL3NlY29uZExldmVsL2VudGVycHJpc2VJbmZvcm1hdGlvbiI7DQppbXBvcnQgY29tcGFyaXNvbkNoYXJ0IGZyb20gIi4vc2Vjb25kTGV2ZWwvY29tcGFyaXNvbkNoYXJ0IjsNCmltcG9ydCBob3RUZWNobm9sb2d5IGZyb20gIi4vc2Vjb25kTGV2ZWwvaG90VGVjaG5vbG9neSI7DQppbXBvcnQgYmFhclRyZWVFY2hhcnRzIGZyb20gIi4vY29tcG9uZW50cy9iYWFyVHJlZUVjaGFydHMiOw0KaW1wb3J0IHRlY2hub2xvZ3lEZXRhaWxzIGZyb20gIi4vc2Vjb25kTGV2ZWwvdGVjaG5vbG9neURldGFpbHMiOw0KaW1wb3J0IHRlY2hCdWJibGVEaWFsb2cgZnJvbSAiLi9zZWNvbmRMZXZlbC90ZWNoQnViYmxlRGlhbG9nIjsNCmltcG9ydCBBcnRpY2xlTm90aWZpY2F0aW9uIGZyb20gIkAvY29tcG9uZW50cy9BcnRpY2xlTm90aWZpY2F0aW9uIjsNCmltcG9ydCB7DQogIHRlY2huaWNhbEFydGljbGVEZXRhaWwsDQogIHN1cHByZXNzRGF0YSwNCiAgc3VwcHJlc3NMZXZlbENvdW50LA0KICBzdXBwcmVzc0VudGVycHJpc2VMaXN0LA0KICBzdXBwcmVzc1BhdGVudExpc3QsDQogIHN1cHByZXNzU29mdHdhcmVMaXN0LA0KICBwcm9wb3NhbHNMaXN0LA0KICBwcm9wb3NhbHNUb0NoaW5hRGF0YSwNCiAgcHJvcG9zYWxzQ291bnQsDQogIGtqZHRBcnRpY2xlTGlzdCwNCiAgbG9naW5UQ0VTLA0KICBsb2dpblNJTkEsDQp9IGZyb20gIkAvYXBpL2JpZ1NjcmVlbi9zYW5oYW8uanMiOw0KaW1wb3J0IHsNCiAgbGFyZ2VIb3RRdWVyeUJ5SWQsDQogIGxhcmdlR2F0aGVyUXVlcnlHYXRoZXJEYXRhLA0KICBnZXRMYXJnZUZUVCwNCiAgbGFyZ2VIb3RMaXN0MiwNCn0gZnJvbSAiQC9hcGkvYmlnU2NyZWVuL2luZGV4MSI7DQppbXBvcnQgeyBtYXJrT2JqIH0gZnJvbSAiLi9kYXRhL3poaWt1LmpzIjsNCmltcG9ydCB7IHRyZWVEYXRhMiwgbWFya2Rvd25EYXRhIH0gZnJvbSAiLi9kYXRhL3Jlbnd1LmpzIjsNCmltcG9ydCBNYXJrbWFwRGlhbG9nIGZyb20gIi4uL2JpZ1NjcmVlblRocmVlL2NvbXBvbmVudHMvTWFya21hcERpYWxvZy52dWUiOw0KaW1wb3J0IHsNCiAgY29udGFpbnNIdG1sVGFncywNCiAgZXh0cmFjdEh0bWxUYWdzLA0KICBoYXNWYWxpZEh0bWxTdHJ1Y3R1cmUsDQp9IGZyb20gIkAvdXRpbHMvaHRtbFV0aWxzIjsNCmltcG9ydCB7IFRyYW5zZm9ybWVyIH0gZnJvbSAibWFya21hcC1saWIiOw0KaW1wb3J0IHsgTWFya21hcCB9IGZyb20gIm1hcmttYXAtdmlldyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlRhYk9uZSIsDQogIGNvbXBvbmVudHM6IHsNCiAgICB1c2FNYXAsDQogICAgdGltZUxpbmUsDQogICAgZ3JhcGhFY2hhcnRzLA0KICAgIHRlY2hub2xvZ3lBcnRpY2xlcywNCiAgICB0cnVtcFZpZXdUcmVlLA0KICAgIHZpZXdUcmVlLA0KICAgIHBvbGljeVJpc2ssDQogICAgYXJ0aWNsZURldGFpbHMsDQogICAgc3VwcHJlc3Npb25PZlJpc2tzLA0KICAgIGVudGVycHJpc2VJbmZvcm1hdGlvbiwNCiAgICBjb21wYXJpc29uQ2hhcnQsDQogICAgaG90VGVjaG5vbG9neSwNCiAgICBiYWFyVHJlZUVjaGFydHMsDQogICAgdGVjaG5vbG9neURldGFpbHMsDQogICAgdGVjaEJ1YmJsZURpYWxvZywNCiAgICBNYXJrbWFwRGlhbG9nLA0KICAgIEFydGljbGVOb3RpZmljYXRpb24sDQogIH0sDQogIHByb3BzOiB7DQogICAgbm90aWZpY2F0aW9uQXJ0aWNsZXM6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10sDQogICAgfSwNCiAgICBzaG93Tm90aWZpY2F0aW9uOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UsDQogICAgfSwNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcG9saWN5Umlza1Nob3dNb2RhbDogZmFsc2UsDQogICAgICBjb21wYXJpc29uQ2hhcnRTaG93TW9kYWw6IGZhbHNlLA0KICAgICAgaG90VGVjaG5vbG9neVNob3dNb2RhbDogZmFsc2UsDQogICAgICBob3RUZWNobm9sb2d5dFRpdGxlOiAiIiwNCiAgICAgIGhvdFRlY2hub2xvZ3l0SUQ6IG51bGwsDQogICAgICBiYWFyVHJlZUVjaGFydHNTaG93TW9kYWw6IGZhbHNlLA0KICAgICAgYmFhclRyZWVFY2hhcnRzVHlwZTogbnVsbCwNCiAgICAgIHRlY2hub2xvZ3lEZXRhaWxzU2hvd01vZGFsOiBmYWxzZSwNCiAgICAgIHRlY2hub2xvZ3lEZXRhaWxzVGl0bGU6ICIiLA0KICAgICAgdGVjaG5vbG9neURldGFpbHNJdGVtOiBudWxsLA0KICAgICAgc3VwcHJlc3Npb25PZlJpc2tzU2hvd01vZGFsOiBmYWxzZSwNCiAgICAgIGVudGVycHJpc2VJbmZvcm1hdGlvblNob3dNb2RhbDogZmFsc2UsDQogICAgICBlbnRlcnByaXNlSW5mb3JtYXRpb25UaXRsZTogIiIsDQogICAgICBhcnRpY2xlRGV0YWlsc1Nob3dNb2RhbDogZmFsc2UsDQogICAgICBhcnRpY2xlRGV0YWlsc1RpdGxlOiAiIiwNCiAgICAgIGFydGljbGVEZXRhaWxzQ29udGVudDogIiIsDQogICAgICBhcnRpY2xlRGV0YWlsc0NvbnRlbnRFbjogIiIsDQogICAgICBzdXBwcmVzc0xpc3REYXRhOiBbXSwNCiAgICAgIHJpc2tCYXJDaGFydERhdGE6IFtdLA0KICAgICAgcmlza0VudGVycHJpc2VMaXN0OiBbXSwNCiAgICAgIHJpc2tFbnRlcnByaXNlTGlzdFRvdGFsOiAwLA0KICAgICAgZW50ZXJwcmlzZUluZm9ybWF0aW9uQ29udGVudDoge30sDQogICAgICBwYXRlbnRMaXN0OiBbXSwNCiAgICAgIHNvZnR3YXJlTGlzdDogW10sDQogICAgICBwYXRlbnRUb3RhbDogMCwNCiAgICAgIHNvZnR3YXJlVG90YWw6IDAsDQogICAgICBwb2xpY3lSaXNrTGlzdDE6IFtdLA0KICAgICAgcG9saWN5Umlza0xpc3QyOiBbXSwNCiAgICAgIHBvbGljeVJpc2tMaXN0MVRvdGFsOiAwLA0KICAgICAgLy8g576O5Zu95Zyw5Zu+5pWw5o2uDQogICAgICB1c2FNYXBEYXRhOiBudWxsLA0KICAgICAgYXJ0aWNsZURldGFpbHNJdGVtOiB7fSwNCiAgICAgIC8vIOeDreeCueaOqOiNkOebuOWFs+aVsOaNrg0KICAgICAgcmVtZW5nd2VuemhhbmdMaXN0OiBbXSwNCiAgICAgIHNjcm9sbFRpbWVyOiBudWxsLA0KICAgICAgc2Nyb2xsVGltZXIxOiBudWxsLA0KICAgICAgc2Nyb2xsVGltZXIyOiBudWxsLA0KICAgICAgaXNIb3ZlcmVkOiBmYWxzZSwNCiAgICAgIGlzSG92ZXJlZDE6IGZhbHNlLA0KICAgICAgaXNIb3ZlcmVkMjogZmFsc2UsDQogICAgICBzY3JvbGxTdGVwOiAxLA0KICAgICAgZHJhd2VySW5mbzoge30sDQogICAgICBhcnRpY2xlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmb250U2l6ZTogMTYsDQogICAgICBvcmlGb250U2l6ZTogMjAsDQogICAgICAvLyDkurrnianop4LngrnmlbDmja4NCiAgICAgIGNoYXJhY3RlclZpZXdEYXRhOiBbXSwNCiAgICAgIC8vIOaZuuW6k+ingueCueaVsOaNrg0KICAgICAgdGhpbmtUYW5rVmlld0RhdGE6IFtdLA0KICAgICAgcmVtZW5nd2VuemhhbmdMaXN0MTogW10sDQogICAgICBhY3RpdmVCdXR0b246IG51bGwsDQogICAgICBxaWFueWFua2VqaUxpc3Q6IFtdLA0KICAgICAgZ2F0aGVyVG90YWw6IDAsDQogICAgICBnYXRoZXJEYXlOdW1iZXI6IDAsDQogICAgICBtYXJrbWFwVmlzaWJsZTogZmFsc2UsDQogICAgICBtYXJrbWFwQ29udGVudDogIiIsDQogICAgICBtYXJrbWFwVGl0bGU6ICLmmbrlupPop4LngrkiLA0KICAgICAgYWlMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZyb250TG9naW5QYXJhbXM6IHsNCiAgICAgICAgdXNlcm5hbWU6ICJndWFubGl5dWFuIiwNCiAgICAgICAgcGFzc3dvcmQ6ICIxMjM0NTYiLA0KICAgICAgfSwNCiAgICAgIGZyb250VG9rZW46ICIiLA0KICAgICAgLy8g5oqA5pyv6aKG5Z+f55u45YWzDQogICAgICBhY3RpdmVUZWNoQnV0dG9uOiAiMTEiLCAvLyDpu5jorqTpgInkuK3mlrDog73mupANCiAgICAgIGN1cnJlbnRUZWNoU2NyZWVuU246ICIxMSIsIC8vIOW9k+WJjeaKgOacr+mihuWfn+eahHNjcmVlblNuDQogICAgICAvLyDmioDmnK/poobln5/ms6Hms6Hlm77lvLnnqpfnm7jlhbMNCiAgICAgIHRlY2hCdWJibGVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHRlY2hCdWJibGVEaWFsb2dUaXRsZTogIiIsDQogICAgICB0ZWNoQnViYmxlRGlhbG9nU2NyZWVuU246ICIiLA0KICAgICAgLy8g5pm65bqT6KeC54K5dGFi5YiH5o2iDQogICAgICBhY3RpdmVUYWI6ICJ0cnVtcCIsIC8vIOm7mOiupOaYvuekuueJueacl+aZrnRhYg0KICAgICAgZG9tYWluTWFya2Rvd246ICIiLA0KICAgICAgc2luYVVybDogIiIsDQogICAgICB6aGlrdUFjdGl2ZTogMCwNCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOWKqOaAgeiuoeeul+eDremXqOaWh+eroOWIl+ihqOahhueahOagt+W8jw0KICAgIHJlbWVuZ3dlbnpoYW5nQm94U3R5bGUoKSB7DQogICAgICBjb25zdCBub3RpZmljYXRpb25IZWlnaHQgPSAxMTA7IC8vIOmAmuefpee7hOS7tueahOmrmOW6pg0KDQogICAgICBpZiAodGhpcy5zaG93Tm90aWZpY2F0aW9uKSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgaGVpZ2h0OiBgY2FsYygxMDAlIC0gJHtub3RpZmljYXRpb25IZWlnaHR9cHgpYCwNCiAgICAgICAgfTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgaGVpZ2h0OiBgMTAwJWAsDQogICAgICAgIH07DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvLyDosIPnlKjnmbvlvZVUQ0VT5o6l5Y+jDQogICAgbG9naW5UQ0VTKCkNCiAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coIlRDRVPnmbvlvZXmiJDlip8iKTsNCiAgICAgIH0pDQogICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIlRDRVPnmbvlvZXlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfSk7DQoNCiAgICAvLyDosIPnlKjnmbvlvZXmlrDmtarmjqXlj6MNCiAgICBsb2dpblNJTkEoKQ0KICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygi5paw5rWq55m75b2V5oiQ5YqfIik7DQogICAgICAgIHRoaXMuc2luYVVybCA9IHJlczsNCiAgICAgIH0pDQogICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuaWsOa1queZu+W9leWksei0pToiLCBlcnJvcik7DQogICAgICB9KTsNCg0KICAgIHRoaXMuZ2V0U3VwcHJlc3NEYXRhKCk7DQogICAgdGhpcy5pbml0SG90TGlzdCgpOw0KICAgIHRoaXMuaW5pdEhvdExpc3QxKCk7DQogICAgdGhpcy51cGRhdGVTY3JvbGxiYXIoKTsNCiAgICB0aGlzLnVwZGF0ZVNjcm9sbGJhcjEoKTsNCiAgICB0aGlzLmZldGNoVXNhTWFwRGF0YSgpOw0KICAgIGxhcmdlR2F0aGVyUXVlcnlHYXRoZXJEYXRhKHt9KS50aGVuKChyZXMpID0+IHsNCiAgICAgIHRoaXMuZ2F0aGVyVG90YWwgPSByZXMuZGF0YS5nYXRoZXJUb3RhbDsNCiAgICAgIHRoaXMuZ2F0aGVyRGF5TnVtYmVyID0gcmVzLmRhdGEuZ2F0aGVyRGF5TnVtYmVyOw0KICAgIH0pOw0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHRoaXMuY2xlYXJTY3JvbGxUaW1lcigpOw0KICAgIHRoaXMuY2xlYXJTY3JvbGxUaW1lcjEoKTsNCiAgICB0aGlzLmNsZWFyU2Nyb2xsVGltZXIyKCk7DQogICAgdGhpcy5oYW5kbGVNYXJrbWFwQ2xvc2UoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOiOt+WPlue+juWbveWcsOWbvuaVsOaNrg0KICAgIGFzeW5jIGZldGNoVXNhTWFwRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcHJvcG9zYWxzQ291bnQoew0KICAgICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgICAgY29sdW1uU246ICIxIiwNCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMudXNhTWFwRGF0YSA9IHJlc3BvbnNlOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W576O5Zu95Zyw5Zu+5pWw5o2u5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgb3BlbkFydGljbGVEZXRhaWxzKHR5cGUsIGl0ZW0pIHsNCiAgICAgIHRoaXMuYXJ0aWNsZURldGFpbHNJdGVtID0gaXRlbTsNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICJ0ZWNobm9sb2d5LWFydGljbGUiOg0KICAgICAgICAgIHRlY2huaWNhbEFydGljbGVEZXRhaWwoeyBpZDogaXRlbS5pZCB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIHRoaXMuYXJ0aWNsZURldGFpbHNUaXRsZSA9IGl0ZW0udGl0bGU7DQogICAgICAgICAgICB0aGlzLmFydGljbGVEZXRhaWxzQ29udGVudCA9IHJlcy5kYXRhLmNvbnRlbnQ7DQogICAgICAgICAgICB0aGlzLmFydGljbGVEZXRhaWxzQ29udGVudEVuID0gcmVzLmRhdGEuZW5Db250ZW50Ow0KICAgICAgICAgICAgdGhpcy5hcnRpY2xlRGV0YWlsc1Nob3dNb2RhbCA9IHRydWU7DQogICAgICAgICAgfSk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImVudGVycHJpc2VJbmZvcm1hdGlvbi1uZXdzIjoNCiAgICAgICAgICB0aGlzLm9wZW5OZXdWaWV3KGl0ZW0pOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICJwb2xpY3lSaXNrLW5ld3MiOg0KICAgICAgICAgIHRoaXMub3Blbk5ld1ZpZXcoaXRlbSk7DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICBvcGVuRW50ZXJwcmlzZUluZm9ybWF0aW9uKGl0ZW0pIHsNCiAgICAgIHN1cHByZXNzUGF0ZW50TGlzdCh7DQogICAgICAgIHN1cHByZXNzU246IGl0ZW0uc3VwcHJlc3NTbiwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucGF0ZW50TGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnBhdGVudFRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgfSk7DQogICAgICBzdXBwcmVzc1NvZnR3YXJlTGlzdCh7DQogICAgICAgIHN1cHByZXNzU246IGl0ZW0uc3VwcHJlc3NTbiwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuc29mdHdhcmVMaXN0ID0gcmVzLnJvd3M7DQogICAgICAgIHRoaXMuc29mdHdhcmVUb3RhbCA9IHJlcy50b3RhbDsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5lbnRlcnByaXNlSW5mb3JtYXRpb25Db250ZW50ID0geyAuLi5pdGVtIH07DQogICAgICB0aGlzLmVudGVycHJpc2VJbmZvcm1hdGlvblRpdGxlID0gaXRlbS5lbnRlcnByaXNlTmFtZTsNCiAgICAgIHRoaXMuZW50ZXJwcmlzZUluZm9ybWF0aW9uU2hvd01vZGFsID0gdHJ1ZTsNCiAgICB9LA0KDQogICAgcGF0ZW50UGFnaW5hdGlvbihzdXBwcmVzc1NuLCBxdWVyeVBhcmFtcykgew0KICAgICAgc3VwcHJlc3NQYXRlbnRMaXN0KHsNCiAgICAgICAgc3VwcHJlc3NTbjogc3VwcHJlc3NTbiwNCiAgICAgICAgLi4ucXVlcnlQYXJhbXMsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5wYXRlbnRMaXN0ID0gcmVzLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgc29mdHdhcmVQYWdpbmF0aW9uKHN1cHByZXNzU24sIHF1ZXJ5UGFyYW1zKSB7DQogICAgICBzdXBwcmVzc1NvZnR3YXJlTGlzdCh7DQogICAgICAgIHN1cHByZXNzU246IHN1cHByZXNzU24sDQogICAgICAgIC4uLnF1ZXJ5UGFyYW1zLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuc29mdHdhcmVMaXN0ID0gcmVzLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0U3VwcHJlc3NEYXRhKCkgew0KICAgICAgc3VwcHJlc3NEYXRhKHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGRhdGEgPSBbXTsNCiAgICAgICAgT2JqZWN0LmtleXMocmVzLmRhdGEpLmZvckVhY2goKGtleSkgPT4gew0KICAgICAgICAgIGRhdGEucHVzaCh7DQogICAgICAgICAgICBkYXRlOiBrZXksDQogICAgICAgICAgICBkZXNjcmlwdGlvbjoNCiAgICAgICAgICAgICAgcmVzLmRhdGFba2V5XS5sZW5ndGggPD0gMw0KICAgICAgICAgICAgICAgID8gcmVzLmRhdGFba2V5XQ0KICAgICAgICAgICAgICAgIDogcmVzLmRhdGFba2V5XS5zbGljZSgNCiAgICAgICAgICAgICAgICAgICAgcmVzLmRhdGFba2V5XS5sZW5ndGggLSAzLA0KICAgICAgICAgICAgICAgICAgICByZXMuZGF0YVtrZXldLmxlbmd0aA0KICAgICAgICAgICAgICAgICAgKSwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuc3VwcHJlc3NMaXN0RGF0YSA9IGRhdGEucmV2ZXJzZSgpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldFJpc2tEZXRhaWwoKSB7DQogICAgICBzdXBwcmVzc0VudGVycHJpc2VMaXN0KHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnJpc2tFbnRlcnByaXNlTGlzdCA9IHJlcy5yb3dzLm1hcCgoaXRlbSwgaW5kZXgpID0+ICh7DQogICAgICAgICAgLi4uaXRlbSwNCiAgICAgICAgICB0eXBlOiAoaW5kZXggJSAzKSArIDEsDQogICAgICAgIH0pKTsNCiAgICAgICAgdGhpcy5yaXNrRW50ZXJwcmlzZUxpc3RUb3RhbCA9IHJlcy50b3RhbDsNCiAgICAgIH0pOw0KICAgICAgc3VwcHJlc3NMZXZlbENvdW50KHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy8g5bCG5a+56LGh5qC85byP6L2s5o2i5Li65pWw57uE5qC85byPDQogICAgICAgIGNvbnN0IGRhdGEgPSBPYmplY3Qua2V5cyhyZXMuZGF0YSkubWFwKCh5ZWFyKSA9PiAoew0KICAgICAgICAgIHByb2R1Y3Q6IHllYXIsDQogICAgICAgICAg5Lil6YeNOiByZXMuZGF0YVt5ZWFyXS7kuKXph40sDQogICAgICAgICAg5LiA6IisOiByZXMuZGF0YVt5ZWFyXS7kuIDoiKwsDQogICAgICAgICAg6L6D6L27OiByZXMuZGF0YVt5ZWFyXS7ovoPovbssDQogICAgICAgIH0pKTsNCiAgICAgICAgdGhpcy5yaXNrQmFyQ2hhcnREYXRhID0gZGF0YTsNCiAgICAgICAgdGhpcy5zdXBwcmVzc2lvbk9mUmlza3NTaG93TW9kYWwgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIHJpc2tFbnRlcnByaXNlTGlzdFBhZ2luYXRpb24ocXVlcnlQYXJhbXMpIHsNCiAgICAgIHN1cHByZXNzRW50ZXJwcmlzZUxpc3Qoew0KICAgICAgICBwcm9qZWN0U246ICIxIiwNCiAgICAgICAgc2NyZWVuU246ICIxIiwNCiAgICAgICAgY29sdW1uU246ICIxIiwNCiAgICAgICAgLi4ucXVlcnlQYXJhbXMsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yaXNrRW50ZXJwcmlzZUxpc3QgPSByZXMucm93cy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoew0KICAgICAgICAgIC4uLml0ZW0sDQogICAgICAgICAgdHlwZTogKGluZGV4ICUgMykgKyAxLA0KICAgICAgICB9KSk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0UG9saWN5Umlza0RldGFpbCgpIHsNCiAgICAgIHByb3Bvc2Fsc0xpc3Qoew0KICAgICAgICBwcm9qZWN0U246ICIxIiwNCiAgICAgICAgc2NyZWVuU246ICIxIiwNCiAgICAgICAgY29sdW1uU246ICIxIiwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucG9saWN5Umlza0xpc3QxID0gcmVzLnJvd3M7DQogICAgICAgIHRoaXMucG9saWN5Umlza0xpc3QxVG90YWwgPSByZXMudG90YWw7DQogICAgICB9KTsNCiAgICAgIHByb3Bvc2Fsc1RvQ2hpbmFEYXRhKHsNCiAgICAgICAgcHJvamVjdFNuOiAiMSIsDQogICAgICAgIHNjcmVlblNuOiAiMSIsDQogICAgICAgIGNvbHVtblNuOiAiMSIsDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5wb2xpY3lSaXNrTGlzdDIgPSByZXMuZGF0YTsNCiAgICAgICAgdGhpcy5wb2xpY3lSaXNrU2hvd01vZGFsID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBwb2xpY3lSaXNrUGFnaW5hdGlvbihxdWVyeVBhcmFtcykgew0KICAgICAgcHJvcG9zYWxzTGlzdCh7DQogICAgICAgIHByb2plY3RTbjogIjEiLA0KICAgICAgICBzY3JlZW5TbjogIjEiLA0KICAgICAgICBjb2x1bW5TbjogIjEiLA0KICAgICAgICAuLi5xdWVyeVBhcmFtcywNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnBvbGljeVJpc2tMaXN0MSA9IHJlcy5yb3dzOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIG9wZW5Ib3RUZWNobm9sb2d5KGRhdGEpIHsNCiAgICAgIHRoaXMuaG90VGVjaG5vbG9neVNob3dNb2RhbCA9IHRydWU7DQogICAgICB0aGlzLmhvdFRlY2hub2xvZ3l0VGl0bGUgPSBkYXRhLnRpdGxlOw0KICAgICAgdGhpcy5ob3RUZWNobm9sb2d5dElEID0gZGF0YS5yZXBvcnRTbjsNCiAgICB9LA0KICAgIG9wZW5iYWFyVHJlZUVjaGFydHMoZGF0YSkgew0KICAgICAgLy8gdGhpcy5iYWFyVHJlZUVjaGFydHNUeXBlID0gcGFyc2VJbnQodGhpcy4kcmVmcy50cnVtcFZpZXdUcmVlLm5vZGVUeXBlLnJlcGxhY2UoL1xEKy9nLCAnJyksIDEwKQ0KICAgICAgLy8gdGhpcy5iYWFyVHJlZUVjaGFydHNTaG93TW9kYWwgPSB0cnVlOw0KDQogICAgICAvLyDmoLnmja7lvZPliY3pgInkuK3nmoR0YWLmnaXlhrPlrprkvb/nlKjlk6rkuKptYXJrT2JqDQogICAgICBpZiAodGhpcy5hY3RpdmVUYWIgPT09ICJ0cnVtcCIpIHsNCiAgICAgICAgLy8g5L2/55So5a+85YWl55qEbWFya2Rvd25EYXRhDQogICAgICAgIHRoaXMubWFya21hcENvbnRlbnQgPQ0KICAgICAgICAgIG1hcmtkb3duRGF0YS50cnVtcFt0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLm5vZGVUeXBlXTsNCiAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi54m55pyX5pmuIjsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5hY3RpdmVUYWIgPT09ICJtc2siKSB7DQogICAgICAgIC8vIOmprOaWr+WFi+eahG1hcmtkb3duRGF0YQ0KICAgICAgICB0aGlzLm1hcmttYXBDb250ZW50ID0NCiAgICAgICAgICBtYXJrZG93bkRhdGEubXNrW3RoaXMuJHJlZnMuY2hhcmFjdGVyVmlld1RyZWUubm9kZVR5cGVdOw0KICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLln4PpmobCt+mHjOWkq8K36ams5pav5YWLIjsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5hY3RpdmVUYWIgPT09ICJ3cyIpIHsNCiAgICAgICAgLy8g5LiH5pav55qEbWFya2Rvd25EYXRhDQogICAgICAgIHRoaXMubWFya21hcENvbnRlbnQgPQ0KICAgICAgICAgIG1hcmtkb3duRGF0YS53c1t0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLm5vZGVUeXBlXTsNCiAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi6Km55aeG5pavwrfllJDnurPlvrfCt+S4h+aWryI7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1hcmttYXBDb250ZW50ID0gdGhpcy5kb21haW5NYXJrZG93bjsNCiAgICAgICAgc3dpdGNoICh0aGlzLmFjdGl2ZVRhYikgew0KICAgICAgICAgIGNhc2UgImJkdCI6DQogICAgICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLljYrlr7zkvZPpoobln58iOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAiZ2R6YiI6DQogICAgICAgICAgICB0aGlzLm1hcmttYXBUaXRsZSA9ICLpq5jnq6/oo4XlpIfkuI7mnZDmlpkiOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAieG55cWMiOg0KICAgICAgICAgICAgdGhpcy5tYXJrbWFwVGl0bGUgPSAi5paw6IO95rqQ5rG96L2m5LiO55S15rGgIjsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgInN6aHp4IjoNCiAgICAgICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIuaVsOWtl+WMlui9rOWei+S4juW3peS4mui9r+S7tiI7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJsc3p6IjoNCiAgICAgICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIue7v+iJsuWItumAoOS4juaWsOiDvea6kCI7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJzd3l5IjoNCiAgICAgICAgICAgIHRoaXMubWFya21hcFRpdGxlID0gIueUn+eJqeWMu+iNr+S4juWMu+eWl+WZqOaisCI7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgdGhpcy5haUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMubWFya21hcFZpc2libGUgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBvcGVuVGVjaG5vbG9neURldGFpbHMoZGF0YSkgew0KICAgICAgdGhpcy50ZWNobm9sb2d5RGV0YWlsc1Nob3dNb2RhbCA9IHRydWU7DQogICAgICB0aGlzLnRlY2hub2xvZ3lEZXRhaWxzVGl0bGUgPSBkYXRhLm5hbWU7DQogICAgICB0aGlzLnRlY2hub2xvZ3lEZXRhaWxzSXRlbSA9IGRhdGEuZGF0YTsNCiAgICB9LA0KDQogICAgLy8g54Ot54K55o6o6I2Q55u45YWz5pa55rOVDQogICAgaW5pdEhvdExpc3QoKSB7DQogICAgICAvLyDkvb/nlKhiaWdTY3JlZW5UaHJlZeebuOWQjOeahEFQSeaOpeWPow0KICAgICAgbGFyZ2VIb3RMaXN0MigpDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLnJlbWVuZ3dlbnpoYW5nTGlzdCA9IHJlcy5kYXRhIHx8IFtdOw0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuc3RhcnRTY3JvbGwoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPlueDreeCueaOqOiNkOaVsOaNruWksei0pToiLCBlcnJvcik7DQogICAgICAgICAgLy8g5aaC5p6cQVBJ6LCD55So5aSx6LSl77yM5L2/55So56m65pWw57uEDQogICAgICAgICAgdGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3QgPSBbXTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBpbml0SG90TGlzdDEoKSB7DQogICAgICB0aGlzLmhhbmRsZUJ1dHRvbkNsaWNrKCLohJHmnLrmjqXlj6MiKTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5zdGFydFNjcm9sbDIoKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBzdGFydFNjcm9sbCgpIHsNCiAgICAgIHRoaXMuY2xlYXJTY3JvbGxUaW1lcigpOw0KICAgICAgY29uc3Qgd3JhcHBlciA9IHRoaXMuJHJlZnMuc2Nyb2xsV3JhcHBlcjsNCiAgICAgIGNvbnN0IGNvbnRlbnQgPSB0aGlzLiRyZWZzLnNjcm9sbENvbnRlbnQ7DQoNCiAgICAgIGlmICghd3JhcHBlciB8fCAhY29udGVudCkgcmV0dXJuOw0KDQogICAgICB0aGlzLnNjcm9sbFRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICBpZiAodGhpcy5pc0hvdmVyZWQpIHJldHVybjsNCg0KICAgICAgICBpZiAod3JhcHBlci5zY3JvbGxUb3AgPj0gY29udGVudC5zY3JvbGxIZWlnaHQgLSB3cmFwcGVyLmNsaWVudEhlaWdodCkgew0KICAgICAgICAgIHdyYXBwZXIuc2Nyb2xsVG9wID0gMDsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB3cmFwcGVyLnNjcm9sbFRvcCArPSB0aGlzLnNjcm9sbFN0ZXA7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy51cGRhdGVTY3JvbGxiYXIoKTsNCiAgICAgIH0sIDQwKTsNCiAgICB9LA0KDQogICAgY2xlYXJTY3JvbGxUaW1lcigpIHsNCiAgICAgIGlmICh0aGlzLnNjcm9sbFRpbWVyKSB7DQogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5zY3JvbGxUaW1lcik7DQogICAgICAgIHRoaXMuc2Nyb2xsVGltZXIgPSBudWxsOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVNb3VzZUVudGVyKCkgew0KICAgICAgdGhpcy5pc0hvdmVyZWQgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBoYW5kbGVNb3VzZUxlYXZlKCkgew0KICAgICAgdGhpcy5pc0hvdmVyZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuc3RhcnRTY3JvbGwoKTsNCiAgICB9LA0KICAgIHN0YXJ0U2Nyb2xsMSgpIHsNCiAgICAgIHRoaXMuY2xlYXJTY3JvbGxUaW1lcjEoKTsNCiAgICAgIGNvbnN0IHdyYXBwZXIgPSB0aGlzLiRyZWZzLnNjcm9sbFdyYXBwZXIxOw0KICAgICAgY29uc3QgY29udGVudCA9IHRoaXMuJHJlZnMuc2Nyb2xsQ29udGVudDE7DQoNCiAgICAgIGlmICghd3JhcHBlciB8fCAhY29udGVudCkgcmV0dXJuOw0KDQogICAgICB0aGlzLnNjcm9sbFRpbWVyMSA9IHNldEludGVydmFsKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuaXNIb3ZlcmVkMSkgcmV0dXJuOw0KDQogICAgICAgIGlmICh3cmFwcGVyLnNjcm9sbFRvcCA+PSBjb250ZW50LnNjcm9sbEhlaWdodCAtIHdyYXBwZXIuY2xpZW50SGVpZ2h0KSB7DQogICAgICAgICAgd3JhcHBlci5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHdyYXBwZXIuc2Nyb2xsVG9wICs9IHRoaXMuc2Nyb2xsU3RlcDsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnVwZGF0ZVNjcm9sbGJhcjEoKTsNCiAgICAgIH0sIDIwKTsNCiAgICB9LA0KICAgIHN0YXJ0U2Nyb2xsMigpIHsNCiAgICAgIHRoaXMuY2xlYXJTY3JvbGxUaW1lcjIoKTsNCiAgICAgIHRoaXMuc2Nyb2xsVGltZXIyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICBpZiAodGhpcy5pc0hvdmVyZWQxKSByZXR1cm47DQoNCiAgICAgICAgLy8g5a6a5LmJ5omA5pyJdGFi5qCH562+55qE6aG65bqPDQogICAgICAgIGNvbnN0IHRhYk9yZGVyID0gWw0KICAgICAgICAgICLohJHmnLrmjqXlj6MiLA0KICAgICAgICAgICLph4/lrZDkv6Hmga8iLA0KICAgICAgICAgICLkurrlvaLmnLrlmajkuroiLA0KICAgICAgICAgICLnlJ/miJDlvI/kurrlt6Xmmbrog70iLA0KICAgICAgICAgICLnlJ/nianliLbpgKAiLA0KICAgICAgICAgICLmnKrmnaXmmL7npLoiLA0KICAgICAgICAgICLmnKrmnaXnvZHnu5wiLA0KICAgICAgICAgICLmlrDlnovlgqjog70iLA0KICAgICAgICAgICLlhbbku5YiLA0KICAgICAgICBdOw0KDQogICAgICAgIC8vIOaJvuWIsOW9k+WJjea0u+i3g+agh+etvueahOe0ouW8lQ0KICAgICAgICBjb25zdCBjdXJyZW50SW5kZXggPSB0YWJPcmRlci5pbmRleE9mKHRoaXMuYWN0aXZlQnV0dG9uKTsNCiAgICAgICAgLy8g6K6h566X5LiL5LiA5Liq5qCH562+55qE57Si5byV77yM5aaC5p6c5Yiw5pyA5ZCO5LiA5Liq5YiZ5Zue5Yiw56ys5LiA5LiqDQogICAgICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRhYk9yZGVyLmxlbmd0aDsNCiAgICAgICAgLy8g5YiH5o2i5Yiw5LiL5LiA5Liq5qCH562+DQogICAgICAgIHRoaXMuaGFuZGxlQnV0dG9uQ2xpY2sodGFiT3JkZXJbbmV4dEluZGV4XSk7DQogICAgICB9LCA4MDAwKTsNCiAgICB9LA0KICAgIGNsZWFyU2Nyb2xsVGltZXIxKCkgew0KICAgICAgaWYgKHRoaXMuc2Nyb2xsVGltZXIxKSB7DQogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5zY3JvbGxUaW1lcjEpOw0KICAgICAgICB0aGlzLnNjcm9sbFRpbWVyMSA9IG51bGw7DQogICAgICB9DQogICAgfSwNCiAgICBjbGVhclNjcm9sbFRpbWVyMigpIHsNCiAgICAgIGlmICh0aGlzLnNjcm9sbFRpbWVyMikgew0KICAgICAgICBjbGVhckludGVydmFsKHRoaXMuc2Nyb2xsVGltZXIyKTsNCiAgICAgICAgdGhpcy5zY3JvbGxUaW1lcjIgPSBudWxsOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlTW91c2VFbnRlcjEoKSB7DQogICAgICB0aGlzLmlzSG92ZXJlZDEgPSB0cnVlOw0KICAgIH0sDQoNCiAgICBoYW5kbGVNb3VzZUxlYXZlMSgpIHsNCiAgICAgIHRoaXMuaXNIb3ZlcmVkMSA9IGZhbHNlOw0KICAgICAgLy8gdGhpcy5zdGFydFNjcm9sbDEoKTsNCiAgICAgIC8vIHRoaXMuc3RhcnRTY3JvbGwyKCk7DQogICAgfSwNCiAgICBoYW5kbGVNb3VzZUVudGVyMigpIHsNCiAgICAgIHRoaXMuaXNIb3ZlcmVkMiA9IHRydWU7DQogICAgfSwNCg0KICAgIGhhbmRsZU1vdXNlTGVhdmUyKCkgew0KICAgICAgdGhpcy5pc0hvdmVyZWQyID0gZmFsc2U7DQogICAgfSwNCiAgICB1cGRhdGVTY3JvbGxiYXIoKSB7DQogICAgICBjb25zdCB3cmFwcGVyID0gdGhpcy4kcmVmcy5zY3JvbGxXcmFwcGVyOw0KICAgICAgaWYgKCF3cmFwcGVyKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IHsgc2Nyb2xsVG9wLCBzY3JvbGxIZWlnaHQsIGNsaWVudEhlaWdodCB9ID0gd3JhcHBlcjsNCiAgICAgIGNvbnN0IHNjcm9sbFBlcmNlbnQgPSBjbGllbnRIZWlnaHQgLyBzY3JvbGxIZWlnaHQ7DQogICAgICBjb25zdCBzY3JvbGxiYXJIZWlnaHQgPSBNYXRoLm1heCgzMCwgc2Nyb2xsUGVyY2VudCAqIGNsaWVudEhlaWdodCk7DQogICAgICBjb25zdCBzY3JvbGxiYXJUb3AgPSAoc2Nyb2xsVG9wIC8gc2Nyb2xsSGVpZ2h0KSAqIGNsaWVudEhlaWdodDsNCg0KICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLnNldFByb3BlcnR5KA0KICAgICAgICAiLS1zY3JvbGxiYXItaGVpZ2h0IiwNCiAgICAgICAgYCR7c2Nyb2xsYmFySGVpZ2h0fXB4YA0KICAgICAgKTsNCiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5zZXRQcm9wZXJ0eSgNCiAgICAgICAgIi0tc2Nyb2xsYmFyLXRvcCIsDQogICAgICAgIGAke3Njcm9sbGJhclRvcH1weGANCiAgICAgICk7DQogICAgfSwNCiAgICB1cGRhdGVTY3JvbGxiYXIxKCkgew0KICAgICAgY29uc3Qgd3JhcHBlciA9IHRoaXMuJHJlZnMuc2Nyb2xsV3JhcHBlcjE7DQogICAgICBpZiAoIXdyYXBwZXIpIHJldHVybjsNCg0KICAgICAgY29uc3QgeyBzY3JvbGxUb3AsIHNjcm9sbEhlaWdodCwgY2xpZW50SGVpZ2h0IH0gPSB3cmFwcGVyOw0KICAgICAgY29uc3Qgc2Nyb2xsUGVyY2VudCA9IGNsaWVudEhlaWdodCAvIHNjcm9sbEhlaWdodDsNCiAgICAgIGNvbnN0IHNjcm9sbGJhckhlaWdodCA9IE1hdGgubWF4KDMwLCBzY3JvbGxQZXJjZW50ICogY2xpZW50SGVpZ2h0KTsNCiAgICAgIGNvbnN0IHNjcm9sbGJhclRvcCA9IChzY3JvbGxUb3AgLyBzY3JvbGxIZWlnaHQpICogY2xpZW50SGVpZ2h0Ow0KDQogICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc3R5bGUuc2V0UHJvcGVydHkoDQogICAgICAgICItLXNjcm9sbGJhci1oZWlnaHQiLA0KICAgICAgICBgJHtzY3JvbGxiYXJIZWlnaHR9cHhgDQogICAgICApOw0KICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLnNldFByb3BlcnR5KA0KICAgICAgICAiLS1zY3JvbGxiYXItdG9wIiwNCiAgICAgICAgYCR7c2Nyb2xsYmFyVG9wfXB4YA0KICAgICAgKTsNCiAgICB9LA0KDQogICAgYXN5bmMgb3Blbk5ld1ZpZXcoaXRlbSkgew0KICAgICAgLy8g5L2/55SoYmlnU2NyZWVuVGhyZWXnm7jlkIznmoRBUEnmjqXlj6MNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGxhcmdlSG90UXVlcnlCeUlkKGl0ZW0uaWQpOw0KICAgICAgICB0aGlzLmRyYXdlckluZm8gPSB7DQogICAgICAgICAgY25UaXRsZToNCiAgICAgICAgICAgIGl0ZW0uY25UaXRsZSB8fCBpdGVtLnRpdGxlIHx8IHJlcy5kYXRhLnRpdGxlIHx8IHJlcy5kYXRhLmNuVGl0bGUsDQogICAgICAgICAgdGl0bGU6DQogICAgICAgICAgICBpdGVtLnRpdGxlIHx8IGl0ZW0uY25UaXRsZSB8fCByZXMuZGF0YS50aXRsZSB8fCByZXMuZGF0YS5jblRpdGxlLA0KICAgICAgICAgIGNuQ29udGVudDogcmVzLmRhdGEuY29udGVudCB8fCByZXMuZGF0YS5jbkNvbnRlbnQsDQogICAgICAgIH07DQoNCiAgICAgICAgLy8g5aSE55CG5YaF5a655qC85byPDQogICAgICAgIGxldCBjb250ZW50ID0gdGhpcy5mb3JtYXR0aW5nSnNvbih0aGlzLmRyYXdlckluZm8uY25Db250ZW50KTsNCiAgICAgICAgLy8gaWYgKGNvbnRlbnQpIHsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cbi9nLCAiPGJyPiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1wke1tefV0rfS9nLCAiPGJyPiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoInx4YTAiLCAiIik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgib3BhY2l0eTogMCIsICIiKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC88aW1nXGJbXj5dKj4vZ2ksICIiKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC8gc3R5bGU9IlteIl0qIi9nLCAiIik7DQogICAgICAgIC8vIH0NCiAgICAgICAgdGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCA9IGNvbnRlbnQ7DQoNCiAgICAgICAgdGhpcy5hcnRpY2xlRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICAgIHRoaXMub3JpRm9udFNpemUgPSB0aGlzLmZvbnRTaXplOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5paH56ug6K+m5oOF5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgLy8g5aaC5p6cQVBJ6LCD55So5aSx6LSl77yM5pi+56S65Z+65pys5L+h5oGvDQogICAgICAgIHRoaXMuZHJhd2VySW5mbyA9IHsNCiAgICAgICAgICBjblRpdGxlOiBpdGVtLnRpdGxlIHx8IGl0ZW0uY25UaXRsZSwNCiAgICAgICAgICB0aXRsZTogaXRlbS50aXRsZSB8fCBpdGVtLmNuVGl0bGUsDQogICAgICAgICAgY25Db250ZW50OiAi5pqC5peg6K+m57uG5YaF5a65IiwNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy5hcnRpY2xlRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICAgIHRoaXMub3JpRm9udFNpemUgPSB0aGlzLmZvbnRTaXplOw0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgb3Blbk5ld1ZpZXcxKGl0ZW0pIHsNCiAgICAgIC8vIOS9v+eUqGJpZ1NjcmVlblRocmVl55u45ZCM55qEQVBJ5o6l5Y+jDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRMYXJnZUZUVChpdGVtLnNuKTsNCiAgICAgICAgdGhpcy5kcmF3ZXJJbmZvID0gew0KICAgICAgICAgIGNuVGl0bGU6DQogICAgICAgICAgICBpdGVtLmNuVGl0bGUgfHwgaXRlbS50aXRsZSB8fCByZXMuZGF0YS50aXRsZSB8fCByZXMuZGF0YS5jblRpdGxlLA0KICAgICAgICAgIHRpdGxlOg0KICAgICAgICAgICAgaXRlbS50aXRsZSB8fCBpdGVtLmNuVGl0bGUgfHwgcmVzLmRhdGEudGl0bGUgfHwgcmVzLmRhdGEuY25UaXRsZSwNCiAgICAgICAgICBjbkNvbnRlbnQ6IHJlcy5kYXRhLmNvbnRlbnQgfHwgcmVzLmRhdGEuY25Db250ZW50LA0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOWkhOeQhuWGheWuueagvOW8jw0KICAgICAgICBsZXQgY29udGVudCA9IHRoaXMuZm9ybWF0dGluZ0pzb24odGhpcy5kcmF3ZXJJbmZvLmNuQ29udGVudCk7DQogICAgICAgIC8vIGlmIChjb250ZW50KSB7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXG4vZywgIjxicj4iKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cJHtbXn1dK30vZywgIjxicj4iKTsNCiAgICAgICAgLy8gICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJ8eGEwIiwgIiIpOw0KICAgICAgICAvLyAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoIm9wYWNpdHk6IDAiLCAiIik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZ1xiW14+XSo+L2dpLCAiIik7DQogICAgICAgIC8vICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvIHN0eWxlPSJbXiJdKiIvZywgIiIpOw0KICAgICAgICAvLyB9DQogICAgICAgIHRoaXMuZHJhd2VySW5mby5jbkNvbnRlbnQgPSBjb250ZW50Ow0KDQogICAgICAgIHRoaXMuYXJ0aWNsZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB0aGlzLm9yaUZvbnRTaXplID0gdGhpcy5mb250U2l6ZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluaWh+eroOivpuaDheWksei0pToiLCBlcnJvcik7DQogICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOaYvuekuuWfuuacrOS/oeaBrw0KICAgICAgICB0aGlzLmRyYXdlckluZm8gPSB7DQogICAgICAgICAgY25UaXRsZTogaXRlbS50aXRsZSB8fCBpdGVtLmNuVGl0bGUsDQogICAgICAgICAgdGl0bGU6IGl0ZW0udGl0bGUgfHwgaXRlbS5jblRpdGxlLA0KICAgICAgICAgIGNuQ29udGVudDogIuaaguaXoOivpue7huWGheWuuSIsDQogICAgICAgIH07DQogICAgICAgIHRoaXMuYXJ0aWNsZURpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgICB0aGlzLm9yaUZvbnRTaXplID0gdGhpcy5mb250U2l6ZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGZvcm1hdHRpbmdKc29uKGNvbnRlbnQpIHsNCiAgICAgIGlmIChjb250ZW50KSB7DQogICAgICAgIGlmIChjb250YWluc0h0bWxUYWdzKGNvbnRlbnQpKSB7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGJyPi9nLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXG4vZywgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xcbi9nLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvXFxcbi9nLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgifHhhMCIsICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJvcGFjaXR5OiAwIiwgIiIpOw0KICAgICAgICAgIC8vIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1wke1tefV0rfS9nLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZ1xiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGZpZ3VyZVxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGlmcmFtZVxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGNueFxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPHZpZGVvXGJbXj5dKj4vZ2ksICIiKTsNCiAgICAgICAgICAvLyDnp7vpmaTluKbmoLflvI/nmoTmoIfnrb7vvIzkv53nlZnlhoXlrrkNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKA0KICAgICAgICAgICAgLzwoXHcrKVtePl0qc3R5bGU9IlteIl0qIltePl0qPiguKj8pPFwvXDE+L2dpLA0KICAgICAgICAgICAgIiQyIg0KICAgICAgICAgICk7DQogICAgICAgICAgLy8g56e76Zmk5Lu75L2V5YW25LuW5qC35byP5qCH562+DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICAgIC88KFx3KylbXj5dKmNsYXNzPSJbXiJdKiJbXj5dKj4oLio/KTxcL1wxPi9naSwNCiAgICAgICAgICAgICIkMiINCiAgICAgICAgICApOw0KDQogICAgICAgICAgY29uc29sZS5sb2coIuWMheWQq+eahEhUTUzmoIfnrb4iLCBleHRyYWN0SHRtbFRhZ3MoY29udGVudCkpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCJIVE1M5piv5ZCm57uT5p6E5q2j56GuIiwgaGFzVmFsaWRIdG1sU3RydWN0dXJlKGNvbnRlbnQpKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cbi9nLCAiPGJyPiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xcbi9nLCAiPGJyPiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoL1xcXG4vZywgIjxicj4iKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKC9cJHtbXn1dK30vZywgIjxicj4iKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKCJ8eGEwIiwgIiIpOw0KICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50LnJlcGxhY2UoIm9wYWNpdHk6IDAiLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGltZ1xiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGZpZ3VyZVxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGlmcmFtZVxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPGNueFxiW14+XSo+L2dpLCAiIik7DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgvPHZpZGVvXGJbXj5dKj4vZ2ksICIiKTsNCiAgICAgICAgICBjb250ZW50ID0gY29udGVudC5yZXBsYWNlKA0KICAgICAgICAgICAgLzwoXHcrKVtePl0qc3R5bGU9IlteIl0qIltePl0qPiguKj8pPFwvXDE+L2dpLA0KICAgICAgICAgICAgIiQyIg0KICAgICAgICAgICk7DQogICAgICAgICAgLy8g56e76Zmk5Lu75L2V5YW25LuW5qC35byP5qCH562+DQogICAgICAgICAgY29udGVudCA9IGNvbnRlbnQucmVwbGFjZSgNCiAgICAgICAgICAgIC88KFx3KylbXj5dKmNsYXNzPSJbXiJdKiJbXj5dKj4oLio/KTxcL1wxPi9naSwNCiAgICAgICAgICAgICIkMiINCiAgICAgICAgICApOw0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gY29udGVudDsNCiAgICB9LA0KDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLmRyYXdlckluZm8gPSB7fTsNCiAgICAgIHRoaXMuYXJ0aWNsZURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgaW5jcmVhc2VGb250U2l6ZSgpIHsNCiAgICAgIGlmICh0aGlzLmZvbnRTaXplIDwgMzApIHsNCiAgICAgICAgdGhpcy5mb250U2l6ZSArPSAyOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBkZWNyZWFzZUZvbnRTaXplKCkgew0KICAgICAgaWYgKHRoaXMuZm9udFNpemUgPiAxNikgew0KICAgICAgICB0aGlzLmZvbnRTaXplIC09IDI7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVOb2RlQ2xpY2sodHlwZSkgew0KICAgICAgLy8g5qC55o2u5b2T5YmNYWN0aXZlVGFi6I635Y+W5a+55bqU5Lq654mp55qE5pWw5o2uDQogICAgICBsZXQgY3VycmVudENoYXJhY3RlciA9ICJ0cnVtcCI7IC8vIOm7mOiupOeJueacl+aZrg0KICAgICAgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAibXNrIikgew0KICAgICAgICBjdXJyZW50Q2hhcmFjdGVyID0gIm1zayI7DQogICAgICB9IGVsc2UgaWYgKHRoaXMuYWN0aXZlVGFiID09PSAid3MiKSB7DQogICAgICAgIGN1cnJlbnRDaGFyYWN0ZXIgPSAid3MiOw0KICAgICAgfQ0KICAgICAgbGV0IHJhd0RhdGEgPSBKU09OLnBhcnNlKA0KICAgICAgICBKU09OLnN0cmluZ2lmeSh0cmVlRGF0YTJbY3VycmVudENoYXJhY3Rlcl1bdHlwZV0gfHwgW10pDQogICAgICApOw0KICAgICAgdGhpcy5jaGFyYWN0ZXJWaWV3RGF0YSA9IHRoaXMubGltaXRMZXZlbDNDaGlsZHJlbihyYXdEYXRhKTsNCiAgICB9LA0KICAgIGxpbWl0TGV2ZWwzQ2hpbGRyZW4oZGF0YSkgew0KICAgICAgaWYgKCFkYXRhIHx8ICFBcnJheS5pc0FycmF5KGRhdGEpKSByZXR1cm4gZGF0YTsNCiAgICAgIHJldHVybiBkYXRhLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICBpZiAoDQogICAgICAgICAgKGl0ZW0udHlwZSA9PSAibGV2ZWwyLTEiIHx8DQogICAgICAgICAgICBpdGVtLnR5cGUgPT0gImxldmVsMi0yIiB8fA0KICAgICAgICAgICAgaXRlbS50eXBlID09ICJsZXZlbDItMyIpICYmDQogICAgICAgICAgQXJyYXkuaXNBcnJheShpdGVtLmNoaWxkcmVuKQ0KICAgICAgICApIHsNCiAgICAgICAgICBpdGVtLmNoaWxkcmVuID0gaXRlbS5jaGlsZHJlbi5zbGljZSgwLCAyKTsgLy8g5Y+q5L+d55WZ5YmN5Lik5LiqDQogICAgICAgIH0NCg0KICAgICAgICBpZiAoaXRlbS5jaGlsZHJlbikgew0KICAgICAgICAgIGl0ZW0uY2hpbGRyZW4gPSB0aGlzLmxpbWl0TGV2ZWwzQ2hpbGRyZW4oaXRlbS5jaGlsZHJlbik7DQogICAgICAgIH0NCg0KICAgICAgICByZXR1cm4gaXRlbTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlQnV0dG9uQ2xpY2sodHlwZSkgew0KICAgICAgbGV0IG9iaiA9IHsNCiAgICAgICAg6ISR5py65o6l5Y+jOiAiMyIsDQogICAgICAgIOmHj+WtkOS/oeaBrzogIjQiLA0KICAgICAgICDkurrlvaLmnLrlmajkuro6ICI2IiwNCiAgICAgICAg55Sf5oiQ5byP5Lq65bel5pm66IO9OiAiMSIsDQogICAgICAgIOeUn+eJqeWItumAoDogIjciLA0KICAgICAgICDmnKrmnaXmmL7npLo6ICI4IiwNCiAgICAgICAg5pyq5p2l572R57ucOiAiOSIsDQogICAgICAgIOaWsOWei+WCqOiDvTogIjEwIiwNCiAgICAgICAg5YW25LuWOiAiMiw1LDExLDEyLDEzLDE0LDE1LDE2LDE3IiwNCiAgICAgIH07DQogICAgICB0aGlzLmFjdGl2ZUJ1dHRvbiA9IHR5cGU7DQoNCiAgICAgIC8vIOmHjee9rui9ruaSreaXtumXtA0KICAgICAgdGhpcy5zdGFydFNjcm9sbDIoKTsNCg0KICAgICAga2pkdEFydGljbGVMaXN0KHsNCiAgICAgICAgbGFiZWxTbjogb2JqW3R5cGVdLA0KICAgICAgfSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIC8vIOWvueaVsOaNrui/m+ihjOWOu+mHjeWkhOeQhu+8jOWfuuS6jmNuVGl0bGXljrvpmaTnqbrmoLzlkI7liKTmlq0NCiAgICAgICAgY29uc3QgZGVkdXBsaWNhdGVkRGF0YSA9IHRoaXMuZGVkdXBsaWNhdGVBcnRpY2xlcyhyZXMgfHwgW10pOw0KICAgICAgICB0aGlzLnJlbWVuZ3dlbnpoYW5nTGlzdDEgPSBkZWR1cGxpY2F0ZWREYXRhOw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgY29uc3Qgd3JhcHBlciA9IHRoaXMuJHJlZnMuc2Nyb2xsV3JhcHBlcjE7DQogICAgICAgICAgd3JhcHBlci5zY3JvbGxUb3AgPSAwOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgcXlramR0T3Blbk5ld1RhYigpIHsNCiAgICAgIGxldCBvYmogPSB7DQogICAgICAgIOiEkeacuuaOpeWPozogIi9xaWFueWFua2VqaWRvbmd0YWkvbmFvamlqaWVrb3U/aWQ9MSZkb21haW49MyIsDQogICAgICAgIOmHj+WtkOS/oeaBrzogIi9xaWFueWFua2VqaWRvbmd0YWkvbGlhbmd6aXhpbnhpP2lkPTEmZG9tYWluPTQiLA0KICAgICAgICDkurrlvaLmnLrlmajkuro6ICIvcWlhbnlhbmtlamlkb25ndGFpL3JlbnhpbmdqaXFpcmVuP2lkPTEmZG9tYWluPTYiLA0KICAgICAgICDnlJ/miJDlvI/kurrlt6Xmmbrog706ICIvcWlhbnlhbmtlamlkb25ndGFpL3Jlbmdvbmd6aGluZW5nP2lkPTEmZG9tYWluPTEiLA0KICAgICAgICDnlJ/nianliLbpgKA6ICIvcWlhbnlhbmtlamlkb25ndGFpL3NoZW5nd3V6aGl6YW8/aWQ9MSZkb21haW49NyIsDQogICAgICAgIOacquadpeaYvuekujogIi9xaWFueWFua2VqaWRvbmd0YWkvd2VpbGFpeGlhbnNoaT9pZD0xJmRvbWFpbj04IiwNCiAgICAgICAg5pyq5p2l572R57ucOiAiL3FpYW55YW5rZWppZG9uZ3RhaS93ZWlsYWl3YW5nbHVvP2lkPTEmZG9tYWluPTkiLA0KICAgICAgICDmlrDlnovlgqjog706ICIvcWlhbnlhbmtlamlkb25ndGFpL3hpbnhpbmdjaHVuZW5nP2lkPTEmZG9tYWluPTEwIiwNCiAgICAgICAg5YW25LuWOiAiL3FpYW55YW5rZWppZG9uZ3RhaS9xaXRhP2lkPTEmZG9tYWluPTIsNSwxMSwxMiwxMywxNCwxNSwxNiwxNyIsDQogICAgICB9Ow0KICAgICAgd2luZG93Lm9wZW4ob2JqW3RoaXMuYWN0aXZlQnV0dG9uXSwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgLy8g5paH56ug5Y676YeN5pa55rOV77yM5Z+65LqOY25UaXRsZeWOu+mZpOepuuagvOWQjuWIpOaWrQ0KICAgIGRlZHVwbGljYXRlQXJ0aWNsZXMoYXJ0aWNsZXMpIHsNCiAgICAgIGlmICghQXJyYXkuaXNBcnJheShhcnRpY2xlcykpIHsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBzZWVuID0gbmV3IFNldCgpOw0KICAgICAgY29uc3QgcmVzdWx0ID0gW107DQoNCiAgICAgIGFydGljbGVzLmZvckVhY2goKGFydGljbGUpID0+IHsNCiAgICAgICAgaWYgKGFydGljbGUgJiYgYXJ0aWNsZS5jblRpdGxlKSB7DQogICAgICAgICAgLy8g5Y676ZmkY25UaXRsZeS4reeahOaJgOacieepuuagvA0KICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRUaXRsZSA9IGFydGljbGUuY25UaXRsZS5yZXBsYWNlKC9ccysvZywgIiIpOw0KDQogICAgICAgICAgaWYgKCFzZWVuLmhhcyhub3JtYWxpemVkVGl0bGUpKSB7DQogICAgICAgICAgICBzZWVuLmFkZChub3JtYWxpemVkVGl0bGUpOw0KICAgICAgICAgICAgcmVzdWx0LnB1c2goYXJ0aWNsZSk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWmguaenOayoeaciWNuVGl0bGXvvIzkuZ/kv53nlZnov5nmnaHorrDlvZUNCiAgICAgICAgICByZXN1bHQucHVzaChhcnRpY2xlKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiByZXN1bHQ7DQogICAgfSwNCiAgICBwYWRXaXRoWmVyb3MobnVtLCB0YXJnZXRMZW5ndGgpIHsNCiAgICAgIGNvbnN0IG51bVN0ciA9IG51bS50b1N0cmluZygpOw0KICAgICAgY29uc3QgcGFkZGluZyA9ICIwIi5yZXBlYXQodGFyZ2V0TGVuZ3RoIC0gbnVtU3RyLmxlbmd0aCk7DQogICAgICByZXR1cm4gYCR7cGFkZGluZ30ke251bVN0cn1gLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICIsIik7DQogICAgfSwNCiAgICBvcGVuTmV3VGFiKHVybCkgew0KICAgICAgd2luZG93Lm9wZW4odXJsLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICBoYW5kbGVNYXJrbWFwQ2xvc2UoKSB7DQogICAgICB0aGlzLm1hcmttYXBDb250ZW50ID0gIiI7DQogICAgICB0aGlzLmFpTG9hZGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy5tYXJrbWFwVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICAvLyDmm7TmlrDng63ngrnmjqjojZDmlofnq6DliJfooagNCiAgICBhc3luYyB1cGRhdGVIb3RBcnRpY2xlc0xpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxhcmdlSG90TGlzdDIoKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBuZXdBcnRpY2xlcyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgLy8g5a+55q+U5pWw5o2u5piv5ZCm5LiA6Ie0DQogICAgICAgICAgaWYgKHRoaXMuaXNBcnRpY2xlRGF0YUNoYW5nZWQobmV3QXJ0aWNsZXMpKSB7DQogICAgICAgICAgICB0aGlzLnJlbWVuZ3dlbnpoYW5nTGlzdCA9IG5ld0FydGljbGVzOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLmm7TmlrDng63ngrnmjqjojZDmlofnq6DliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmo4Dmn6Xmlofnq6DmlbDmja7mmK/lkKblj5HnlJ/lj5jljJYNCiAgICBpc0FydGljbGVEYXRhQ2hhbmdlZChuZXdBcnRpY2xlcykgew0KICAgICAgLy8g5aaC5p6c5b2T5YmN5YiX6KGo5Li656m677yM55u05o6l6L+U5ZuedHJ1ZQ0KICAgICAgaWYgKHRoaXMucmVtZW5nd2VuemhhbmdMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICByZXR1cm4gbmV3QXJ0aWNsZXMubGVuZ3RoID4gMDsNCiAgICAgIH0NCg0KICAgICAgLy8g5aaC5p6c5pWw6YeP5LiN5ZCM77yM6K+05piO5pyJ5Y+Y5YyWDQogICAgICBpZiAodGhpcy5yZW1lbmd3ZW56aGFuZ0xpc3QubGVuZ3RoICE9PSBuZXdBcnRpY2xlcy5sZW5ndGgpIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQoNCiAgICAgIC8vIOWvueavlOavj+evh+aWh+eroOeahOWFs+mUruS/oeaBrw0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBuZXdBcnRpY2xlcy5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBuZXdBcnRpY2xlID0gbmV3QXJ0aWNsZXNbaV07DQogICAgICAgIGNvbnN0IG9sZEFydGljbGUgPSB0aGlzLnJlbWVuZ3dlbnpoYW5nTGlzdFtpXTsNCg0KICAgICAgICAvLyDlr7nmr5Tmlofnq6BJROOAgeagh+mimOOAgeWPkeW4g+aXtumXtOetieWFs+mUruWtl+autQ0KICAgICAgICBpZiAoDQogICAgICAgICAgbmV3QXJ0aWNsZS5pZCAhPT0gb2xkQXJ0aWNsZS5pZCB8fA0KICAgICAgICAgIG5ld0FydGljbGUudGl0bGUgIT09IG9sZEFydGljbGUudGl0bGUgfHwNCiAgICAgICAgICBuZXdBcnRpY2xlLnB1Ymxpc2hUaW1lICE9PSBvbGRBcnRpY2xlLnB1Ymxpc2hUaW1lIHx8DQogICAgICAgICAgbmV3QXJ0aWNsZS5zb3VyY2VOYW1lICE9PSBvbGRBcnRpY2xlLnNvdXJjZU5hbWUNCiAgICAgICAgKSB7DQogICAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5omA5pyJ5pWw5o2u6YO95LiA6Ie0DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuaKgOacr+mihuWfn+aMiemSrueCueWHuw0KICAgIGhhbmRsZVRlY2hCdXR0b25DbGljayhzY3JlZW5TbiwgYnV0dG9uTmFtZSkgew0KICAgICAgY29uc29sZS5sb2coIuWIh+aNouaKgOacr+mihuWfnzoiLCBidXR0b25OYW1lLCAic2NyZWVuU246Iiwgc2NyZWVuU24pOw0KICAgICAgdGhpcy5hY3RpdmVUZWNoQnV0dG9uID0gc2NyZWVuU247DQogICAgICB0aGlzLmN1cnJlbnRUZWNoU2NyZWVuU24gPSBzY3JlZW5TbjsNCiAgICAgIC8vIOW8ueWHuuaKgOacr+mihuWfn+azoeazoeWbvuW8ueeqlw0KICAgICAgdGhpcy50ZWNoQnViYmxlRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnRlY2hCdWJibGVEaWFsb2dUaXRsZSA9IGJ1dHRvbk5hbWU7DQogICAgICB0aGlzLnRlY2hCdWJibGVEaWFsb2dTY3JlZW5TbiA9IHNjcmVlblNuOw0KDQogICAgICAvLyDpgJrnn6XlrZDnu4Tku7bmm7TmlrDmlbDmja4NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgLy8g5Y+v5Lul6YCa6L+HcmVm55u05o6l6LCD55So5a2Q57uE5Lu255qE5pa55rOV5p2l5Yi35paw5pWw5o2uDQogICAgICAgIC8vIOaIluiAhemAmui/h3dhdGNo55uR5ZCsY3VycmVudFRlY2hTY3JlZW5TbueahOWPmOWMluadpeinpuWPkeWtkOe7hOS7tuabtOaWsA0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOWFs+mXreaKgOacr+mihuWfn+azoeazoeWbvuW8ueeqlw0KICAgIGhhbmRsZVRlY2hCdWJibGVEaWFsb2dDbG9zZSgpIHsNCiAgICAgIHRoaXMudGVjaEJ1YmJsZURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMudGVjaEJ1YmJsZURpYWxvZ1RpdGxlID0gIiI7DQogICAgICB0aGlzLnRlY2hCdWJibGVEaWFsb2dTY3JlZW5TbiA9ICIiOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbpgJrnn6XlhbPpl60NCiAgICBoYW5kbGVOb3RpZmljYXRpb25DbG9zZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoIm5vdGlmaWNhdGlvbi1jbG9zZSIpOw0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbmn6XnnIvljZXnr4fmlofnq6ANCiAgICBoYW5kbGVWaWV3QXJ0aWNsZShhcnRpY2xlKSB7DQogICAgICB0aGlzLiRlbWl0KCJub3RpZmljYXRpb24tdmlldy1hcnRpY2xlIiwgYXJ0aWNsZSk7DQogICAgfSwNCg0KICAgIC8vIOWIh+aNouaZuuW6k+ingueCuXRhYg0KICAgIHN3aXRjaFRhYih0YWJOYW1lLCBtYXJrZG93blR5cGUpIHsNCiAgICAgIGlmIChtYXJrZG93blR5cGUpIHsNCiAgICAgICAgdGhpcy5hY3RpdmVUYWIgPSB0YWJOYW1lOw0KICAgICAgICB0aGlzLmRvbWFpbk1hcmtkb3duID0gbWFya09ialsidHlwZSIgKyBtYXJrZG93blR5cGVdOw0KICAgICAgICB0aGlzLnJlbmRlck1hcmttYXAoKTsNCiAgICAgIH0gZWxzZSBpZiAodGFiTmFtZSA9PT0gInRydW1wIiB8fCB0YWJOYW1lID09PSAibXNrIiB8fCB0YWJOYW1lID09PSAid3MiKSB7DQogICAgICAgIC8vIOWmguaenOeCueWHu+eahOaYr+W9k+WJjeW3sua/gOa0u+eahOS6uueJqXRhYu+8jOmcgOimgemHjeaWsOinpuWPkeaVsOaNruWKoOi9vQ0KICAgICAgICBpZiAodGhpcy5hY3RpdmVUYWIgPT09IHRhYk5hbWUpIHsNCiAgICAgICAgICAvLyDmuIXnqbrmlbDmja4NCiAgICAgICAgICB0aGlzLmNoYXJhY3RlclZpZXdEYXRhID0gW107DQogICAgICAgICAgLy8g6YCa6L+HcmVm55u05o6l6LCD55SodHJ1bXBWaWV3VHJlZee7hOS7tueahOaWueazleadpemHjeaWsOWIneWni+WMlg0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIGlmICgNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5jaGFyYWN0ZXJWaWV3VHJlZSAmJg0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLmFsbFR5cGVzLmxlbmd0aCA+IDANCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLmhhbmRsZU5vZGVDbGljaygNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmNoYXJhY3RlclZpZXdUcmVlLmFsbFR5cGVzWzBdDQogICAgICAgICAgICAgICk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5YiH5o2i5Yiw5LiN5ZCM55qE5Lq654mpdGFi5pe277yM6K6+572uYWN0aXZlVGFi77yM6K6pd2F0Y2hlcuiHquWKqOWkhOeQhg0KICAgICAgICAgIHRoaXMuYWN0aXZlVGFiID0gdGFiTmFtZTsNCiAgICAgICAgICB0aGlzLmNoYXJhY3RlclZpZXdEYXRhID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHJlbmRlck1hcmttYXAoKSB7DQogICAgICBpZiAoIXRoaXMuZG9tYWluTWFya2Rvd24pIHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kbmV4dFRpY2soKTsNCiAgICAgICAgY29uc3Qgc3ZnID0gdGhpcy4kcmVmcy5tYXJrbWFwOw0KICAgICAgICBpZiAoIXN2Zykgew0KICAgICAgICAgIHRocm93IG5ldyBFcnJvcigiU1ZHIGVsZW1lbnQgbm90IGZvdW5kIik7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmuIXnqbrkuYvliY3nmoTlhoXlrrkNCiAgICAgICAgc3ZnLmlubmVySFRNTCA9ICIiOw0KDQogICAgICAgIC8vIOWkhOeQhuWGheWuue+8jOenu+mZpCBtYXJrZG93biDmoIforrANCiAgICAgICAgbGV0IHByb2Nlc3NlZENvbnRlbnQgPSB0aGlzLmRvbWFpbk1hcmtkb3duDQogICAgICAgICAgLnJlcGxhY2UoL15gYGBtYXJrZG93blxzKi9pLCAiIikgLy8g56e76Zmk5byA5aS055qEIGBgYG1hcmtkb3duDQogICAgICAgICAgLnJlcGxhY2UoL1xzKmBgYFxzKiQvLCAiIik7IC8vIOenu+mZpOe7k+WwvueahCBgYGANCg0KICAgICAgICBjb25zdCB0cmFuc2Zvcm1lciA9IG5ldyBUcmFuc2Zvcm1lcigpOw0KICAgICAgICBjb25zdCB7IHJvb3QgfSA9IHRyYW5zZm9ybWVyLnRyYW5zZm9ybShwcm9jZXNzZWRDb250ZW50KTsNCg0KICAgICAgICAvLyDliJvlu7rmgJ3nu7Tlr7zlm74NCiAgICAgICAgY29uc3QgbW0gPSBNYXJrbWFwLmNyZWF0ZSgNCiAgICAgICAgICBzdmcsDQogICAgICAgICAgew0KICAgICAgICAgICAgYXV0b0ZpdDogdHJ1ZSwNCiAgICAgICAgICAgIGR1cmF0aW9uOiAwLA0KICAgICAgICAgICAgbm9kZU1pbkhlaWdodDogMjAsDQogICAgICAgICAgICBzcGFjaW5nVmVydGljYWw6IDEwLA0KICAgICAgICAgICAgc3BhY2luZ0hvcml6b250YWw6IDEwMCwNCiAgICAgICAgICAgIHBhZGRpbmdYOiAyMCwNCiAgICAgICAgICAgIGNvbG9yOiAobm9kZSkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBjb2xvcnMgPSB7DQogICAgICAgICAgICAgICAgMDogIiMwMDUyZmYiLCAvLyDkuq7ok53oibINCiAgICAgICAgICAgICAgICAxOiAiIzAwOTYwMCIsIC8vIOS6rue7v+iJsg0KICAgICAgICAgICAgICAgIDI6ICIjZmY2NjAwIiwgLy8g5Lqu5qmZ6ImyDQogICAgICAgICAgICAgICAgMzogIiM4MDAwZmYiLCAvLyDkuq7ntKvoibINCiAgICAgICAgICAgICAgICA0OiAiI2ZmMDA2NiIsIC8vIOS6rueyieiJsg0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICByZXR1cm4gY29sb3JzW25vZGUuZGVwdGhdIHx8ICIjMDA1MmZmIjsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBub2RlRm9udDogKG5vZGUpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgZm9udHMgPSB7DQogICAgICAgICAgICAgICAgMDogJ2JvbGQgMjBweC8xLjUgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8nLA0KICAgICAgICAgICAgICAgIDE6ICc2MDAgMThweC8xLjUgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8nLA0KICAgICAgICAgICAgICAgIDI6ICc1MDAgMTZweC8xLjUgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAiU2Vnb2UgVUkiLCBSb2JvdG8nLA0KICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICByZXR1cm4gKA0KICAgICAgICAgICAgICAgIGZvbnRzW25vZGUuZGVwdGhdIHx8DQogICAgICAgICAgICAgICAgJzQwMCAxNHB4LzEuNSAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICJTZWdvZSBVSSIsIFJvYm90bycNCiAgICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBtYXhXaWR0aDogNDAwLA0KICAgICAgICAgICAgaW5pdGlhbEV4cGFuZExldmVsOiAtMSwNCiAgICAgICAgICAgIHpvb206IHRydWUsDQogICAgICAgICAgICBwYW46IHRydWUsDQogICAgICAgICAgICBsaW5rU2hhcGU6ICJkaWFnb25hbCIsDQogICAgICAgICAgICBsaW5rV2lkdGg6IChub2RlKSA9PiAyLjUgLSBub2RlLmRlcHRoICogMC41LA0KICAgICAgICAgICAgbGlua0NvbG9yOiAobm9kZSkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBjb2xvcnMgPSB7DQogICAgICAgICAgICAgICAgMDogInJnYmEoMCwgODIsIDI1NSwgMC44KSIsIC8vIOS6ruiTneiJsg0KICAgICAgICAgICAgICAgIDE6ICJyZ2JhKDAsIDE1MCwgMCwgMC44KSIsIC8vIOS6rue7v+iJsg0KICAgICAgICAgICAgICAgIDI6ICJyZ2JhKDI1NSwgMTAyLCAwLCAwLjgpIiwgLy8g5Lqu5qmZ6ImyDQogICAgICAgICAgICAgIH07DQogICAgICAgICAgICAgIHJldHVybiBjb2xvcnNbbm9kZS5kZXB0aF0gfHwgInJnYmEoMTI4LCAwLCAyNTUsIDAuOCkiOw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHJvb3QNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDkv67mlLnliJ3lp4vljJbliqjnlLvpg6jliIYNCiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgbW0uZml0KCk7IC8vIOmAguW6lOinhuWbvuWkp+Wwjw0KDQogICAgICAgICAgLy8g6YeN5paw6K6+572u5pWw5o2u5Lul6Kem5Y+R6YeN57uYDQogICAgICAgICAgY29uc3QgZml0UmF0aW8gPSAwLjk1OyAvLyDnlZnlh7rkuIDkupvovrnot50NCiAgICAgICAgICBjb25zdCB7IG1pblgsIG1heFgsIG1pblksIG1heFkgfSA9IG1tLnN0YXRlOw0KICAgICAgICAgIGNvbnN0IHdpZHRoID0gbWF4WCAtIG1pblg7DQogICAgICAgICAgY29uc3QgaGVpZ2h0ID0gbWF4WSAtIG1pblk7DQogICAgICAgICAgY29uc3QgY29udGFpbmVyV2lkdGggPSBzdmcuY2xpZW50V2lkdGg7DQogICAgICAgICAgY29uc3QgY29udGFpbmVySGVpZ2h0ID0gc3ZnLmNsaWVudEhlaWdodDsNCg0KICAgICAgICAgIC8vIOiuoeeul+WQiOmAgueahOe8qeaUvuavlOS+iw0KICAgICAgICAgIGNvbnN0IHNjYWxlID0gTWF0aC5taW4oDQogICAgICAgICAgICAoY29udGFpbmVyV2lkdGggLyB3aWR0aCkgKiBmaXRSYXRpbywNCiAgICAgICAgICAgIChjb250YWluZXJIZWlnaHQgLyBoZWlnaHQpICogZml0UmF0aW8NCiAgICAgICAgICApOw0KDQogICAgICAgICAgLy8g5pu05paw5pWw5o2u5Lul5bqU55So5paw55qE57yp5pS+DQogICAgICAgICAgbW0uc2V0RGF0YShyb290LCB7DQogICAgICAgICAgICBpbml0aWFsU2NhbGU6IHNjYWxlLA0KICAgICAgICAgICAgaW5pdGlhbFBvc2l0aW9uOiBbDQogICAgICAgICAgICAgIChjb250YWluZXJXaWR0aCAtIHdpZHRoICogc2NhbGUpIC8gMiwNCiAgICAgICAgICAgICAgKGNvbnRhaW5lckhlaWdodCAtIGhlaWdodCAqIHNjYWxlKSAvIDIsDQogICAgICAgICAgICBdLA0KICAgICAgICAgIH0pOw0KICAgICAgICB9LCAxMDApOw0KDQogICAgICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlg0KICAgICAgICBjb25zdCByZXNpemVIYW5kbGVyID0gKCkgPT4gbW0uZml0KCk7DQogICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCJyZXNpemUiLCByZXNpemVIYW5kbGVyKTsNCg0KICAgICAgICAvLyDnu4Tku7bplIDmr4Hml7bmuIXnkIYNCiAgICAgICAgdGhpcy4kb25jZSgiaG9vazpiZWZvcmVEZXN0cm95IiwgKCkgPT4gew0KICAgICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCJyZXNpemUiLCByZXNpemVIYW5kbGVyKTsNCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCJNYXJrbWFwIHJlbmRlcmluZyBlcnJvcjoiLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaAnee7tOWvvOWbvua4suafk+Wksei0pSIpOw0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDmiZPlvIDmlrDmtaroiIbmg4XpgJoNCiAgICBvcGVuU2luYSgpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHRoaXMuc2luYVVybCwgIl9ibGFuayIpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["tabOne.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8kBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "tabOne.vue", "sourceRoot": "src/views/bigScreenSanhao", "sourcesContent": ["<template>\r\n  <div style=\"height: 100%; display: flex\" class=\"two\">\r\n    <div class=\"left\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">热点推荐</div>\r\n          <!-- <div class=\"bsContentTitleHelp\"></div> -->\r\n\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('MonitorUse?id=1')\"\r\n            style=\"right: 80px\"\r\n          >\r\n            更多\r\n          </div>\r\n          <div class=\"bsContentTitleMore\" @click=\"openSina\">舆情通</div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <div class=\"remengwenzhang-box\" :style=\"remengwenzhangBoxStyle\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper\"\r\n              @mouseenter=\"handleMouseEnter\"\r\n              @mouseleave=\"handleMouseLeave\"\r\n              @scroll=\"updateScrollbar\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView(item)\"\r\n                >\r\n                  <div\r\n                    class=\"block\"\r\n                    :style=\"{\r\n                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',\r\n                    }\"\r\n                  ></div>\r\n                  <div class=\"title\">{{ item.title }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"scroll-bar\"></div>\r\n          </div>\r\n\r\n          <!-- 文章通知组件 -->\r\n          <article-notification\r\n            ref=\"articleNotification\"\r\n            :articles=\"notificationArticles\"\r\n            :visible=\"showNotification\"\r\n            @close=\"handleNotificationClose\"\r\n            @view-article=\"handleViewArticle\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">政策风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getPolicyRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <usaMap\r\n            style=\"width: 516px; height: 247px\"\r\n            :external-data=\"usaMapData\"\r\n          ></usaMap>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 290px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">打压风险</div>\r\n          <div class=\"bsContentTitleHelp\" @click=\"getRiskDetail\"></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openNewTab('http://61.149.6.16:21001/bigScreen')\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"bsContentContent\">\r\n          <timeLine\r\n            :timelineEvents=\"suppressListData\"\r\n            @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n            style=\"width: 516px; height: 247px\"\r\n          ></timeLine>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <div class=\"center-top\">\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherTotal, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">有效采集量</div>\r\n        </div>\r\n        <div class=\"top-content\">\r\n          <div class=\"bg1\"></div>\r\n          <div class=\"top-content-number\">\r\n            {{ padWithZeros(gatherDayNumber, 6) }}\r\n          </div>\r\n          <div class=\"top-content-name\">当日采集数量</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"bsContentBox1\"\r\n        @mouseenter=\"handleMouseEnter2\"\r\n        @mouseleave=\"handleMouseLeave2\"\r\n      >\r\n        <div class=\"bsContentTitle1\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\" style=\"display: flex\">\r\n            <div\r\n              @click=\"zhikuActive = 0\"\r\n              :style=\"{ fontWeight: zhikuActive === 0 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              重点人物分析\r\n            </div>\r\n            <div style=\"margin: 0 4px\">/</div>\r\n            <div\r\n              @click=\"zhikuActive = 1\"\r\n              :style=\"{ fontWeight: zhikuActive === 1 ? '800' : '400' }\"\r\n              style=\"cursor: pointer\"\r\n            >\r\n              产业与技术专题分析\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"bsContentTitleHelp\" ></div> -->\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          style=\"display: flex; flex-direction: column; gap: 8px; padding: 8px\"\r\n        >\r\n          <!-- Tab 切换按钮 -->\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 0\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'trump' }\"\r\n              @click=\"switchTab('trump')\"\r\n            >\r\n              特朗普\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'msk' }\"\r\n              @click=\"switchTab('msk')\"\r\n            >\r\n              埃隆·里夫·马斯克\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'ws' }\"\r\n              @click=\"switchTab('ws')\"\r\n            >\r\n              詹姆斯·唐纳德·万斯\r\n            </div>\r\n          </div>\r\n          <div class=\"tab-buttons\" v-if=\"zhikuActive === 1\">\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'bdt' }\"\r\n              @click=\"switchTab('bdt', 7)\"\r\n            >\r\n              半导体领域\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'gdzb' }\"\r\n              @click=\"switchTab('gdzb', 8)\"\r\n            >\r\n              高端装备与材料\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'xnyqc' }\"\r\n              @click=\"switchTab('xnyqc', 9)\"\r\n            >\r\n              新能源汽车与电池\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'szhzx' }\"\r\n              @click=\"switchTab('szhzx', 10)\"\r\n            >\r\n              数字化转型与工业软件\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'lszz' }\"\r\n              @click=\"switchTab('lszz', 11)\"\r\n            >\r\n              绿色制造与新能源\r\n            </div>\r\n            <div\r\n              class=\"tab-button\"\r\n              :class=\"{ active: activeTab === 'swyy' }\"\r\n              @click=\"switchTab('swyy', 12)\"\r\n            >\r\n              生物医药与医疗器械\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 人物观点内容（特朗普/马斯克公用） -->\r\n          <div\r\n            v-show=\"\r\n              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <!-- 上方人物观点树状图 -->\r\n            <div class=\"trump-view-container\">\r\n              <trumpViewTree\r\n                ref=\"characterViewTree\"\r\n                @handleNodeClick=\"handleNodeClick\"\r\n                style=\"width: 100%; height: 300px\"\r\n                :move=\"isHovered2\"\r\n                :currentCharacter=\"activeTab\"\r\n              >\r\n              </trumpViewTree>\r\n            </div>\r\n            <div class=\"view-tree-container\">\r\n              <viewTree\r\n                :treeData=\"characterViewData\"\r\n                :title=\"'renwu'\"\r\n                :visible=\"\r\n                  activeTab === 'trump' ||\r\n                  activeTab === 'msk' ||\r\n                  activeTab === 'ws'\r\n                \"\r\n                style=\"width: 100%; height: 100%\"\r\n                @openNewView=\"openNewView1\"\r\n                @openbaarTreeEcharts=\"openbaarTreeEcharts()\"\r\n              ></viewTree>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中国制造短板分析内容 -->\r\n          <div\r\n            v-show=\"\r\n              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'\r\n            \"\r\n            class=\"tab-content\"\r\n          >\r\n            <svg ref=\"markmap\" class=\"markmap-svg\"></svg>\r\n          </div>\r\n\r\n          <div\r\n            style=\"\"\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"openbaarTreeEcharts()\"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right\">\r\n      <div class=\"bsContentBox\" style=\"width: 516px; height: 380px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">前沿科技动态</div>\r\n          <div class=\"bsContentTitleMore\" @click=\"qykjdtOpenNewTab()\">更多</div>\r\n        </div>\r\n        <div\r\n          class=\"bsContentContent\"\r\n          @mouseleave=\"handleMouseLeave1\"\r\n          @scroll=\"updateScrollbar1\"\r\n        >\r\n          <div class=\"kejidongtai-box\">\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '脑机接口' }\"\r\n              @click=\"handleButtonClick('脑机接口')\"\r\n            >\r\n              脑机接口\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '量子信息' }\"\r\n              @click=\"handleButtonClick('量子信息')\"\r\n            >\r\n              量子信息\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '人形机器人' }\"\r\n              @click=\"handleButtonClick('人形机器人')\"\r\n            >\r\n              人形机器人\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生成式人工智能' }\"\r\n              @click=\"handleButtonClick('生成式人工智能')\"\r\n            >\r\n              生成式人工智能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '生物制造' }\"\r\n              @click=\"handleButtonClick('生物制造')\"\r\n            >\r\n              生物制造\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来显示' }\"\r\n              @click=\"handleButtonClick('未来显示')\"\r\n            >\r\n              未来显示\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '未来网络' }\"\r\n              @click=\"handleButtonClick('未来网络')\"\r\n            >\r\n              未来网络\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '新型储能' }\"\r\n              @click=\"handleButtonClick('新型储能')\"\r\n            >\r\n              新型储能\r\n            </div>\r\n            <div\r\n              class=\"kejidongtai-button\"\r\n              :class=\"{ active: activeButton === '其他' }\"\r\n              @click=\"handleButtonClick('其他')\"\r\n            >\r\n              其他\r\n            </div>\r\n          </div>\r\n          <div class=\"remengwenzhang-box1\">\r\n            <div\r\n              class=\"scroll-wrapper\"\r\n              ref=\"scrollWrapper1\"\r\n              @mouseenter=\"handleMouseEnter1\"\r\n            >\r\n              <div class=\"scroll-content\" ref=\"scrollContent1\">\r\n                <div\r\n                  class=\"remengwenzhang-list\"\r\n                  v-for=\"(item, index) in remengwenzhangList1\"\r\n                  :key=\"index\"\r\n                  @click=\"openNewView1(item)\"\r\n                >\r\n                  <div class=\"block\"></div>\r\n                  <div class=\"title\">{{ item.cnTitle }}</div>\r\n                  <div class=\"sourceName\">{{ item.sourceName }}</div>\r\n                  <div class=\"time\">\r\n                    {{ parseTime(item.publishTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"scroll-bar\"></div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"bsContentBox2\" style=\"width: 516px; height: 600px\">\r\n        <div class=\"bsContentTitle\">\r\n          <div class=\"bsContentTitleIcon\"></div>\r\n          <div class=\"bsContentTitleName\">国内外前沿热点技术</div>\r\n          <div\r\n            class=\"bsContentTitleHelp\"\r\n            @click=\"comparisonChartShowModal = true\"\r\n          ></div>\r\n          <div\r\n            class=\"bsContentTitleMore\"\r\n            @click=\"\r\n              openNewTab(\r\n                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'\r\n              )\r\n            \"\r\n          >\r\n            更多\r\n          </div>\r\n        </div>\r\n        <div class=\"kejidongtai-box\">\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '11' }\"\r\n            @click=\"handleTechButtonClick('11', '新能源')\"\r\n          >\r\n            新能源\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '12' }\"\r\n            @click=\"handleTechButtonClick('12', '新材料')\"\r\n          >\r\n            新材料\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '13' }\"\r\n            @click=\"handleTechButtonClick('13', '高端装备')\"\r\n          >\r\n            高端装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '14' }\"\r\n            @click=\"handleTechButtonClick('14', '新能源汽车')\"\r\n          >\r\n            新能源汽车\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '17' }\"\r\n            @click=\"handleTechButtonClick('17', '船舶与海洋工程装备')\"\r\n          >\r\n            船舶与海洋工程装备\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '16' }\"\r\n            @click=\"handleTechButtonClick('16', '民用航空')\"\r\n          >\r\n            民用航空\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '15' }\"\r\n            @click=\"handleTechButtonClick('15', '绿色环保')\"\r\n          >\r\n            绿色环保\r\n          </div>\r\n          <div\r\n            class=\"kejidongtai-button\"\r\n            :class=\"{ active: activeTechButton === '18' }\"\r\n            @click=\"handleTechButtonClick('18', '新一代信息技术')\"\r\n          >\r\n            新一代信息技术\r\n          </div>\r\n        </div>\r\n        <!-- <div class=\"bsContentContent\" style=\"height: 188px\">\r\n          <technologyArticles\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openHotTechnology=\"openHotTechnology\"\r\n          ></technologyArticles>\r\n        </div> -->\r\n        <div class=\"bsContentContent\" style=\"padding-top: 0px; height: 450px\">\r\n          <graphEcharts\r\n            :sccenId=\"1\"\r\n            :screenSn=\"currentTechScreenSn\"\r\n            @openTechnologyDetails=\"openTechnologyDetails\"\r\n          ></graphEcharts>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <policyRisk\r\n      :visible=\"policyRiskShowModal\"\r\n      :list1=\"policyRiskList1\"\r\n      :list2=\"policyRiskList2\"\r\n      :total=\"policyRiskList1Total\"\r\n      :usa-map-data=\"usaMapData\"\r\n      title=\"美国相关提案\"\r\n      @update:visible=\"policyRiskShowModal = $event\"\r\n      @pagination=\"policyRiskPagination\"\r\n      @openArticleDetail=\"openArticleDetails('policyRisk-news', $event)\"\r\n    >\r\n    </policyRisk>\r\n    <suppressionOfRisks\r\n      :visible=\"suppressionOfRisksShowModal\"\r\n      :levelCount=\"riskBarChartData\"\r\n      :enterpriseList=\"riskEnterpriseList\"\r\n      :total=\"riskEnterpriseListTotal\"\r\n      title=\"打压风险\"\r\n      @update:visible=\"suppressionOfRisksShowModal = $event\"\r\n      @openEnterpriseInformation=\"openEnterpriseInformation\"\r\n      @pagination=\"riskEnterpriseListPagination\"\r\n    >\r\n    </suppressionOfRisks>\r\n    <technologyDetails\r\n      :visible=\"technologyDetailsShowModal\"\r\n      @update:visible=\"technologyDetailsShowModal = $event\"\r\n      @openArticleDetail=\"(e) => openArticleDetails('technology-article', e)\"\r\n      :title=\"technologyDetailsTitle\"\r\n      :item=\"technologyDetailsItem\"\r\n    ></technologyDetails>\r\n    <articleDetails\r\n      :visible=\"articleDetailsShowModal\"\r\n      :title=\"articleDetailsTitle\"\r\n      :content=\"articleDetailsContent\"\r\n      :contentEn=\"articleDetailsContentEn\"\r\n      :item=\"articleDetailsItem\"\r\n      @update:visible=\"articleDetailsShowModal = $event\"\r\n    >\r\n    </articleDetails>\r\n    <enterpriseInformation\r\n      :visible=\"enterpriseInformationShowModal\"\r\n      :title=\"enterpriseInformationTitle\"\r\n      :content=\"enterpriseInformationContent\"\r\n      :patentList=\"patentList\"\r\n      :softwareList=\"softwareList\"\r\n      :total1=\"patentTotal\"\r\n      :total2=\"softwareTotal\"\r\n      @update:visible=\"enterpriseInformationShowModal = $event\"\r\n      @pagination1=\"patentPagination\"\r\n      @pagination2=\"softwarePagination\"\r\n      @openArticleDetail=\"\r\n        (e) => openArticleDetails('enterpriseInformation-news', e)\r\n      \"\r\n    >\r\n    </enterpriseInformation>\r\n    <comparisonChart\r\n      :visible=\"comparisonChartShowModal\"\r\n      @update:visible=\"comparisonChartShowModal = $event\"\r\n      @openHotTechnology=\"openHotTechnology\"\r\n      title=\"前沿技术热点对比图详情\"\r\n    ></comparisonChart>\r\n    <hotTechnology\r\n      :visible=\"hotTechnologyShowModal\"\r\n      :title=\"hotTechnologytTitle\"\r\n      :id=\"hotTechnologytID\"\r\n      @update:visible=\"hotTechnologyShowModal = $event\"\r\n    ></hotTechnology>\r\n    <baarTreeEcharts\r\n      :visible=\"baarTreeEchartsShowModal\"\r\n      :type=\"baarTreeEchartsType\"\r\n      title=\"智库观点\"\r\n      @update:visible=\"baarTreeEchartsShowModal = $event\"\r\n      @openNewView=\"openNewView1\"\r\n    ></baarTreeEcharts>\r\n\r\n    <!-- 热点推荐文章详情弹窗 -->\r\n    <el-dialog\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"articleDialogVisible\"\r\n      width=\"65%\"\r\n      append-to-body\r\n      :before-close=\"handleClose\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"fz\">\r\n        <div class=\"text\">字号：</div>\r\n        <div class=\"btns\">\r\n          <div class=\"btn-minus\" @click=\"decreaseFontSize\">-</div>\r\n          <div class=\"font-size\">{{ fontSize }}px</div>\r\n          <div class=\"btn-plus\" @click=\"increaseFontSize\">+</div>\r\n        </div>\r\n      </div>\r\n      <div\r\n        class=\"dialog-art\"\r\n        :style=\"{ fontSize: fontSize + 'px' }\"\r\n        v-html=\"drawerInfo.cnContent\"\r\n      ></div>\r\n      <el-empty\r\n        description=\"当前文章暂无数据\"\r\n        v-if=\"!drawerInfo.cnContent\"\r\n      ></el-empty>\r\n    </el-dialog>\r\n    <markmap-dialog\r\n      :visible.sync=\"markmapVisible\"\r\n      :content=\"markmapContent\"\r\n      :title=\"markmapTitle\"\r\n      :loading=\"aiLoading\"\r\n      @close=\"handleMarkmapClose\"\r\n    />\r\n\r\n    <!-- 技术领域泡泡图弹窗 -->\r\n    <techBubbleDialog\r\n      :visible=\"techBubbleDialogVisible\"\r\n      :title=\"techBubbleDialogTitle\"\r\n      :screenSn=\"techBubbleDialogScreenSn\"\r\n      @update:visible=\"techBubbleDialogVisible = $event\"\r\n      @openTechnologyDetails=\"openTechnologyDetails\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport usaMap from \"./components/usaMap\";\r\nimport timeLine from \"./components/timeLine\";\r\nimport graphEcharts from \"./components/graphEcharts\";\r\nimport technologyArticles from \"./components/technologyArticles\";\r\nimport trumpViewTree from \"./components/trumpViewTree\";\r\nimport viewTree from \"./components/viewTree\";\r\nimport policyRisk from \"./secondLevel/policyRisk\";\r\nimport articleDetails from \"./secondLevel/articleDetails\";\r\nimport suppressionOfRisks from \"./secondLevel/suppressionOfRisks\";\r\nimport enterpriseInformation from \"./secondLevel/enterpriseInformation\";\r\nimport comparisonChart from \"./secondLevel/comparisonChart\";\r\nimport hotTechnology from \"./secondLevel/hotTechnology\";\r\nimport baarTreeEcharts from \"./components/baarTreeEcharts\";\r\nimport technologyDetails from \"./secondLevel/technologyDetails\";\r\nimport techBubbleDialog from \"./secondLevel/techBubbleDialog\";\r\nimport ArticleNotification from \"@/components/ArticleNotification\";\r\nimport {\r\n  technicalArticleDetail,\r\n  suppressData,\r\n  suppressLevelCount,\r\n  suppressEnterpriseList,\r\n  suppressPatentList,\r\n  suppressSoftwareList,\r\n  proposalsList,\r\n  proposalsToChinaData,\r\n  proposalsCount,\r\n  kjdtArticleList,\r\n  loginTCES,\r\n  loginSINA,\r\n} from \"@/api/bigScreen/sanhao.js\";\r\nimport {\r\n  largeHotQueryById,\r\n  largeGatherQueryGatherData,\r\n  getLargeFTT,\r\n  largeHotList2,\r\n} from \"@/api/bigScreen/index1\";\r\nimport { markObj } from \"./data/zhiku.js\";\r\nimport { treeData2, markdownData } from \"./data/renwu.js\";\r\nimport MarkmapDialog from \"../bigScreenThree/components/MarkmapDialog.vue\";\r\nimport {\r\n  containsHtmlTags,\r\n  extractHtmlTags,\r\n  hasValidHtmlStructure,\r\n} from \"@/utils/htmlUtils\";\r\nimport { Transformer } from \"markmap-lib\";\r\nimport { Markmap } from \"markmap-view\";\r\n\r\nexport default {\r\n  name: \"TabOne\",\r\n  components: {\r\n    usaMap,\r\n    timeLine,\r\n    graphEcharts,\r\n    technologyArticles,\r\n    trumpViewTree,\r\n    viewTree,\r\n    policyRisk,\r\n    articleDetails,\r\n    suppressionOfRisks,\r\n    enterpriseInformation,\r\n    comparisonChart,\r\n    hotTechnology,\r\n    baarTreeEcharts,\r\n    technologyDetails,\r\n    techBubbleDialog,\r\n    MarkmapDialog,\r\n    ArticleNotification,\r\n  },\r\n  props: {\r\n    notificationArticles: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    showNotification: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      policyRiskShowModal: false,\r\n      comparisonChartShowModal: false,\r\n      hotTechnologyShowModal: false,\r\n      hotTechnologytTitle: \"\",\r\n      hotTechnologytID: null,\r\n      baarTreeEchartsShowModal: false,\r\n      baarTreeEchartsType: null,\r\n      technologyDetailsShowModal: false,\r\n      technologyDetailsTitle: \"\",\r\n      technologyDetailsItem: null,\r\n      suppressionOfRisksShowModal: false,\r\n      enterpriseInformationShowModal: false,\r\n      enterpriseInformationTitle: \"\",\r\n      articleDetailsShowModal: false,\r\n      articleDetailsTitle: \"\",\r\n      articleDetailsContent: \"\",\r\n      articleDetailsContentEn: \"\",\r\n      suppressListData: [],\r\n      riskBarChartData: [],\r\n      riskEnterpriseList: [],\r\n      riskEnterpriseListTotal: 0,\r\n      enterpriseInformationContent: {},\r\n      patentList: [],\r\n      softwareList: [],\r\n      patentTotal: 0,\r\n      softwareTotal: 0,\r\n      policyRiskList1: [],\r\n      policyRiskList2: [],\r\n      policyRiskList1Total: 0,\r\n      // 美国地图数据\r\n      usaMapData: null,\r\n      articleDetailsItem: {},\r\n      // 热点推荐相关数据\r\n      remengwenzhangList: [],\r\n      scrollTimer: null,\r\n      scrollTimer1: null,\r\n      scrollTimer2: null,\r\n      isHovered: false,\r\n      isHovered1: false,\r\n      isHovered2: false,\r\n      scrollStep: 1,\r\n      drawerInfo: {},\r\n      articleDialogVisible: false,\r\n      fontSize: 16,\r\n      oriFontSize: 20,\r\n      // 人物观点数据\r\n      characterViewData: [],\r\n      // 智库观点数据\r\n      thinkTankViewData: [],\r\n      remengwenzhangList1: [],\r\n      activeButton: null,\r\n      qianyankejiList: [],\r\n      gatherTotal: 0,\r\n      gatherDayNumber: 0,\r\n      markmapVisible: false,\r\n      markmapContent: \"\",\r\n      markmapTitle: \"智库观点\",\r\n      aiLoading: false,\r\n      frontLoginParams: {\r\n        username: \"guanliyuan\",\r\n        password: \"123456\",\r\n      },\r\n      frontToken: \"\",\r\n      // 技术领域相关\r\n      activeTechButton: \"11\", // 默认选中新能源\r\n      currentTechScreenSn: \"11\", // 当前技术领域的screenSn\r\n      // 技术领域泡泡图弹窗相关\r\n      techBubbleDialogVisible: false,\r\n      techBubbleDialogTitle: \"\",\r\n      techBubbleDialogScreenSn: \"\",\r\n      // 智库观点tab切换\r\n      activeTab: \"trump\", // 默认显示特朗普tab\r\n      domainMarkdown: \"\",\r\n      sinaUrl: \"\",\r\n      zhikuActive: 0,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算热门文章列表框的样式\r\n    remengwenzhangBoxStyle() {\r\n      const notificationHeight = 110; // 通知组件的高度\r\n\r\n      if (this.showNotification) {\r\n        return {\r\n          height: `calc(100% - ${notificationHeight}px)`,\r\n        };\r\n      } else {\r\n        return {\r\n          height: `100%`,\r\n        };\r\n      }\r\n    },\r\n  },\r\n  mounted() {\r\n    // 调用登录TCES接口\r\n    loginTCES()\r\n      .then(() => {\r\n        console.log(\"TCES登录成功\");\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"TCES登录失败:\", error);\r\n      });\r\n\r\n    // 调用登录新浪接口\r\n    loginSINA()\r\n      .then((res) => {\r\n        console.log(\"新浪登录成功\");\r\n        this.sinaUrl = res;\r\n      })\r\n      .catch((error) => {\r\n        console.error(\"新浪登录失败:\", error);\r\n      });\r\n\r\n    this.getSuppressData();\r\n    this.initHotList();\r\n    this.initHotList1();\r\n    this.updateScrollbar();\r\n    this.updateScrollbar1();\r\n    this.fetchUsaMapData();\r\n    largeGatherQueryGatherData({}).then((res) => {\r\n      this.gatherTotal = res.data.gatherTotal;\r\n      this.gatherDayNumber = res.data.gatherDayNumber;\r\n    });\r\n  },\r\n  beforeDestroy() {\r\n    this.clearScrollTimer();\r\n    this.clearScrollTimer1();\r\n    this.clearScrollTimer2();\r\n    this.handleMarkmapClose();\r\n  },\r\n  methods: {\r\n    // 获取美国地图数据\r\n    async fetchUsaMapData() {\r\n      try {\r\n        const response = await proposalsCount({\r\n          projectSn: \"1\",\r\n          screenSn: \"1\",\r\n          columnSn: \"1\",\r\n        });\r\n        this.usaMapData = response;\r\n      } catch (error) {\r\n        console.error(\"获取美国地图数据失败:\", error);\r\n      }\r\n    },\r\n\r\n    openArticleDetails(type, item) {\r\n      this.articleDetailsItem = item;\r\n      switch (type) {\r\n        case \"technology-article\":\r\n          technicalArticleDetail({ id: item.id }).then((res) => {\r\n            this.articleDetailsTitle = item.title;\r\n            this.articleDetailsContent = res.data.content;\r\n            this.articleDetailsContentEn = res.data.enContent;\r\n            this.articleDetailsShowModal = true;\r\n          });\r\n          break;\r\n        case \"enterpriseInformation-news\":\r\n          this.openNewView(item);\r\n          break;\r\n        case \"policyRisk-news\":\r\n          this.openNewView(item);\r\n          break;\r\n      }\r\n    },\r\n    openEnterpriseInformation(item) {\r\n      suppressPatentList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n        this.patentTotal = res.total;\r\n      });\r\n      suppressSoftwareList({\r\n        suppressSn: item.suppressSn,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n        this.softwareTotal = res.total;\r\n      });\r\n      this.enterpriseInformationContent = { ...item };\r\n      this.enterpriseInformationTitle = item.enterpriseName;\r\n      this.enterpriseInformationShowModal = true;\r\n    },\r\n\r\n    patentPagination(suppressSn, queryParams) {\r\n      suppressPatentList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.patentList = res.rows;\r\n      });\r\n    },\r\n\r\n    softwarePagination(suppressSn, queryParams) {\r\n      suppressSoftwareList({\r\n        suppressSn: suppressSn,\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.softwareList = res.rows;\r\n      });\r\n    },\r\n\r\n    getSuppressData() {\r\n      suppressData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        let data = [];\r\n        Object.keys(res.data).forEach((key) => {\r\n          data.push({\r\n            date: key,\r\n            description:\r\n              res.data[key].length <= 3\r\n                ? res.data[key]\r\n                : res.data[key].slice(\r\n                    res.data[key].length - 3,\r\n                    res.data[key].length\r\n                  ),\r\n          });\r\n        });\r\n        this.suppressListData = data.reverse();\r\n      });\r\n    },\r\n\r\n    getRiskDetail() {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n        this.riskEnterpriseListTotal = res.total;\r\n      });\r\n      suppressLevelCount({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        // 将对象格式转换为数组格式\r\n        const data = Object.keys(res.data).map((year) => ({\r\n          product: year,\r\n          严重: res.data[year].严重,\r\n          一般: res.data[year].一般,\r\n          较轻: res.data[year].较轻,\r\n        }));\r\n        this.riskBarChartData = data;\r\n        this.suppressionOfRisksShowModal = true;\r\n      });\r\n    },\r\n\r\n    riskEnterpriseListPagination(queryParams) {\r\n      suppressEnterpriseList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.riskEnterpriseList = res.rows.map((item, index) => ({\r\n          ...item,\r\n          type: (index % 3) + 1,\r\n        }));\r\n      });\r\n    },\r\n\r\n    getPolicyRiskDetail() {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n        this.policyRiskList1Total = res.total;\r\n      });\r\n      proposalsToChinaData({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n      }).then((res) => {\r\n        this.policyRiskList2 = res.data;\r\n        this.policyRiskShowModal = true;\r\n      });\r\n    },\r\n\r\n    policyRiskPagination(queryParams) {\r\n      proposalsList({\r\n        projectSn: \"1\",\r\n        screenSn: \"1\",\r\n        columnSn: \"1\",\r\n        ...queryParams,\r\n      }).then((res) => {\r\n        this.policyRiskList1 = res.rows;\r\n      });\r\n    },\r\n\r\n    openHotTechnology(data) {\r\n      this.hotTechnologyShowModal = true;\r\n      this.hotTechnologytTitle = data.title;\r\n      this.hotTechnologytID = data.reportSn;\r\n    },\r\n    openbaarTreeEcharts(data) {\r\n      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\\D+/g, ''), 10)\r\n      // this.baarTreeEchartsShowModal = true;\r\n\r\n      // 根据当前选中的tab来决定使用哪个markObj\r\n      if (this.activeTab === \"trump\") {\r\n        // 使用导入的markdownData\r\n        this.markmapContent =\r\n          markdownData.trump[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"特朗普\";\r\n      } else if (this.activeTab === \"msk\") {\r\n        // 马斯克的markdownData\r\n        this.markmapContent =\r\n          markdownData.msk[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"埃隆·里夫·马斯克\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        // 万斯的markdownData\r\n        this.markmapContent =\r\n          markdownData.ws[this.$refs.characterViewTree.nodeType];\r\n        this.markmapTitle = \"詹姆斯·唐纳德·万斯\";\r\n      } else {\r\n        this.markmapContent = this.domainMarkdown;\r\n        switch (this.activeTab) {\r\n          case \"bdt\":\r\n            this.markmapTitle = \"半导体领域\";\r\n            break;\r\n          case \"gdzb\":\r\n            this.markmapTitle = \"高端装备与材料\";\r\n            break;\r\n          case \"xnyqc\":\r\n            this.markmapTitle = \"新能源汽车与电池\";\r\n            break;\r\n          case \"szhzx\":\r\n            this.markmapTitle = \"数字化转型与工业软件\";\r\n            break;\r\n          case \"lszz\":\r\n            this.markmapTitle = \"绿色制造与新能源\";\r\n            break;\r\n          case \"swyy\":\r\n            this.markmapTitle = \"生物医药与医疗器械\";\r\n            break;\r\n        }\r\n      }\r\n      this.aiLoading = false;\r\n      this.markmapVisible = true;\r\n    },\r\n\r\n    openTechnologyDetails(data) {\r\n      this.technologyDetailsShowModal = true;\r\n      this.technologyDetailsTitle = data.name;\r\n      this.technologyDetailsItem = data.data;\r\n    },\r\n\r\n    // 热点推荐相关方法\r\n    initHotList() {\r\n      // 使用bigScreenThree相同的API接口\r\n      largeHotList2()\r\n        .then((res) => {\r\n          this.remengwenzhangList = res.data || [];\r\n          this.$nextTick(() => {\r\n            this.startScroll();\r\n          });\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取热点推荐数据失败:\", error);\r\n          // 如果API调用失败，使用空数组\r\n          this.remengwenzhangList = [];\r\n        });\r\n    },\r\n    initHotList1() {\r\n      this.handleButtonClick(\"脑机接口\");\r\n      this.$nextTick(() => {\r\n        this.startScroll2();\r\n      });\r\n    },\r\n\r\n    startScroll() {\r\n      this.clearScrollTimer();\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      const content = this.$refs.scrollContent;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer = setInterval(() => {\r\n        if (this.isHovered) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar();\r\n      }, 40);\r\n    },\r\n\r\n    clearScrollTimer() {\r\n      if (this.scrollTimer) {\r\n        clearInterval(this.scrollTimer);\r\n        this.scrollTimer = null;\r\n      }\r\n    },\r\n\r\n    handleMouseEnter() {\r\n      this.isHovered = true;\r\n    },\r\n\r\n    handleMouseLeave() {\r\n      this.isHovered = false;\r\n      this.startScroll();\r\n    },\r\n    startScroll1() {\r\n      this.clearScrollTimer1();\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      const content = this.$refs.scrollContent1;\r\n\r\n      if (!wrapper || !content) return;\r\n\r\n      this.scrollTimer1 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {\r\n          wrapper.scrollTop = 0;\r\n        } else {\r\n          wrapper.scrollTop += this.scrollStep;\r\n        }\r\n        this.updateScrollbar1();\r\n      }, 20);\r\n    },\r\n    startScroll2() {\r\n      this.clearScrollTimer2();\r\n      this.scrollTimer2 = setInterval(() => {\r\n        if (this.isHovered1) return;\r\n\r\n        // 定义所有tab标签的顺序\r\n        const tabOrder = [\r\n          \"脑机接口\",\r\n          \"量子信息\",\r\n          \"人形机器人\",\r\n          \"生成式人工智能\",\r\n          \"生物制造\",\r\n          \"未来显示\",\r\n          \"未来网络\",\r\n          \"新型储能\",\r\n          \"其他\",\r\n        ];\r\n\r\n        // 找到当前活跃标签的索引\r\n        const currentIndex = tabOrder.indexOf(this.activeButton);\r\n        // 计算下一个标签的索引，如果到最后一个则回到第一个\r\n        const nextIndex = (currentIndex + 1) % tabOrder.length;\r\n        // 切换到下一个标签\r\n        this.handleButtonClick(tabOrder[nextIndex]);\r\n      }, 8000);\r\n    },\r\n    clearScrollTimer1() {\r\n      if (this.scrollTimer1) {\r\n        clearInterval(this.scrollTimer1);\r\n        this.scrollTimer1 = null;\r\n      }\r\n    },\r\n    clearScrollTimer2() {\r\n      if (this.scrollTimer2) {\r\n        clearInterval(this.scrollTimer2);\r\n        this.scrollTimer2 = null;\r\n      }\r\n    },\r\n    handleMouseEnter1() {\r\n      this.isHovered1 = true;\r\n    },\r\n\r\n    handleMouseLeave1() {\r\n      this.isHovered1 = false;\r\n      // this.startScroll1();\r\n      // this.startScroll2();\r\n    },\r\n    handleMouseEnter2() {\r\n      this.isHovered2 = true;\r\n    },\r\n\r\n    handleMouseLeave2() {\r\n      this.isHovered2 = false;\r\n    },\r\n    updateScrollbar() {\r\n      const wrapper = this.$refs.scrollWrapper;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n    updateScrollbar1() {\r\n      const wrapper = this.$refs.scrollWrapper1;\r\n      if (!wrapper) return;\r\n\r\n      const { scrollTop, scrollHeight, clientHeight } = wrapper;\r\n      const scrollPercent = clientHeight / scrollHeight;\r\n      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);\r\n      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;\r\n\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-height\",\r\n        `${scrollbarHeight}px`\r\n      );\r\n      document.documentElement.style.setProperty(\r\n        \"--scrollbar-top\",\r\n        `${scrollbarTop}px`\r\n      );\r\n    },\r\n\r\n    async openNewView(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await largeHotQueryById(item.id);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    async openNewView1(item) {\r\n      // 使用bigScreenThree相同的API接口\r\n      try {\r\n        const res = await getLargeFTT(item.sn);\r\n        this.drawerInfo = {\r\n          cnTitle:\r\n            item.cnTitle || item.title || res.data.title || res.data.cnTitle,\r\n          title:\r\n            item.title || item.cnTitle || res.data.title || res.data.cnTitle,\r\n          cnContent: res.data.content || res.data.cnContent,\r\n        };\r\n\r\n        // 处理内容格式\r\n        let content = this.formattingJson(this.drawerInfo.cnContent);\r\n        // if (content) {\r\n        //   content = content.replace(/\\n/g, \"<br>\");\r\n        //   content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n        //   content = content.replace(\"|xa0\", \"\");\r\n        //   content = content.replace(\"opacity: 0\", \"\");\r\n        //   content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n        //   content = content.replace(/ style=\"[^\"]*\"/g, \"\");\r\n        // }\r\n        this.drawerInfo.cnContent = content;\r\n\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      } catch (error) {\r\n        console.error(\"获取文章详情失败:\", error);\r\n        // 如果API调用失败，显示基本信息\r\n        this.drawerInfo = {\r\n          cnTitle: item.title || item.cnTitle,\r\n          title: item.title || item.cnTitle,\r\n          cnContent: \"暂无详细内容\",\r\n        };\r\n        this.articleDialogVisible = true;\r\n        this.oriFontSize = this.fontSize;\r\n      }\r\n    },\r\n    formattingJson(content) {\r\n      if (content) {\r\n        if (containsHtmlTags(content)) {\r\n          content = content.replace(/<br>/g, \"\");\r\n          content = content.replace(/\\n/g, \"\");\r\n          content = content.replace(/\\\\n/g, \"\");\r\n          content = content.replace(/\\\\\\n/g, \"\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          // content = content.replace(/\\${[^}]+}/g, \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          // 移除带样式的标签，保留内容\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n\r\n          console.log(\"包含的HTML标签\", extractHtmlTags(content));\r\n          console.log(\"HTML是否结构正确\", hasValidHtmlStructure(content));\r\n        } else {\r\n          content = content.replace(/\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\\\\\n/g, \"<br>\");\r\n          content = content.replace(/\\${[^}]+}/g, \"<br>\");\r\n          content = content.replace(\"|xa0\", \"\");\r\n          content = content.replace(\"opacity: 0\", \"\");\r\n          content = content.replace(/<img\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<figure\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<iframe\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<cnx\\b[^>]*>/gi, \"\");\r\n          content = content.replace(/<video\\b[^>]*>/gi, \"\");\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*style=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n          // 移除任何其他样式标签\r\n          content = content.replace(\r\n            /<(\\w+)[^>]*class=\"[^\"]*\"[^>]*>(.*?)<\\/\\1>/gi,\r\n            \"$2\"\r\n          );\r\n        }\r\n      }\r\n      return content;\r\n    },\r\n\r\n    handleClose() {\r\n      this.drawerInfo = {};\r\n      this.articleDialogVisible = false;\r\n    },\r\n\r\n    increaseFontSize() {\r\n      if (this.fontSize < 30) {\r\n        this.fontSize += 2;\r\n      }\r\n    },\r\n\r\n    decreaseFontSize() {\r\n      if (this.fontSize > 16) {\r\n        this.fontSize -= 2;\r\n      }\r\n    },\r\n    handleNodeClick(type) {\r\n      // 根据当前activeTab获取对应人物的数据\r\n      let currentCharacter = \"trump\"; // 默认特朗普\r\n      if (this.activeTab === \"msk\") {\r\n        currentCharacter = \"msk\";\r\n      } else if (this.activeTab === \"ws\") {\r\n        currentCharacter = \"ws\";\r\n      }\r\n      let rawData = JSON.parse(\r\n        JSON.stringify(treeData2[currentCharacter][type] || [])\r\n      );\r\n      this.characterViewData = this.limitLevel3Children(rawData);\r\n    },\r\n    limitLevel3Children(data) {\r\n      if (!data || !Array.isArray(data)) return data;\r\n      return data.map((item) => {\r\n        if (\r\n          (item.type == \"level2-1\" ||\r\n            item.type == \"level2-2\" ||\r\n            item.type == \"level2-3\") &&\r\n          Array.isArray(item.children)\r\n        ) {\r\n          item.children = item.children.slice(0, 2); // 只保留前两个\r\n        }\r\n\r\n        if (item.children) {\r\n          item.children = this.limitLevel3Children(item.children);\r\n        }\r\n\r\n        return item;\r\n      });\r\n    },\r\n    handleButtonClick(type) {\r\n      let obj = {\r\n        脑机接口: \"3\",\r\n        量子信息: \"4\",\r\n        人形机器人: \"6\",\r\n        生成式人工智能: \"1\",\r\n        生物制造: \"7\",\r\n        未来显示: \"8\",\r\n        未来网络: \"9\",\r\n        新型储能: \"10\",\r\n        其他: \"2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      this.activeButton = type;\r\n\r\n      // 重置轮播时间\r\n      this.startScroll2();\r\n\r\n      kjdtArticleList({\r\n        labelSn: obj[type],\r\n      }).then((res) => {\r\n        // 对数据进行去重处理，基于cnTitle去除空格后判断\r\n        const deduplicatedData = this.deduplicateArticles(res || []);\r\n        this.remengwenzhangList1 = deduplicatedData;\r\n        this.$nextTick(() => {\r\n          const wrapper = this.$refs.scrollWrapper1;\r\n          wrapper.scrollTop = 0;\r\n        });\r\n      });\r\n    },\r\n    qykjdtOpenNewTab() {\r\n      let obj = {\r\n        脑机接口: \"/qianyankejidongtai/naojijiekou?id=1&domain=3\",\r\n        量子信息: \"/qianyankejidongtai/liangzixinxi?id=1&domain=4\",\r\n        人形机器人: \"/qianyankejidongtai/renxingjiqiren?id=1&domain=6\",\r\n        生成式人工智能: \"/qianyankejidongtai/rengongzhineng?id=1&domain=1\",\r\n        生物制造: \"/qianyankejidongtai/shengwuzhizao?id=1&domain=7\",\r\n        未来显示: \"/qianyankejidongtai/weilaixianshi?id=1&domain=8\",\r\n        未来网络: \"/qianyankejidongtai/weilaiwangluo?id=1&domain=9\",\r\n        新型储能: \"/qianyankejidongtai/xinxingchuneng?id=1&domain=10\",\r\n        其他: \"/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17\",\r\n      };\r\n      window.open(obj[this.activeButton], \"_blank\");\r\n    },\r\n    // 文章去重方法，基于cnTitle去除空格后判断\r\n    deduplicateArticles(articles) {\r\n      if (!Array.isArray(articles)) {\r\n        return [];\r\n      }\r\n\r\n      const seen = new Set();\r\n      const result = [];\r\n\r\n      articles.forEach((article) => {\r\n        if (article && article.cnTitle) {\r\n          // 去除cnTitle中的所有空格\r\n          const normalizedTitle = article.cnTitle.replace(/\\s+/g, \"\");\r\n\r\n          if (!seen.has(normalizedTitle)) {\r\n            seen.add(normalizedTitle);\r\n            result.push(article);\r\n          }\r\n        } else {\r\n          // 如果没有cnTitle，也保留这条记录\r\n          result.push(article);\r\n        }\r\n      });\r\n\r\n      return result;\r\n    },\r\n    padWithZeros(num, targetLength) {\r\n      const numStr = num.toString();\r\n      const padding = \"0\".repeat(targetLength - numStr.length);\r\n      return `${padding}${numStr}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\r\n    },\r\n    openNewTab(url) {\r\n      window.open(url, \"_blank\");\r\n    },\r\n    handleMarkmapClose() {\r\n      this.markmapContent = \"\";\r\n      this.aiLoading = false;\r\n      this.markmapVisible = false;\r\n    },\r\n\r\n    // 更新热点推荐文章列表\r\n    async updateHotArticlesList() {\r\n      try {\r\n        const response = await largeHotList2();\r\n        if (response && response.data) {\r\n          const newArticles = response.data;\r\n          // 对比数据是否一致\r\n          if (this.isArticleDataChanged(newArticles)) {\r\n            this.remengwenzhangList = newArticles;\r\n          } else {\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"更新热点推荐文章列表失败:\", error);\r\n      }\r\n    },\r\n\r\n    // 检查文章数据是否发生变化\r\n    isArticleDataChanged(newArticles) {\r\n      // 如果当前列表为空，直接返回true\r\n      if (this.remengwenzhangList.length === 0) {\r\n        return newArticles.length > 0;\r\n      }\r\n\r\n      // 如果数量不同，说明有变化\r\n      if (this.remengwenzhangList.length !== newArticles.length) {\r\n        return true;\r\n      }\r\n\r\n      // 对比每篇文章的关键信息\r\n      for (let i = 0; i < newArticles.length; i++) {\r\n        const newArticle = newArticles[i];\r\n        const oldArticle = this.remengwenzhangList[i];\r\n\r\n        // 对比文章ID、标题、发布时间等关键字段\r\n        if (\r\n          newArticle.id !== oldArticle.id ||\r\n          newArticle.title !== oldArticle.title ||\r\n          newArticle.publishTime !== oldArticle.publishTime ||\r\n          newArticle.sourceName !== oldArticle.sourceName\r\n        ) {\r\n          return true;\r\n        }\r\n      }\r\n\r\n      // 所有数据都一致\r\n      return false;\r\n    },\r\n\r\n    // 处理技术领域按钮点击\r\n    handleTechButtonClick(screenSn, buttonName) {\r\n      console.log(\"切换技术领域:\", buttonName, \"screenSn:\", screenSn);\r\n      this.activeTechButton = screenSn;\r\n      this.currentTechScreenSn = screenSn;\r\n      // 弹出技术领域泡泡图弹窗\r\n      this.techBubbleDialogVisible = true;\r\n      this.techBubbleDialogTitle = buttonName;\r\n      this.techBubbleDialogScreenSn = screenSn;\r\n\r\n      // 通知子组件更新数据\r\n      this.$nextTick(() => {\r\n        // 可以通过ref直接调用子组件的方法来刷新数据\r\n        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新\r\n      });\r\n    },\r\n\r\n    // 关闭技术领域泡泡图弹窗\r\n    handleTechBubbleDialogClose() {\r\n      this.techBubbleDialogVisible = false;\r\n      this.techBubbleDialogTitle = \"\";\r\n      this.techBubbleDialogScreenSn = \"\";\r\n    },\r\n\r\n    // 处理通知关闭\r\n    handleNotificationClose() {\r\n      this.$emit(\"notification-close\");\r\n    },\r\n\r\n    // 处理查看单篇文章\r\n    handleViewArticle(article) {\r\n      this.$emit(\"notification-view-article\", article);\r\n    },\r\n\r\n    // 切换智库观点tab\r\n    switchTab(tabName, markdownType) {\r\n      if (markdownType) {\r\n        this.activeTab = tabName;\r\n        this.domainMarkdown = markObj[\"type\" + markdownType];\r\n        this.renderMarkmap();\r\n      } else if (tabName === \"trump\" || tabName === \"msk\" || tabName === \"ws\") {\r\n        // 如果点击的是当前已激活的人物tab，需要重新触发数据加载\r\n        if (this.activeTab === tabName) {\r\n          // 清空数据\r\n          this.characterViewData = [];\r\n          // 通过ref直接调用trumpViewTree组件的方法来重新初始化\r\n          this.$nextTick(() => {\r\n            if (\r\n              this.$refs.characterViewTree &&\r\n              this.$refs.characterViewTree.allTypes.length > 0\r\n            ) {\r\n              this.$refs.characterViewTree.handleNodeClick(\r\n                this.$refs.characterViewTree.allTypes[0]\r\n              );\r\n            }\r\n          });\r\n        } else {\r\n          // 切换到不同的人物tab时，设置activeTab，让watcher自动处理\r\n          this.activeTab = tabName;\r\n          this.characterViewData = [];\r\n        }\r\n      }\r\n    },\r\n    async renderMarkmap() {\r\n      if (!this.domainMarkdown) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      try {\r\n        await this.$nextTick();\r\n        const svg = this.$refs.markmap;\r\n        if (!svg) {\r\n          throw new Error(\"SVG element not found\");\r\n        }\r\n\r\n        // 清空之前的内容\r\n        svg.innerHTML = \"\";\r\n\r\n        // 处理内容，移除 markdown 标记\r\n        let processedContent = this.domainMarkdown\r\n          .replace(/^```markdown\\s*/i, \"\") // 移除开头的 ```markdown\r\n          .replace(/\\s*```\\s*$/, \"\"); // 移除结尾的 ```\r\n\r\n        const transformer = new Transformer();\r\n        const { root } = transformer.transform(processedContent);\r\n\r\n        // 创建思维导图\r\n        const mm = Markmap.create(\r\n          svg,\r\n          {\r\n            autoFit: true,\r\n            duration: 0,\r\n            nodeMinHeight: 20,\r\n            spacingVertical: 10,\r\n            spacingHorizontal: 100,\r\n            paddingX: 20,\r\n            color: (node) => {\r\n              const colors = {\r\n                0: \"#0052ff\", // 亮蓝色\r\n                1: \"#009600\", // 亮绿色\r\n                2: \"#ff6600\", // 亮橙色\r\n                3: \"#8000ff\", // 亮紫色\r\n                4: \"#ff0066\", // 亮粉色\r\n              };\r\n              return colors[node.depth] || \"#0052ff\";\r\n            },\r\n            nodeFont: (node) => {\r\n              const fonts = {\r\n                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto',\r\n              };\r\n              return (\r\n                fonts[node.depth] ||\r\n                '400 14px/1.5 -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto'\r\n              );\r\n            },\r\n            maxWidth: 400,\r\n            initialExpandLevel: -1,\r\n            zoom: true,\r\n            pan: true,\r\n            linkShape: \"diagonal\",\r\n            linkWidth: (node) => 2.5 - node.depth * 0.5,\r\n            linkColor: (node) => {\r\n              const colors = {\r\n                0: \"rgba(0, 82, 255, 0.8)\", // 亮蓝色\r\n                1: \"rgba(0, 150, 0, 0.8)\", // 亮绿色\r\n                2: \"rgba(255, 102, 0, 0.8)\", // 亮橙色\r\n              };\r\n              return colors[node.depth] || \"rgba(128, 0, 255, 0.8)\";\r\n            },\r\n          },\r\n          root\r\n        );\r\n\r\n        // 修改初始化动画部分\r\n        setTimeout(() => {\r\n          mm.fit(); // 适应视图大小\r\n\r\n          // 重新设置数据以触发重绘\r\n          const fitRatio = 0.95; // 留出一些边距\r\n          const { minX, maxX, minY, maxY } = mm.state;\r\n          const width = maxX - minX;\r\n          const height = maxY - minY;\r\n          const containerWidth = svg.clientWidth;\r\n          const containerHeight = svg.clientHeight;\r\n\r\n          // 计算合适的缩放比例\r\n          const scale = Math.min(\r\n            (containerWidth / width) * fitRatio,\r\n            (containerHeight / height) * fitRatio\r\n          );\r\n\r\n          // 更新数据以应用新的缩放\r\n          mm.setData(root, {\r\n            initialScale: scale,\r\n            initialPosition: [\r\n              (containerWidth - width * scale) / 2,\r\n              (containerHeight - height * scale) / 2,\r\n            ],\r\n          });\r\n        }, 100);\r\n\r\n        // 监听窗口大小变化\r\n        const resizeHandler = () => mm.fit();\r\n        window.addEventListener(\"resize\", resizeHandler);\r\n\r\n        // 组件销毁时清理\r\n        this.$once(\"hook:beforeDestroy\", () => {\r\n          window.removeEventListener(\"resize\", resizeHandler);\r\n        });\r\n      } catch (error) {\r\n        console.error(\"Markmap rendering error:\", error);\r\n        this.$message.error(\"思维导图渲染失败\");\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    // 打开新浪舆情通\r\n    openSina() {\r\n      window.open(this.sinaUrl, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.two {\r\n  height: 100%;\r\n  width: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .left {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .center {\r\n    margin: 0 11px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .center-top {\r\n      height: 150px;\r\n      display: flex;\r\n      justify-content: space-around;\r\n      align-items: center;\r\n\r\n      .top-content {\r\n        position: relative;\r\n        width: 315px;\r\n        height: 98px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg1.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bg1 {\r\n        position: absolute;\r\n        top: 17px;\r\n        left: 43px;\r\n        width: 60px;\r\n        height: 60px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/centerBg2.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .top-content-number {\r\n        font-size: 24px;\r\n        color: #00e5ff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 44px;\r\n      }\r\n\r\n      .top-content-name {\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        position: absolute;\r\n        left: 138px;\r\n        top: 22px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .right {\r\n    width: 520px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-between;\r\n\r\n    .bsContentBox2 {\r\n      background-image: url(\"../../assets/bigScreenSanhao/contentBg1.png\");\r\n\r\n      .bsContentContent {\r\n        height: calc((100% - 43px) / 2);\r\n      }\r\n\r\n      .kejidongtai-box {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .bsContentBox,\r\n  .bsContentBox2 {\r\n    background-image: url(\"../../assets/bigScreenSanhao/contentBg.png\");\r\n    background-size: 100% 100%;\r\n\r\n    .bsContentTitle {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n    }\r\n  }\r\n\r\n  .bsContentBox1 {\r\n    flex: 1;\r\n\r\n    .bsContentTitle1 {\r\n      height: 43px;\r\n      display: flex;\r\n      align-items: center;\r\n      padding-left: 10px;\r\n      background-image: url(\"../../assets/bigScreenSanhao/title1.png\");\r\n      background-size: 100% 100%;\r\n      position: relative;\r\n\r\n      .bsContentTitleIcon {\r\n        width: 22px;\r\n        height: 22px;\r\n        margin-right: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleLogo.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleName {\r\n        height: 43px;\r\n        line-height: 43px;\r\n        font-weight: 800;\r\n        font-size: 20px;\r\n        color: #00abf4;\r\n\r\n        span {\r\n          font-weight: normal;\r\n          cursor: pointer;\r\n          color: rgba(0, 171, 244, 0.5);\r\n        }\r\n\r\n        .titleColor {\r\n          font-weight: 800;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleHelp {\r\n        cursor: pointer;\r\n        width: 21px;\r\n        height: 21px;\r\n        margin-left: 10px;\r\n        background-image: url(\"../../assets/bigScreenSanhao/titleHelp.png\");\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        right: 10px;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .bsContentContent {\r\n      height: calc(100% - 43px);\r\n      display: flex;\r\n      flex-direction: column;\r\n      position: relative;\r\n\r\n      .trump-view-container {\r\n        height: 300px;\r\n      }\r\n\r\n      .view-tree-container {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        & > div {\r\n          flex: 1;\r\n        }\r\n      }\r\n\r\n      .bsContentTitleMore {\r\n        position: absolute;\r\n        bottom: 0;\r\n        right: 0;\r\n        z-index: 99;\r\n        cursor: pointer;\r\n        width: 70px;\r\n        height: 43px;\r\n        line-height: 60px;\r\n        font-size: 17px;\r\n        color: #00c8ff;\r\n\r\n        &:after {\r\n          content: \"\";\r\n          display: inline-block;\r\n          position: absolute;\r\n          top: 22px;\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-left: 5px;\r\n          background-image: url(\"../../assets/bigScreenSanhao/jiantou.png\");\r\n          background-size: 100% 100%;\r\n        }\r\n      }\r\n\r\n      // Tab 按钮样式\r\n      .tab-buttons {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .tab-button {\r\n          padding: 8px 8px;\r\n          background: rgba(0, 171, 244, 0.2);\r\n          border: 1px solid rgba(0, 171, 244, 0.5);\r\n          border-radius: 4px;\r\n          color: rgba(0, 171, 244, 0.8);\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 171, 244, 0.3);\r\n            color: #00abf4;\r\n          }\r\n\r\n          &.active {\r\n            background: rgba(0, 171, 244, 0.5);\r\n            color: #ffffff;\r\n            border-color: #00abf4;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Tab 内容样式\r\n      .tab-content {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .trump-view-container {\r\n          height: 300px;\r\n        }\r\n\r\n        .view-tree-container {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          & > div {\r\n            flex: 1;\r\n          }\r\n        }\r\n\r\n        .markmap-svg {\r\n          width: 100%;\r\n          height: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 热点推荐滚动列表样式\r\n  .remengwenzhang-box {\r\n    width: 100%;\r\n    height: 100%;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remengwenzhang-box1 {\r\n    width: 100%;\r\n    height: 230px;\r\n    padding: 20px;\r\n    border: 1px solid rgba(16, 216, 255, 0.4);\r\n    background: rgba(0, 0, 0, 0.15);\r\n    box-shadow: 0px 0px 8px 0px #0056ad;\r\n    overflow: hidden;\r\n    position: relative;\r\n\r\n    .scroll-wrapper {\r\n      height: 100%;\r\n      overflow-y: scroll;\r\n      overflow-x: hidden;\r\n      position: relative;\r\n\r\n      scrollbar-width: none;\r\n      -ms-overflow-style: none;\r\n\r\n      &::-webkit-scrollbar {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      height: calc(100% - 40px);\r\n      width: 6px;\r\n      background: rgba(16, 216, 255, 0.1);\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      pointer-events: none;\r\n    }\r\n\r\n    .scroll-bar {\r\n      position: absolute;\r\n      top: 20px;\r\n      right: 0;\r\n      width: 6px;\r\n      height: var(--scrollbar-height, 100px);\r\n      background: rgba(16, 216, 255, 0.4);\r\n      border-radius: 3px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n      transform: translateY(var(--scrollbar-top, 0));\r\n      pointer-events: none;\r\n    }\r\n\r\n    &:hover {\r\n      &::after,\r\n      .scroll-bar {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    .remengwenzhang-list {\r\n      position: relative;\r\n      height: 40px;\r\n      padding-left: 20px;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        width: 330px;\r\n        overflow: hidden;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 700;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .time,\r\n      .sourceName {\r\n        width: 150px;\r\n        color: rgba(216, 240, 255, 0.8);\r\n        text-align: right;\r\n        font-family: \"Source Han Sans CN\";\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        line-height: 20px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .block {\r\n        position: absolute;\r\n        left: 0px;\r\n        top: 6px;\r\n        width: 10px;\r\n        height: 10px;\r\n        border-radius: 1px;\r\n        background: #1bdcff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 弹窗样式\r\n::v-deep .el-dialog {\r\n  background: url(\"../../assets/bigScreenTwo/dialogBackground.png\") no-repeat;\r\n  background-size: 100% 100% !important;\r\n  background-size: cover;\r\n  height: 800px;\r\n\r\n  .el-dialog__header {\r\n    background-color: #1d233400;\r\n    font-size: 30px;\r\n    color: #ffffff;\r\n    line-height: 100px;\r\n    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);\r\n    height: 100px;\r\n\r\n    .el-dialog__title {\r\n      display: inline-block;\r\n      width: calc(100% - 100px);\r\n      white-space: nowrap;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    background-color: #2a304000;\r\n    color: #f2f2f2;\r\n    height: calc(100% - 140px);\r\n    overflow: hidden;\r\n    padding: 20px 30px;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    background-color: #1d233400;\r\n    padding: 18px 20px;\r\n  }\r\n\r\n  .el-button {\r\n    background-color: #002766;\r\n    color: #fff;\r\n    border: 0px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    background: url(\"../../assets/bigScreenTwo/关闭小.png\") no-repeat;\r\n    background-size: 100% 100% !important;\r\n    background-size: cover;\r\n    width: 31px;\r\n    height: 31px;\r\n    top: 16px;\r\n\r\n    &::before {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n.dialog-art {\r\n  background: #1d293b;\r\n  padding: 20px;\r\n  height: 590px;\r\n  overflow-y: auto;\r\n  line-height: 1.8em;\r\n  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,\r\n    sans-serif;\r\n\r\n  ::v-deep p {\r\n    text-indent: 2em;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n::v-deep .dialog-art::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n\r\n.fz {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  margin-bottom: 20px;\r\n\r\n  .text {\r\n    font-weight: 400;\r\n    font-size: 20px;\r\n    color: #ffffff;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #1d293b;\r\n    border-radius: 14px;\r\n    padding: 0 10px;\r\n    height: 28px;\r\n\r\n    .btn-minus,\r\n    .btn-plus {\r\n      width: 24px;\r\n      height: 24px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      font-size: 20px;\r\n      color: #ffffff;\r\n\r\n      &:hover {\r\n        color: #2f7cfe;\r\n      }\r\n    }\r\n\r\n    .font-size {\r\n      margin: 0 15px;\r\n      color: #ffffff;\r\n      font-size: 16px;\r\n      min-width: 45px;\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n\r\n.kejidongtai-box {\r\n  width: 100%;\r\n  // height: 45px;\r\n  // padding-top: 11px;\r\n  // margin: 3px 0;\r\n  display: flex;\r\n  // justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  column-gap: 10px;\r\n  row-gap: 10px;\r\n  padding: 10px;\r\n  margin-top: 10px;\r\n\r\n  .kejidongtai-button {\r\n    // width: 111px;\r\n    height: 33px;\r\n    line-height: 33px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    color: #ffffff;\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan1.png\");\r\n    background-size: 100% 100%;\r\n    padding: 0 12px;\r\n  }\r\n\r\n  .active {\r\n    background-image: url(\"../../assets/bigScreenSanhao/kejiqianyan2.png\");\r\n  }\r\n}\r\n\r\n:deep(.markmap-node) {\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n:deep(.markmap-node-circle) {\r\n  fill: transparent; // 修改节点背景为透明\r\n  stroke-width: 2px;\r\n}\r\n\r\n:deep(.markmap-node-text) {\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto,\r\n    \"Helvetica Neue\", Arial;\r\n\r\n  tspan {\r\n    fill: #333 !important; // 修改文字颜色为深色\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n:deep(.markmap-link) {\r\n  fill: none;\r\n  stroke-width: 2.5px; // 加粗连线\r\n}\r\n\r\n// 根节点样式\r\n:deep(.markmap-node[data-depth=\"0\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #0052ff; // 亮蓝色\r\n    stroke-width: 3px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 20px !important;\r\n    font-weight: bold !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 二级节点样式\r\n:deep(.markmap-node[data-depth=\"1\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #009600; // 亮绿色\r\n    stroke-width: 2.5px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 18px !important;\r\n    font-weight: 600 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 三级节点样式\r\n:deep(.markmap-node[data-depth=\"2\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #ff6600; // 亮橙色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 16px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n\r\n// 其他层级节点样式\r\n:deep(.markmap-node[data-depth=\"3\"]),\r\n:deep(.markmap-node[data-depth=\"4\"]) {\r\n  .markmap-node-circle {\r\n    stroke: #8000ff; // 亮紫色\r\n    stroke-width: 2px;\r\n  }\r\n\r\n  .markmap-node-text tspan {\r\n    font-size: 14px !important;\r\n    font-weight: 500 !important;\r\n    fill: #333 !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}