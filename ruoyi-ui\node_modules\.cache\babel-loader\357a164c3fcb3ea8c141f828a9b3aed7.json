{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\MainArticle.vue", "mtime": 1753846364235}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_request", "_auth", "_elementUi", "_axios", "_classify", "_fileSaver", "_ruoyi", "_list", "_DeepseekReportDialog", "_ai", "_marked", "_config", "_articleHistory", "props", "downLoadShow", "required", "type", "Boolean", "default", "editShow", "copyShow", "<PERSON>u<PERSON><PERSON>", "height", "Number", "currentPage", "pageSize", "total", "ArticleList", "flag", "SeachData", "keywords", "String", "sourceType", "components", "DeepseekReportDialog", "data", "loading", "regExpImg", "reportId", "reportOptions", "dialogVisible", "checkedCities", "checked", "html", "text", "that", "tagShow", "isIndeterminate", "count", "separate", "tagDialog", "formLabelAlign", "tag", "industry", "domain", "options", "options1", "tagItem", "areaList", "num", "timer", "drawer", "drawerInfo", "AreaId", "translationBtnShow", "open", "sourceTypeList", "sourceLists", "sourceTypeLists", "form", "rules", "title", "message", "content", "publishTime", "cnTitle", "originalUrl", "summary", "sn", "vertifyUpload", "isUploading", "headers", "Authorization", "getToken", "ContentType", "url", "process", "env", "VUE_APP_BASE_API", "fileList", "fileUrlList", "fileUrlurl", "showSummary", "batchImportVisible", "batchImportFiles", "showDeepseekDialog", "currentArticle", "aiDialogVisible", "chatMessages", "isThinking", "userAvatar", "streamingMessage", "markdownOptions", "gfm", "breaks", "headerIds", "mangle", "headerPrefix", "pedantic", "sanitize", "smartLists", "smartypants", "xhtml", "isRequesting", "isAborted", "currentReader", "aiPlatform", "articleAiPrompt", "computed", "watch", "newVal", "oldVal", "_this", "API", "getNewBuilt", "then", "code", "$message", "handler", "Refresh", "deep", "filter", "item", "mounted", "created", "_this2", "openDialog", "getListClassify", "res", "getSourceList", "$route", "query", "getConfigKey", "msg", "$store", "getters", "avatar", "updated", "filters", "methods", "formatPublishTime", "webstePublishTime", "formattedPublishTime", "parseTime", "formattedWebsteTime", "includes", "dateMatch", "match", "year", "month", "day", "concat", "padStart", "has<PERSON><PERSON>ual<PERSON><PERSON>nt", "contentWithoutTags", "replace", "test", "changeColor", "str", "Str", "split", "map", "keyitem", "keyindex", "length", "replaceReg", "RegExp", "replaceString", "downLoadExcel", "_this3", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "response", "a", "document", "createElement", "href", "window", "URL", "createObjectURL", "download", "Date", "getTime", "click", "downLoadExportExcel", "stop", "batchDelete", "_this4", "$confirm", "batchRemove", "join", "$emit", "catch", "publishHot", "_this5", "publishEverydayHot", "mainScorll", "_this6", "scrollStep", "$refs", "scroll", "scrollTop", "scrollInterval", "setInterval", "scrollBy", "clearInterval", "scrollChange", "_this7", "clearTimeout", "setTimeout", "stopScroll", "downLoad", "handleSizeChange", "handleCurrentChange", "current", "collect", "_this8", "_callee2", "_res", "_callee2$", "_context2", "id", "favorites", "collectApi", "sent", "cocelCollect", "copyText", "_this9", "navigator", "clipboard", "writeText", "alert", "typeHandle", "handleCheckedCitiesChange", "value", "handleCheckAllChange", "val", "reportSubmit", "_this10", "_callee3", "keyWordList", "_callee3$", "_context3", "push", "listId", "listSn", "for<PERSON>ach", "article", "AddReport", "separateAdd", "openNewView", "isLink", "docId", "<PERSON><PERSON><PERSON><PERSON>", "tags", "_this11", "_callee4", "_callee4$", "_context4", "remoteEvent", "fieldName", "remoteIndustry", "industryName", "SubmitTag", "_this12", "_callee5", "params", "_callee5$", "_context5", "articleId", "tagAdd", "closeTag", "resetFields", "hotIncrease", "_this13", "_callee6", "is<PERSON><PERSON>ther", "_callee6$", "_context6", "JSON", "parse", "openDrawer", "_this14", "_callee7", "_callee7$", "_context7", "AreaInfo", "cnC<PERSON>nt", "b", "c", "industryHandle", "_this15", "ids", "ele", "undefined", "domainHandle", "_this16", "resultEvent", "_this17", "warning", "zhuangtai", "snapshotUrl", "downLoadExportKe", "$msgbox", "_defineProperty2", "showCancelButton", "confirmButtonText", "cancelButtonText", "beforeClose", "action", "instance", "done", "err", "downLoadExportZhuan", "location", "origin", "documentDownload", "_this18", "_callee9", "urls", "_iterator", "_step", "_loop", "_callee9$", "_context10", "fileUrl", "_createForOfIteratorHelper2", "entries", "_step$value", "index", "_loop$", "_context9", "_slicedToArray2", "indexOf", "_callee8", "_callee8$", "_context8", "downLoadFun", "error", "s", "n", "<PERSON><PERSON><PERSON>", "t1", "e", "f", "finish", "_this19", "_callee10", "formData", "isBlob", "blob", "list", "fileName", "resText", "rspObj", "errMsg", "_callee10$", "_context11", "FormData", "append", "downloadFile", "blobValidate", "Blob", "saveAs", "errorCode", "t0", "translateTitle", "row", "_this20", "$loading", "lock", "spinner", "background", "translationTitle", "originalText", "translationField", "translationType", "findIndex", "isTranslated", "close", "translateEvent", "_this21", "handleUpdate", "_this22", "reset", "handleDelete", "_this23", "$modal", "confirm", "monitoringEsRemove", "msgSuccess", "submitForm", "_this24", "validate", "valid", "queryForm", "stringify", "articleListEdit", "requestLoad", "file", "_this25", "_callee11", "_callee11$", "_context12", "uploadCover", "uid", "path", "imgUrl", "exceed", "handleRemove", "handleChange", "upload", "submit", "beforeUploadUrl", "name", "substring", "lastIndexOf", "toLowerCase", "condition", "fileSize", "size", "$notify", "uploadUrlSuccess", "uploadUrlExceed", "uploadUrlRequest", "_this26", "uploadFile", "uploadUrlRemove", "_this27", "removeFile", "filePath", "cancel", "articleSn", "sourceName", "sourceSn", "shortUrl", "author", "description", "cnSummary", "cover", "publishType", "publishCode", "publishArea", "numberLikes", "numberReads", "numberCollects", "numberShares", "numberComments", "emotion", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "userId", "deptId", "tmpUrl", "is<PERSON><PERSON><PERSON>", "groupId", "appId", "resetForm", "openBatchImportDialog", "handleFileSelect", "newFiles", "raw", "splice", "cancelBatchImport", "batchUpload", "clearFiles", "confirmBatchImport", "_this28", "_callee12", "emptySourceNames", "sourceNames", "_callee12$", "_context13", "trim", "batchImportReports", "console", "reportAiChat", "selectedArticleId", "selectedArticle", "find", "difyAiChat", "_this29", "_callee13", "_articlesResponse$dat", "selectedArticles", "titles", "articlesResponse", "articlesContent", "aiMessage", "prompt", "reader", "decoder", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "isInThinkTag", "decodeUnicode", "updateContent", "_yield$reader$read", "lastData", "decodedAnswer", "chunk", "newlineIndex", "line", "jsonData", "answer", "_callee13$", "_context14", "log", "Promise", "resolve", "getListByIds", "Error", "_selectedArticles$ind", "_selectedArticles$ind2", "role", "difyAiQa", "ok", "body", "<PERSON><PERSON><PERSON><PERSON>", "TextDecoder", "fromCharCode", "parseInt", "newContent", "renderedContent", "marked", "$nextTick", "scrollHeight", "read", "warn", "decode", "slice", "startsWith", "t2", "ollamaAiChat", "_this30", "_callee15", "_articlesResponse$dat2", "_aiMessage", "lastUpdateTime", "isThinkContent", "temp<PERSON><PERSON><PERSON>", "processStream", "_callee15$", "_context16", "_selectedArticles$ind3", "_selectedArticles$ind4", "ollamaAiQa", "now", "currentTime", "_ref2", "_callee14", "_yield$reader$read2", "lines", "_iterator2", "_step2", "_response", "thinkStartIndex", "thinkEndIndex", "_callee14$", "_context15", "apply", "arguments", "deepseekAiChat", "_this31", "_callee16", "_aiMessage2", "_lastUpdateTime", "_yield$reader$read3", "_iterator3", "_step3", "_jsonData$choices", "_callee16$", "_context17", "_selectedArticles$ind5", "_selectedArticles$ind6", "deepseekAiQa", "choices", "delta", "t3", "t4", "closeAiDialog", "articleAiChat"], "sources": ["src/views/components/MainArticle.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Monitor'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    >\r\n                    </span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ item.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i title=\"批量生成快照\" class=\"icon-pingmukuaizhao\" style=\"color:green\" @click=\"resultEvent('BatchGeneration')\"></i>\r\n          </p> -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-document-add\"\r\n              style=\"font-size: 24px\"\r\n              title=\"发布到每日最新热点\"\r\n              @click=\"publishHot\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\" v-if=\"$route.query.domain\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek深度解读\"\r\n              @click=\"articleAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"articleAiChat\"\r\n              >Deepseek深度解读</span\r\n            >\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          :style=\"\r\n            $route.query.domain\r\n              ? 'height: calc(100vh - 170px)'\r\n              : 'height: calc(100vh - 389px)'\r\n          \"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ $route.query.domain ? \"是\" : item.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  plain\r\n                  @click=\"handleUpdate(item)\"\r\n                  v-hasPermi=\"['article:articleList:edit']\"\r\n                  >{{ ` 修改 ` }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  v-hasPermi=\"['article:list:remove']\"\r\n                  >{{ `删除` }}</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Special'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(100vh - 405px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div\r\n                  class=\"ArticlBottom\"\r\n                  v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <div>\r\n                    原文标题:\r\n                    <span class=\"infomation\">\r\n                      {{ item.title }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ item.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n                <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'infoInter'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                    ></span\r\n                    >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                  >\r\n                </p>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    类型:\r\n                    <span class=\"infomation\">\r\n                      {{ typeHandle(item.sourceType) }}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.author\">\r\n                    作者:\r\n                    <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                  <!-- <p>标签A</p> -->\r\n                </div>\r\n                <div class=\"ArticlBottom\">\r\n                  <div v-if=\"item.industryName\">\r\n                    所属行业:\r\n                    <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                  </div>\r\n                  <div v-if=\"item.domain\">\r\n                    所属领域:\r\n                    <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                  </div>\r\n                  <div>\r\n                    原文链接:\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3\"\r\n                    >\r\n                      {{\r\n                        typeHandle(item.sourceType) == \"微信公众号\"\r\n                          ? item.shortUrl\r\n                          : item.originalUrl\r\n                      }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n              <!-- <div class=\"imgBox\" v-if=\"item.imgage\">\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n              <img src=\"../../assets/images/priview.png\" alt=\"加载失败了\" />\r\n            </div> -->\r\n            </div>\r\n            <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'Wechat'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <!-- <p class=\"toolTitle\"><i title=\"下载\" class=\"icon-xiazai1\" style=\"color:green\" @click=\"downLoad\"></i></p>  2023-9-4 沈老师  暂时注释 -->\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"false\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"true\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n          style=\"height: calc(50vh - 200px)\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_kqcb\">\r\n              <div>\r\n                <div class=\"ArticlTop\" style=\"width: 55%\">\r\n                  <p style=\"line-height: 20px\">\r\n                    <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                      {{ null }} </el-checkbox\r\n                    >&nbsp;&nbsp;\r\n                    <a href=\"#\"\r\n                      ><span style=\"padding: 0 10px 0 0\"\r\n                        >{{\r\n                          (currentPage - 1) * pageSize + key + 1\r\n                        }}&nbsp;</span\r\n                      ><span\r\n                        class=\"title_Article\"\r\n                        @click=\"openNewView(item)\"\r\n                        v-html=\"changeColor(item.cnTitle || item.title)\"\r\n                      ></span\r\n                      >&nbsp;&nbsp;&nbsp;&nbsp;</a\r\n                    >\r\n                    <span\r\n                      class=\"linkStyle\"\r\n                      @click=\"openNewView(item, 'link')\"\r\n                      style=\"color: #1889f3; cursor: pointer\"\r\n                    >\r\n                      原文链接\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                <div class=\"info_flex\" style=\"width: auto\">\r\n                  <div class=\"ArticlBottom\">\r\n                    <div>\r\n                      类型:\r\n                      <span class=\"infomation\">\r\n                        {{ typeHandle(item.sourceType) }}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      媒体来源:\r\n                      <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.author\">\r\n                      作者:\r\n                      <span class=\"infomation\" v-html=\"item.author\"></span>\r\n                    </div>\r\n                    <div>\r\n                      发布时间:\r\n                      <span class=\"infomation\">{{\r\n                        formatPublishTime(\r\n                          item.publishTime,\r\n                          item.webstePublishTime\r\n                        )\r\n                      }}</span>\r\n                    </div>\r\n                    <!-- <span style=\"color: rgb(32, 126, 250);\">相似文章</span> -->\r\n                    <!-- <p>标签A</p> -->\r\n                  </div>\r\n                  <div class=\"ArticlBottom\">\r\n                    <div v-if=\"item.industryName\">\r\n                      所属行业:\r\n                      <span class=\"infomation\">{{ industryHandle(item) }}</span>\r\n                    </div>\r\n                    <div v-if=\"item.domain\">\r\n                      所属领域:\r\n                      <span class=\"infomation\">{{ domainHandle(item) }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <!-- <div class=\"leftBtnGroup2\">\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-hot2-copy':\r\n                      item.isWhether == null || item.isWhether == 0,\r\n                    'icon-hot2': item.isWhether == 1,\r\n                  }\"\r\n                  title=\"热点\"\r\n                  @click=\"hotIncrease(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  :class=\"{\r\n                    'icon-jiaxingshoucangtianchong-copy': !item.favorites,\r\n                    'icon-jiaxingshoucangtianchong': item.favorites,\r\n                  }\"\r\n                  title=\"收藏\"\r\n                  @click.passive=\"collect(item)\"\r\n                  v-hasPermi=\"['article:collection:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon--_tianjiadaoku\"\r\n                  title=\"添加到报告\"\r\n                  @click=\"separateAdd(item)\"\r\n                  v-hasPermi=\"['result:report:add']\"\r\n                ></i>\r\n              </p>\r\n              <p>\r\n                <i\r\n                  class=\"icon-biaoqian\"\r\n                  title=\"标签\"\r\n                  @click=\"tagHandler(item)\"\r\n                  v-hasPermi=\"['article:label:add']\"\r\n                ></i>\r\n              </p> -->\r\n            <!-- <p><i class=\"icon-fuzhi\" title=\"复制\" @click=\"copyText(item)\"></i></p>   2023-9-4 沈老师 暂时注释 -->\r\n            <!-- </div> -->\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div\r\n      class=\"MainArticle\"\r\n      v-if=\"flag == 'artificialIntelligence' || flag == 'networkSecurity'\"\r\n    >\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载文章\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除文章\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"icon--_tianjiadaoku\"\r\n              title=\"添加到报告\"\r\n              @click=\"dialogVisible = true\"\r\n              v-hasPermi=\"['result:report:add']\"\r\n            ></i>\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div class=\"scollBox\" @mousewheel=\"scrollChange\" ref=\"scroll\">\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div\r\n              class=\"Articl_left\"\r\n              :style=\"item.fileUrl ? 'width: 85%' : 'width: 100%'\"\r\n            >\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\"\r\n                      >{{ (currentPage - 1) * pageSize + key + 1 }}&nbsp;</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span>\r\n                </p>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题:</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ item.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\" v-if=\"item.fileUrl\">\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <!-- <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"primary\" plain @click=\"handleUpdate(item)\"\r\n                    v-hasPermi=\"['article:articleList:edit']\">{{ ` 修改 ` }}</el-button>\r\n                </p>\r\n                <p style=\"margin-top: -5px;\">\r\n                  <el-button style=\"width: 80px;\" size=\"mini\" type=\"danger\" plain @click=\"handleDelete(item)\"\r\n                    v-hasPermi=\"['article:list:remove']\">{{ `删除` }}</el-button>\r\n                </p> -->\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"MainArticle\" v-if=\"flag == 'zhikubaogao'\">\r\n      <div class=\"TopBtnGroup\">\r\n        <div class=\"leftBtnGroup\">\r\n          <p>\r\n            <el-checkbox\r\n              v-model=\"checked\"\r\n              :isIndeterminate=\"isIndeterminate\"\r\n              @change=\"handleCheckAllChange\"\r\n              >全选</el-checkbox\r\n            >\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-upload2\"\r\n              style=\"font-size: 22px; color: #1296db\"\r\n              title=\"批量导入报告\"\r\n              @click=\"openBatchImportDialog\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量下载报告\"\r\n              class=\"icon-xiazai1\"\r\n              style=\"color: green\"\r\n              @click=\"downLoadExcel\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i\r\n              title=\"批量删除报告\"\r\n              class=\"icon-shanchu\"\r\n              @click=\"batchDelete\"\r\n            ></i>\r\n          </p>\r\n          <p class=\"toolTitle\">\r\n            <i class=\"icon-shuaxin-copy\" title=\"刷新\" @click=\"Refresh\"></i>\r\n          </p>\r\n          <!-- <p class=\"toolTitle\">\r\n            <i\r\n              class=\"el-icon-chat-dot-round\"\r\n              style=\"font-size: 24px\"\r\n              title=\"Deepseek报告解读\"\r\n              @click=\"reportAiChat\"\r\n            ></i>\r\n            <span class=\"deepseek-text\" @click=\"reportAiChat\"\r\n              >Deepseek报告解读</span\r\n            >\r\n          </p> -->\r\n        </div>\r\n        <div>\r\n          <el-checkbox\r\n            v-model=\"showSummary\"\r\n            @change=\"(e) => (showSummary = e)\"\r\n            style=\"margin-right: 10px\"\r\n            >是否显示摘要</el-checkbox\r\n          >\r\n          <span style=\"font-size: 14px\">排序方式:</span>&nbsp;\r\n          <el-select v-model=\"SeachData.sortMode\" size=\"mini\">\r\n            <el-option label=\"按发布时间倒序排序\" :value=\"'0'\"></el-option>\r\n            <el-option label=\"按发布时间正序排序\" :value=\"'1'\"></el-option>\r\n            <el-option label=\"按采集时间倒序排序\" :value=\"'2'\"></el-option>\r\n            <el-option label=\"按采集时间正序排序\" :value=\"'3'\"></el-option>\r\n            <el-option label=\"按系统推荐排序\" :value=\"'4'\"></el-option>\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n      <el-checkbox-group\r\n        v-model=\"checkedCities\"\r\n        @change=\"handleCheckedCitiesChange\"\r\n      >\r\n        <div\r\n          class=\"scollBox\"\r\n          style=\"height: calc(100vh - 310px)\"\r\n          @mousewheel=\"scrollChange\"\r\n          ref=\"scroll\"\r\n        >\r\n          <div class=\"Articl\" v-for=\"(item, key) in ArticleList\" :key=\"key\">\r\n            <div class=\"Articl_left\">\r\n              <div class=\"ArticlTop\">\r\n                <p style=\"margin: 20px 0 5px 0; line-height: 20px\">\r\n                  <el-checkbox :label=\"item.id\" :value=\"item.id\">\r\n                    {{ null }} </el-checkbox\r\n                  >&nbsp;&nbsp;\r\n                  <a href=\"#\"\r\n                    ><span style=\"padding: 0 10px 0 0\">{{\r\n                      (currentPage - 1) * pageSize + key + 1\r\n                    }}</span\r\n                    ><span\r\n                      class=\"title_Article\"\r\n                      @click=\"openNewView(item)\"\r\n                      v-html=\"changeColor(item.title)\"\r\n                    ></span\r\n                  ></a>\r\n                  &nbsp;&nbsp;&nbsp;&nbsp;\r\n                  <!-- v-if=\"item.isTranslated == 0\" -->\r\n                  <!-- <i\r\n                    class=\"icon-taizhangtranslate\"\r\n                    @click=\"translateTitle(item)\"\r\n                    v-if=\"item.sourceType !== '1' && item.sourceType !== '3'\"\r\n                  ></i>\r\n                  <span\r\n                    class=\"linkStyle\"\r\n                    @click=\"openNewView(item, 'link')\"\r\n                    style=\"color: #1889f3; cursor: pointer\"\r\n                  >\r\n                    原文链接\r\n                  </span> -->\r\n                </p>\r\n                <!-- v-if=\"item.fileUrl\" -->\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                id\r\n                v-if=\"\r\n                  item.sourceType !== '1' &&\r\n                  item.sourceType !== '3' &&\r\n                  item.cnTitle\r\n                \"\r\n              >\r\n                <a href=\"#\">\r\n                  <span style=\"color: #9b9b9b\">中文标题：</span>\r\n                  <span\r\n                    v-html=\"changeColor(item.cnTitle)\"\r\n                    @click=\"openNewView(item)\"\r\n                  ></span>\r\n                </a>\r\n              </div>\r\n              <div class=\"info_flex\">\r\n                <div class=\"ArticlBottom\">\r\n                  <div>\r\n                    媒体来源:\r\n                    <span class=\"infomation\">{{ item.sourceName }}</span>\r\n                  </div>\r\n                  <div>\r\n                    发布时间:\r\n                    <span class=\"infomation\">{{\r\n                      formatPublishTime(\r\n                        item.publishTime,\r\n                        item.webstePublishTime\r\n                      )\r\n                    }}</span>\r\n                  </div>\r\n                  <div>\r\n                    采集时间:\r\n                    <span class=\"infomation\">{{\r\n                      parseTime(item.createTime, \"{y}-{m}-{d} {h}:{i}:{s}\")\r\n                    }}</span>\r\n                  </div>\r\n                  <!-- <div>\r\n                    大模型筛选:\r\n                    <span class=\"infomation\">\r\n                      {{ item.isTechnology == 1 ? \"是\" : \"否\" }}\r\n                    </span>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n              <div\r\n                class=\"ArticlMain\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  word-break: break-all;\r\n                \"\r\n                v-if=\"\r\n                  showSummary &&\r\n                  hasActualContent(item.cnSummary || item.summary)\r\n                \"\r\n              >\r\n                <span style=\"color: #9b9b9b\">摘要：</span>\r\n                <span\r\n                  v-html=\"\r\n                    changeColor(\r\n                      item.cnSummary.replace(\r\n                        /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                        'span'\r\n                      ) ||\r\n                        item.summary.replace(\r\n                          /p|strong|em|b|div|svg|h1|h2|h3|h4|h5|h6/g,\r\n                          'span'\r\n                        )\r\n                    )\r\n                  \"\r\n                  @click=\"openNewView(item)\"\r\n                ></span>\r\n              </div>\r\n            </div>\r\n            <div class=\"btnBox\">\r\n              <!-- <p>\r\n                  <el-button type=\"primary\" plain size=\"mini\" @click=\"resultEvent(item)\">\r\n                    {{ item.snapshotUrl\r\n                      ? '下载快照' : '生成快照' }}\r\n                  </el-button>\r\n                </p> -->\r\n              <p>\r\n                <el-button\r\n                  v-if=\"item.fileUrl\"\r\n                  :type=\"item.fileUrl ? 'primary' : 'info'\"\r\n                  :disabled=\"!item.fileUrl\"\r\n                  plain\r\n                  size=\"mini\"\r\n                  @click=\"documentDownload(item)\"\r\n                  >{{ \"附件下载\" }}</el-button\r\n                >\r\n              </p>\r\n              <p>\r\n                <el-button\r\n                  style=\"width: 80px\"\r\n                  size=\"mini\"\r\n                  type=\"danger\"\r\n                  plain\r\n                  @click=\"handleDelete(item)\"\r\n                  >删除</el-button\r\n                >\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <el-empty\r\n            :image-size=\"200\"\r\n            description=\"当前筛选条件下暂无数据\"\r\n            v-if=\"ArticleList == null || ArticleList.length == 0\"\r\n          ></el-empty>\r\n        </div>\r\n      </el-checkbox-group>\r\n    </div>\r\n    <div class=\"pagination\" ref=\"pagination\">\r\n      <el-pagination\r\n        :small=\"false\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-sizes=\"[10, 20, 30, 40, 50]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"->, total, sizes, prev, pager, next, jumper\"\r\n        :total=\"total\"\r\n      ></el-pagination>\r\n    </div>\r\n    <el-dialog\r\n      title=\"添加到报告\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-row style=\"line-height: 50px\">\r\n        <el-col :span=\"18\">\r\n          <el-select\r\n            v-model=\"reportId\"\r\n            placeholder=\"请选择报告\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"(item, key) in reportOptions\"\r\n              :key=\"key\"\r\n              :label=\"item.title\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-col>\r\n      </el-row>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"reportSubmit\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title\r\n      :visible.sync=\"tagDialog\"\r\n      width=\"20%\"\r\n      :before-close=\"closeTag\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <el-form\r\n        label-position=\"left\"\r\n        label-width=\"40px\"\r\n        :model=\"formLabelAlign\"\r\n        ref=\"ruleForm\"\r\n      >\r\n        <el-form-item label=\"行业\" prop=\"industry\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.industry\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            placeholder=\"请选择行业\"\r\n            :filterable=\"true\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options1\"\r\n              :key=\"item.id\"\r\n              :label=\"item.industryName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"领域\" prop=\"domain\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.domain\"\r\n            filterable\r\n            style=\"width: 100%\"\r\n            multiple\r\n            clearable\r\n            placeholder=\"请选择领域\"\r\n            default-first-option\r\n          >\r\n            <el-option\r\n              v-for=\"item in options\"\r\n              :key=\"item.id\"\r\n              :label=\"item.fieldName\"\r\n              :value=\"item.id\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"标签\" prop=\"tag\">\r\n          <el-select\r\n            v-model=\"formLabelAlign.tag\"\r\n            style=\"width: 100%\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请添加文章标签\"\r\n          >\r\n            <!-- <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>-->\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeTag\" size=\"mini\">取 消</el-button>\r\n        <el-button @click=\"SubmitTag\" type=\"primary\" size=\"mini\"\r\n          >确 定</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n    <el-drawer\r\n      @open=\"openDrawer\"\r\n      :title=\"drawerInfo.cnTitle || drawerInfo.title\"\r\n      :visible.sync=\"drawer\"\r\n      custom-class=\"drawer_box\"\r\n      direction=\"rtl\"\r\n      style=\"width: 100%\"\r\n    >\r\n      <div\r\n        v-if=\"flag == 'MonitorUse' || flag == 'specialSubjectUse'\"\r\n        style=\"width: 100%; text-align: right; padding-right: 10px\"\r\n      >\r\n        <el-button\r\n          @click=\"documentDownload('drawer')\"\r\n          :type=\"drawerInfo.fileUrl ? 'primary' : 'info'\"\r\n          size=\"mini\"\r\n          plain\r\n          :disabled=\"!drawerInfo.fileUrl\"\r\n          >{{ \"附件下载\" }}</el-button\r\n        >\r\n        <!-- <el-button @click=\"resultEvent('drawer')\" type=\"primary\" size=\"mini\" plain>\r\n          {{ drawerInfo.snapshotUrl\r\n            ? '下载快照' : '生成快照' }}\r\n        </el-button> -->\r\n        <!-- v-if=\"translationBtnShow\" -->\r\n        <el-button\r\n          type=\"primary\"\r\n          size=\"mini\"\r\n          plain\r\n          @click=\"translateEvent(drawerInfo)\"\r\n          >翻译内容</el-button\r\n        >\r\n      </div>\r\n      <template slot=\"title\">\r\n        <span class=\"drawer_Title\">{{\r\n          drawerInfo.cnTitle || drawerInfo.title\r\n        }}</span>\r\n      </template>\r\n      <div class=\"drawer_Style\">\r\n        <p class=\"title\">\r\n          {{ drawerInfo.cnTitle || drawerInfo.title }}\r\n          <!-- v-if=\"!drawerInfo.cnTitle\" -->\r\n          <i\r\n            class=\"icon-taizhangtranslate\"\r\n            @click=\"translateTitle(drawerInfo)\"\r\n          ></i>\r\n        </p>\r\n        <p style=\"text-align: center\">\r\n          <span class=\"source\">{{ drawerInfo.sourceName }}</span>\r\n          <span class=\"time\">{{ drawerInfo.publishTime }}</span>\r\n        </p>\r\n        <div\r\n          style=\"user-select: text !important; line-height: 30px\"\r\n          v-html=\"\r\n            drawerInfo.cnContent &&\r\n            drawerInfo.cnContent.replace(/<img\\b[^>]*>/gi, '')\r\n          \"\r\n        ></div>\r\n        <el-empty\r\n          description=\"当前文章暂无数据\"\r\n          v-if=\"!drawerInfo.cnContent\"\r\n        ></el-empty>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog\r\n      :title=\"'修改文章'\"\r\n      :visible.sync=\"open\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"dialog_Box\">\r\n        <el-form\r\n          :model=\"form\"\r\n          class=\"form_Style\"\r\n          label-position=\"top\"\r\n          ref=\"form\"\r\n          :rules=\"rules\"\r\n          size=\"mini\"\r\n        >\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"文章标题\" prop=\"title\">\r\n                <el-input v-model=\"form.title\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"中文标题\" prop=\"cnTitle\">\r\n                <el-input v-model=\"form.cnTitle\"></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"平台类型\" prop=\"sourceType\">\r\n                <el-select\r\n                  v-model=\"form.sourceType\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.id\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"媒体来源\" prop=\"sourceName\">\r\n                <el-select\r\n                  v-model=\"form.sourceName\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  filterable\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in sourceTypeLists\"\r\n                    :key=\"index\"\r\n                    :label=\"item.name\"\r\n                    :value=\"item.name\"\r\n                  ></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发布时间\" prop=\"publishTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.publishTime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"datetime\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                  placeholder=\"选择日期\"\r\n                ></el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"文章作者\" prop=\"author\">\r\n                <el-input\r\n                  v-model=\"form.author\"\r\n                  style=\"width: 100%\"\r\n                  clearable\r\n                ></el-input>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-form-item label=\"原文链接\" prop=\"originalUrl\">\r\n            <el-input v-model=\"form.originalUrl\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"摘要\" prop=\"summary\">\r\n            <el-input\r\n              v-model=\"form.summary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文摘要\" prop=\"cnSummary\">\r\n            <el-input\r\n              v-model=\"form.cnSummary\"\r\n              type=\"textarea\"\r\n              :autosize=\"{ minRows: 3, maxRows: 6 }\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"中文内容\" prop=\"cnContent\">\r\n            <editor v-model=\"form.cnContent\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <el-form-item label=\"文章内容\" prop=\"content\">\r\n            <editor v-model=\"form.content\" :minHeight=\"150\"></editor>\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"封面图片\" prop=\"cover\">\r\n            <el-upload action=\"#\" ref=\"upload\" :limit=\"3\" :on-exceed=\"exceed\" list-type=\"picture-card\"\r\n              :auto-upload=\"false\" :headers=\"vertifyUpload.headers\" :file-list=\"fileList\" :on-change=\"handleChange\"\r\n              :http-request=\"requestLoad\">\r\n              <i slot=\"default\" class=\"el-icon-plus\"></i>\r\n              <div slot=\"file\" slot-scope=\"{ file }\">\r\n                <img class=\"el-upload-list__item-thumbnail\" :src=\"file.url\" alt=\"文件缩略图加载失败\" />\r\n                <span class=\"el-upload-list__item-actions\">\r\n                  <span v-if=\"!disabled\" class=\"el-upload-list__item-delete\" @click=\"handleRemove(file)\">\r\n                    <i class=\"el-icon-delete\"></i>\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </el-upload>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传附件\" prop=\"fileUrl\">\r\n            <el-upload class=\"upload-demo\" :action=\"fileUrlurl\" :before-upload=\"beforeUploadUrl\" multiple :limit=\"1\"\r\n              :http-request=\"uploadUrlRequest\" :on-success=\"uploadUrlSuccess\" :file-list=\"fileUrlList\"\r\n              :on-exceed=\"uploadUrlExceed\" :on-remove=\"uploadUrlRemove\">\r\n              <el-button size=\"small\" type=\"primary\">点击上传</el-button>\r\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>\r\n            </el-upload>\r\n          </el-form-item> -->\r\n        </el-form>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量导入弹框 -->\r\n    <el-dialog\r\n      title=\"批量导入报告\"\r\n      :visible.sync=\"batchImportVisible\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"batch-import-container\">\r\n        <!-- 文件选择区域 -->\r\n        <div class=\"file-select-area\">\r\n          <el-upload\r\n            ref=\"batchUpload\"\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :show-file-list=\"false\"\r\n            multiple\r\n            :on-change=\"handleFileSelect\"\r\n            accept=\".pdf,.doc,.docx,.txt\"\r\n          >\r\n            <el-button type=\"primary\" icon=\"el-icon-upload\">选择文件</el-button>\r\n            <div slot=\"tip\" class=\"el-upload__tip\">\r\n              支持选择多个文件，格式：PDF、DOC、DOCX、TXT\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <!-- 文件列表表格 -->\r\n        <div class=\"file-table-area\" v-if=\"batchImportFiles.length > 0\">\r\n          <el-table\r\n            :data=\"batchImportFiles\"\r\n            border\r\n            style=\"width: 100%; margin-top: 20px\"\r\n            max-height=\"300\"\r\n          >\r\n            <el-table-column prop=\"fileName\" label=\"文件名称\" width=\"300\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.fileName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"sourceName\" label=\"数据源名称\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.sourceName\"\r\n                  placeholder=\"请输入数据源名称\"\r\n                  size=\"small\"\r\n                ></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"removeFile(scope.$index)\"\r\n                  style=\"color: #f56c6c\"\r\n                >\r\n                  删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBatchImport\">取 消</el-button>\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"confirmBatchImport\"\r\n          :disabled=\"batchImportFiles.length === 0\"\r\n        >\r\n          确 认\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog\r\n      title=\"Deepseek深度解读\"\r\n      :visible.sync=\"aiDialogVisible\"\r\n      width=\"1000px\"\r\n      :before-close=\"closeAiDialog\"\r\n      custom-class=\"ai-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div class=\"ai-chat-container\">\r\n        <div class=\"chat-messages\" ref=\"chatMessages\">\r\n          <div\r\n            class=\"message\"\r\n            v-for=\"(message, index) in chatMessages\"\r\n            :key=\"index\"\r\n            :class=\"[\r\n              'message',\r\n              message.role === 'user' ? 'user-message' : 'ai-message',\r\n            ]\"\r\n          >\r\n            <div class=\"avatar\">\r\n              <img\r\n                style=\"width: 30px; height: 30px\"\r\n                v-if=\"message.role === 'user'\"\r\n                :src=\"userAvatar || require('@/assets/images/home/<USER>')\"\r\n                alt=\"用户头像\"\r\n              />\r\n              <img v-else src=\"@/assets/images/logo2.png\" alt=\"AI头像\" />\r\n            </div>\r\n            <div class=\"message-wrapper\">\r\n              <div\r\n                v-if=\"\r\n                  message.role === 'assistant' && isThinking && !message.content\r\n                \"\r\n                class=\"thinking-animation\"\r\n              >\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n              </div>\r\n              <div\r\n                v-else\r\n                class=\"message-content\"\r\n                v-html=\"\r\n                  message.role === 'assistant'\r\n                    ? message.content\r\n                    : message.content\r\n                \"\r\n              ></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template slot=\"footer\">\r\n        <el-button @click=\"closeAiDialog\">取 消</el-button>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Deepseek报告解读弹窗 -->\r\n    <deepseek-report-dialog\r\n      :visible.sync=\"showDeepseekDialog\"\r\n      :article-data=\"currentArticle\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport API from \"@/api/ScienceApi/index.js\";\r\nimport request from \"@/utils/request\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { MessageBox } from \"element-ui\";\r\nimport axios from \"axios\";\r\nimport { getListClassify } from \"@/api/article/classify\";\r\nimport { saveAs } from \"file-saver\";\r\nimport { blobValidate, tansParams } from \"@/utils/ruoyi\";\r\nimport { articleListEdit, uploadCover } from \"@/api/articleCrawler/list\";\r\nimport DeepseekReportDialog from \"./DeepseekReportDialog.vue\";\r\nimport { deepseekAiQa, difyAiQa, ollamaAiQa } from \"@/api/infoEscalation/ai\";\r\nimport { marked } from \"marked\";\r\nimport { getConfigKey } from \"@/api/system/config\";\r\nimport { getListByIds } from \"@/api/article/articleHistory\";\r\n\r\nexport default {\r\n  props: {\r\n    downLoadShow: {\r\n      /* 下载按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    editShow: {\r\n      /* 编辑按钮 */ required: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    copyShow: {\r\n      /* 复制按钮 */ reuqired: false,\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    height: {\r\n      type: Number,\r\n      default: 655,\r\n    },\r\n    currentPage: {\r\n      reuqired: true,\r\n      default: 1,\r\n    },\r\n    pageSize: {\r\n      reuqired: true,\r\n      default: 50,\r\n    },\r\n    total: {\r\n      reuqired: true,\r\n      default: 0,\r\n    },\r\n    ArticleList: {\r\n      required: true,\r\n      default: [],\r\n    },\r\n    flag: {\r\n      required: true,\r\n    },\r\n    SeachData: {\r\n      required: true,\r\n    },\r\n    keywords: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    // 报告类型字段\r\n    sourceType: {\r\n      default: \"\",\r\n    },\r\n  },\r\n  components: {\r\n    DeepseekReportDialog,\r\n  },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      regExpImg: /^\\n$/,\r\n      reportId: \"\",\r\n      reportOptions: [],\r\n      dialogVisible: false,\r\n      checkedCities: [] /* 多选 */,\r\n      checked: false /* 全选 */,\r\n      html: \"\",\r\n      text: \"\",\r\n      that: this,\r\n      tagShow: false,\r\n      isIndeterminate: true,\r\n      count: 0,\r\n      separate: {},\r\n      /* 标签功能 */\r\n      tagDialog: false,\r\n      formLabelAlign: {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      },\r\n      options: [],\r\n      options1: [],\r\n      tagItem: {} /* 标签对象 */,\r\n      areaList: [] /* 领域 */,\r\n      industry: [] /* 行业 */,\r\n      num: 0,\r\n      timer: null,\r\n      drawer: false,\r\n      drawerInfo: {},\r\n      AreaId: null,\r\n      translationBtnShow: null,\r\n      open: false,\r\n      sourceTypeList: [], // 数据源分类\r\n      sourceLists: [], // 数据源列表\r\n      sourceTypeLists: [],\r\n      form: {}, // 表单参数\r\n      rules: {\r\n        // 表单校验\r\n        title: [{ required: true, message: \"文章标题为必填项\" }],\r\n        content: [{ required: true, message: \"文章详情为必填项\" }],\r\n        publishTime: [{ required: true, message: \"发布时间为必填项\" }],\r\n        cnTitle: [{ required: true, message: \"中文名称为必填项\" }],\r\n        sourceType: [{ required: true, message: \"平台类型为必填项\" }],\r\n        originalUrl: [{ required: true, message: \"原文为必填项\" }],\r\n        summary: [{ required: true, message: \"请填写摘要\" }],\r\n        // cnSummary: [{ required: true, message: '请填写中文摘要' }],\r\n        sn: [{ required: true, message: \"请填写文章地址唯一识别号\" }],\r\n      },\r\n      vertifyUpload: {\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: {\r\n          Authorization: \"Bearer \" + getToken(),\r\n          ContentType: \"application/json;charset=utf-8\",\r\n        },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/article/articleList/cover\",\r\n      },\r\n      fileList: [],\r\n      fileUrlList: [],\r\n      fileUrlurl:\r\n        process.env.VUE_APP_BASE_API + \"/article/articleList/upload/file\",\r\n      showSummary: true,\r\n      // 批量导入相关数据\r\n      batchImportVisible: false,\r\n      batchImportFiles: [],\r\n      // Deepseek报告解读弹窗\r\n      showDeepseekDialog: false,\r\n      currentArticle: {},\r\n      // ai相关\r\n      aiDialogVisible: false,\r\n      chatMessages: [],\r\n      isThinking: false,\r\n      userAvatar: \"\", // 用户头像\r\n      streamingMessage: \"\", // 添加用于存储正在流式输出的消息\r\n      markdownOptions: {\r\n        gfm: true,\r\n        breaks: true,\r\n        headerIds: true,\r\n        mangle: false,\r\n        headerPrefix: \"\",\r\n        pedantic: false,\r\n        sanitize: false,\r\n        smartLists: true,\r\n        smartypants: true,\r\n        xhtml: true,\r\n      },\r\n      isRequesting: false, // 标记是否正在请求中\r\n      isAborted: false, // 标记是否已中断\r\n      currentReader: null, // 当前的 reader\r\n      aiPlatform: \"\",\r\n      articleAiPrompt: \"\",\r\n    };\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    dialogVisible: function (newVal, oldVal) {\r\n      if (newVal) {\r\n        API.getNewBuilt({ sourceType: this.sourceType }).then((data) => {\r\n          if (data.code == 200) {\r\n            this.reportOptions = data.data;\r\n          } else {\r\n            this.$message({ message: \"报告列表获取失败了\", type: \"error\" });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 'formLabelAlign.industry': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options1 = this.industry\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 'formLabelAlign.domain': {\r\n    //   handler(newVal) {\r\n    //     if (newVal == '') {\r\n    //       this.options = this.areaList\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    \"SeachData.sortMode\": {\r\n      handler(newVal, oldVal) {\r\n        this.Refresh();\r\n      },\r\n      deep: true,\r\n    },\r\n    \"form.sourceType\": {\r\n      handler(newVal, oldVal) {\r\n        this.sourceTypeLists = this.sourceLists.filter((item) => {\r\n          return item.type == newVal;\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n  mounted() {},\r\n  created() {\r\n    if (\r\n      this.flag !== \"MonitorUse\" &&\r\n      this.flag !== \"specialSubjectUse\" &&\r\n      this.flag !== \"Wechat\"\r\n    ) {\r\n      this.openDialog();\r\n    }\r\n    if (this.flag !== \"Wechat\") {\r\n      getListClassify().then((res) => {\r\n        this.sourceTypeList = res.data;\r\n      });\r\n      API.getSourceList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.sourceLists = data.data;\r\n        }\r\n      });\r\n    }\r\n    if (this.$route.query.domain) {\r\n      getConfigKey(\"sys.ai.platform\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.aiPlatform = res.msg;\r\n        }\r\n      });\r\n      getConfigKey(\"wechat.ai.articlePrompt\").then((res) => {\r\n        if (res.code == 200) {\r\n          this.articleAiPrompt = res.msg;\r\n        }\r\n      });\r\n      // 获取用户头像\r\n      this.userAvatar = this.$store.getters.avatar;\r\n    }\r\n\r\n    this.showSummary = true;\r\n  },\r\n  updated() {},\r\n  filters: {},\r\n  methods: {\r\n    // 处理发布时间的显示\r\n    formatPublishTime(publishTime, webstePublishTime) {\r\n      // 格式化publishTime为年月日\r\n      const formattedPublishTime = this.parseTime(publishTime, \"{y}-{m}-{d}\");\r\n\r\n      // 如果webstePublishTime不存在，直接返回publishTime\r\n      if (!webstePublishTime) {\r\n        return formattedPublishTime;\r\n      }\r\n\r\n      let formattedWebsteTime = \"\";\r\n      // 处理不同格式的webstePublishTime\r\n      if (webstePublishTime) {\r\n        // 处理2025-04-12 10:09:21.971191格式（包含连字符的标准格式）\r\n        if (webstePublishTime.includes(\"-\")) {\r\n          const dateMatch = webstePublishTime.match(/(\\d{4})-(\\d{2})-(\\d{2})/);\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2];\r\n            const day = dateMatch[3];\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年04月14日 11:29:22格式（中文年月日格式，带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\") &&\r\n          webstePublishTime.includes(\"日\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})日/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025年4月15格式（中文年月格式，不带\"日\"字）\r\n        else if (\r\n          webstePublishTime.includes(\"年\") &&\r\n          webstePublishTime.includes(\"月\")\r\n        ) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})年(\\d{1,2})月(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        }\r\n        // 处理2025/04/14 11:29:22格式（斜杠分隔的格式）\r\n        else if (webstePublishTime.includes(\"/\")) {\r\n          const dateMatch = webstePublishTime.match(\r\n            /(\\d{4})\\/(\\d{1,2})\\/(\\d{1,2})/\r\n          );\r\n          if (dateMatch) {\r\n            const year = dateMatch[1];\r\n            const month = dateMatch[2].padStart(2, \"0\");\r\n            const day = dateMatch[3].padStart(2, \"0\");\r\n            formattedWebsteTime = `${year}-${month}-${day}`;\r\n          } else {\r\n            formattedWebsteTime = webstePublishTime;\r\n          }\r\n        } else {\r\n          // 其他格式直接使用原值\r\n          formattedWebsteTime = webstePublishTime;\r\n        }\r\n      }\r\n\r\n      // 比较年月日是否相同\r\n      if (formattedPublishTime === formattedWebsteTime) {\r\n        return formattedPublishTime;\r\n      } else {\r\n        return `${formattedPublishTime} / ${webstePublishTime}`;\r\n      }\r\n    },\r\n\r\n    // 检查文本是否有实际内容（去除HTML标签后）\r\n    hasActualContent(text) {\r\n      if (!text) {\r\n        return false;\r\n      }\r\n      // 去除HTML标签\r\n      const contentWithoutTags = text.replace(/<[^>]*>/g, \"\");\r\n      // 检查是否有中文、英文、数字等实际内容\r\n      return /[\\u4e00-\\u9fa5a-zA-Z0-9]/.test(contentWithoutTags);\r\n    },\r\n    // 关键字替换\r\n    changeColor(str) {\r\n      let Str = str;\r\n      if (Str) {\r\n        let keywords = this.keywords.split(\",\");\r\n        keywords.map((keyitem, keyindex) => {\r\n          if (keyitem && keyitem.length > 0) {\r\n            // 匹配关键字正则\r\n            let replaceReg = new RegExp(keyitem, \"g\");\r\n            // 高亮替换v-html值\r\n            let replaceString =\r\n              '<span class=\"highlight\"' +\r\n              ' style=\"color: red;\">' +\r\n              keyitem +\r\n              \"</span>\";\r\n            Str = Str.replace(replaceReg, replaceString);\r\n          }\r\n        });\r\n      }\r\n      return Str;\r\n    },\r\n    /* 下载Excel */\r\n    async downLoadExcel() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要导出的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.flag == \"specialSubjectUse\") {\r\n        API.downLoadExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n        });\r\n      } else {\r\n        await API.downLoadExportExcel(this.checkedCities).then((response) => {\r\n          let a = document.createElement(\"a\");\r\n          a.href = window.URL.createObjectURL(response);\r\n          a.download = `source_${new Date().getTime()}.xlsx`;\r\n          a.click();\r\n\r\n          // saveAs(blob, `source_${new Date().getTime()}.xlsx`)\r\n        });\r\n      }\r\n    },\r\n    batchDelete() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要删除的数据\", type: \"warning\" });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认删除已勾选的数据项?\")\r\n        .then(() => {\r\n          API.batchRemove(this.checkedCities.join(\",\")).then((response) => {\r\n            this.$message({ message: \"删除成功\", type: \"success\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 发布到每日最新热点 */\r\n    publishHot() {\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({\r\n          message: \"请选择要发布到每日最新热点的数据\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\"是否确认发布已勾选的数据项到每日最新热点?\")\r\n        .then(() => {\r\n          API.publishEverydayHot(this.checkedCities.join(\",\")).then(() => {\r\n            this.$message({ type: \"success\", message: \"发布成功!\" });\r\n            this.$emit(\"Refresh\");\r\n            this.checkedCities = [];\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /* 返回顶部动画 */\r\n    mainScorll() {\r\n      var scrollStep = -this.$refs.scroll.scrollTop / (800 / 15); // 计算每一步滚动的距离\r\n      var scrollInterval = setInterval(() => {\r\n        if (this.$refs.scroll.scrollTop !== 0) {\r\n          this.$refs.scroll.scrollBy(0, scrollStep); // 按照给定步长滚动窗口\r\n        } else {\r\n          clearInterval(scrollInterval); // 到达顶部时清除定时器\r\n        }\r\n      }, 15);\r\n    },\r\n    scrollChange() {\r\n      clearTimeout(this.timer);\r\n      this.timer = setTimeout(() => {\r\n        this.stopScroll();\r\n      }, 500);\r\n      // this.$refs.pagination.style.opacity = 0\r\n      // this.$refs.pagination.style.transition = '0'\r\n    } /* 滚动事件 */,\r\n    stopScroll() {\r\n      // this.$refs.pagination.style.transition = '1s'\r\n      // this.$refs.pagination.style.opacity = 1\r\n    },\r\n    /* 下载 */\r\n    downLoad() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /* 每页条数变化 */\r\n    handleSizeChange(num) {\r\n      this.$emit(\"handleSizeChange\", num);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 页码变化 */\r\n    handleCurrentChange(current) {\r\n      this.$emit(\"handleCurrentChange\", current);\r\n      this.mainScorll();\r\n      this.checked = false;\r\n    },\r\n    /* 收藏 */\r\n    async collect(item) {\r\n      /* 点击列表收藏 */\r\n      if (item.id) {\r\n        this.checkedCities = [item.id];\r\n      }\r\n      /* 未选择提示 */\r\n      if (this.checkedCities.length == 0) {\r\n        this.$message({ message: \"请选择要收藏的文章\", type: \"info\" });\r\n        return;\r\n      }\r\n      /* 收藏 */\r\n      if (!item.favorites) {\r\n        let res = await API.collectApi([item.id]);\r\n        if (res.code) {\r\n          this.$message({\r\n            message: \"收藏成功,请前往个人中心查看\",\r\n            type: \"success\",\r\n          });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"收藏失败\", type: \"info\" });\r\n      } else {\r\n        let res = await API.cocelCollect([item.id]);\r\n        if (res.code) {\r\n          this.$message({ message: \"已取消收藏\", type: \"success\" });\r\n          this.$emit(\"Refresh\");\r\n          this.checkedCities = [];\r\n          return;\r\n        }\r\n        this.$message({ message: \"取消收藏失败\", type: \"info\" });\r\n      }\r\n    },\r\n    /* 一键复制 */\r\n    copyText(item) {\r\n      navigator.clipboard\r\n        .writeText(item.cnTitle)\r\n        .then(() => {\r\n          this.$message({ message: \"已成功复制到剪贴板\", type: \"success\" });\r\n        })\r\n        .catch(function () {\r\n          alert(\"复制失败\");\r\n        });\r\n    },\r\n    typeHandle(data) {\r\n      if (data == 1) {\r\n        return \"微信公众号\";\r\n      } else if (data == 2) {\r\n        return \"网站\";\r\n      } else if (data == 3) {\r\n        return \"手动录入\";\r\n      }\r\n    },\r\n    /* 选择事件 */\r\n    handleCheckedCitiesChange(value) {\r\n      this.checkedCities = value;\r\n    },\r\n    /* 全选 */\r\n    handleCheckAllChange(val) {\r\n      this.checkedCities = val ? this.ArticleList.map((item) => item.id) : [];\r\n      this.isIndeterminate = false;\r\n    },\r\n    /* 刷新 */\r\n    Refresh() {\r\n      this.$emit(\"Refresh\");\r\n    },\r\n    /*确定添加到报告 */\r\n    async reportSubmit() {\r\n      this.dialogVisible = false;\r\n      let keyWordList = [];\r\n      if (!this.reportId)\r\n        return this.$message({\r\n          message: \"请选择要添加到的报告\",\r\n          type: \"warning\",\r\n        });\r\n      /* 单独添加 */\r\n      if (this.separate.id) {\r\n        // let keyword = Object.keys(this.separate.keywordCount)\r\n        keyWordList.push({\r\n          reportId: this.reportId,\r\n          listId: this.separate.id,\r\n          listSn: this.separate.sn,\r\n        });\r\n      } else {\r\n        /* 批量添加 */\r\n        if (this.checkedCities == \"\")\r\n          return this.$message({\r\n            message: \"请选择要添加的数据\",\r\n            type: \"warning\",\r\n          });\r\n        this.checkedCities.forEach((item) => {\r\n          let article = this.ArticleList.filter((value) => value.id == item);\r\n          keyWordList.push({\r\n            reportId: this.reportId,\r\n            listId: item,\r\n            listSn: article[0].sn,\r\n          });\r\n        });\r\n      }\r\n      let res = await API.AddReport(keyWordList);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"已添加到报告\", type: \"success\" });\r\n        this.$emit(\"Refresh\");\r\n      } else {\r\n        this.$message({\r\n          message: \"添加到报告失败,请联系管理员\",\r\n          type: \"error\",\r\n        });\r\n      }\r\n      this.separate = {};\r\n      this.reportId = \"\";\r\n      this.checkedCities = [];\r\n      this.checked = false;\r\n    },\r\n    /* 单独添加报告 */\r\n    separateAdd(item) {\r\n      this.dialogVisible = true;\r\n      this.separate = item;\r\n    },\r\n    /* 跳转新页面 */\r\n    openNewView(item, isLink) {\r\n      if (isLink) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n        return;\r\n      }\r\n      window.open(\r\n        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,\r\n        \"_blank\"\r\n      );\r\n      // this.drawerInfo = item\r\n      // this.drawer = true\r\n    },\r\n    /* 文章打标签 */\r\n    tagHandler(item) {\r\n      this.tagDialog = true;\r\n      this.tagItem = item;\r\n      if (item.industry) {\r\n        this.formLabelAlign.industry = item.industry\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      if (item.domain) {\r\n        this.formLabelAlign.domain = item.domain\r\n          .split(\",\")\r\n          .map((data) => Number(data));\r\n      }\r\n      this.formLabelAlign.tag = item.tags ? item.tags.split(\",\") : \"\";\r\n    },\r\n    /* 获取领域和分类 */\r\n    async openDialog() {\r\n      await API.areaList().then((data) => {\r\n        if (data.code == 200) {\r\n          this.areaList = data.data;\r\n          this.options = data.data;\r\n          API.industry().then((value) => {\r\n            this.industry = value.data;\r\n            this.options1 = value.data;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 筛选领域 */\r\n    remoteEvent(query) {\r\n      this.options = this.areaList.filter((item) => item.fieldName == query);\r\n    },\r\n    /* 筛选行业 */\r\n    remoteIndustry(query) {\r\n      this.options1 = this.industry.filter(\r\n        (item) => item.industryName == query\r\n      );\r\n    },\r\n    async SubmitTag() {\r\n      let params = {\r\n        domain: String(this.formLabelAlign.domain),\r\n        industry: String(this.formLabelAlign.industry),\r\n        tags: String(this.formLabelAlign.tag),\r\n        articleId: String(this.tagItem.id),\r\n        docId: this.tagItem.docId ? String(this.tagItem.docId) : \"\",\r\n      };\r\n      let res = await API.tagAdd(params);\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"保存成功\", type: \"success\" });\r\n        setTimeout(() => {\r\n          this.Refresh();\r\n        }, 1000);\r\n      } else {\r\n        this.$message({ message: \"保存失败\", type: \"error\" });\r\n      }\r\n      this.closeTag();\r\n    },\r\n    closeTag() {\r\n      this.$refs[\"ruleForm\"].resetFields();\r\n      this.formLabelAlign = {\r\n        tag: \"\",\r\n        industry: \"\",\r\n        domain: \"\",\r\n      };\r\n      this.tagDialog = false;\r\n    },\r\n    async hotIncrease(item) {\r\n      let isWhether = JSON.parse(item.isWhether);\r\n      let res = await API.tagAdd({\r\n        articleId: item.id,\r\n        isWhether: +!Boolean(isWhether),\r\n      });\r\n      if (res.code == 200) {\r\n        this.$message({ message: \"操作成功\", type: \"success\" });\r\n        this.Refresh();\r\n      } else {\r\n        this.$message({ message: \"操作失败\", type: \"error\" });\r\n      }\r\n    },\r\n    async openDrawer() {\r\n      let docId = this.drawerInfo.docId;\r\n      await API.AreaInfo(this.drawerInfo.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.drawerInfo = res.data;\r\n          this.drawerInfo.docId = docId;\r\n          /* 将字符串中的\\n替换为<br> */\r\n          this.translationBtnShow = !this.drawerInfo.cnContent;\r\n          if (this.drawerInfo.cnContent || this.drawerInfo.content) {\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\\\n/g, (a, b, c) => {\r\n              return \"<br>\";\r\n            });\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(/\\${[^}]+}/g, \"<br>\");\r\n            this.drawerInfo.cnContent = (\r\n              this.drawerInfo.cnContent || this.drawerInfo.content\r\n            ).replace(\"|xa0\", \"\");\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /* 所属行业处理 */\r\n    industryHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.industry) {\r\n        ids = item.industry.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.industry.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.industryName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    domainHandle(item) {\r\n      let ids = [],\r\n        str = \"\";\r\n      if (item.domain) {\r\n        ids = item.domain.split(\",\");\r\n      }\r\n      ids.forEach((data) => {\r\n        this.areaList.map((ele) => {\r\n          if (ele.id == data) {\r\n            if (str == undefined) {\r\n              str = \"\";\r\n            }\r\n            str += ele.fieldName + \" \";\r\n          }\r\n        });\r\n      });\r\n      return str;\r\n    },\r\n    /* 快照生成 */\r\n    resultEvent(item) {\r\n      if (item == \"BatchGeneration\" && this.checkedCities.length == 0) {\r\n        this.$message.warning(\"请先选择文章\");\r\n        return;\r\n      }\r\n      let ids = null;\r\n      let zhuangtai = \"生成\";\r\n      let url = \"\";\r\n      if (item == \"drawer\") {\r\n        ids = [this.drawerInfo.id];\r\n        if (this.drawerInfo.snapshotUrl) zhuangtai = \"查看\";\r\n        url = this.drawerInfo.snapshotUrl;\r\n      } else if (item == \"BatchGeneration\") {\r\n        ids = this.checkedCities;\r\n      } else {\r\n        ids = [item.id];\r\n        if (item.snapshotUrl) zhuangtai = \"查看\";\r\n        url = item.snapshotUrl;\r\n      }\r\n      if (zhuangtai == \"生成\") {\r\n        if (this.flag == \"MonitorUse\") {\r\n          API.downLoadExportKe(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        } else {\r\n          API.downLoadExportZhuan(ids)\r\n            .then((response) => {\r\n              if (response.code == 200) {\r\n                this.$msgbox({\r\n                  title: \"提示\",\r\n                  message: \"快照正在生成中，请稍后查看\",\r\n                  showCancelButton: true,\r\n                  confirmButtonText: \"关闭\",\r\n                  cancelButtonText: \"取消\",\r\n                  showCancelButton: false,\r\n                  beforeClose: (action, instance, done) => {\r\n                    done();\r\n                  },\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: \"申请失败，请联系管理员，确认采集器是否正常\",\r\n                  type: \"error\",\r\n                });\r\n              }\r\n            })\r\n            .catch((err) => {});\r\n        }\r\n      } else {\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/server-api/\", \"g\"), \"/\");\r\n        url = url.replace(new RegExp(\"/home/<USER>/dpx/\", \"g\"), \"/\");\r\n        window.open(window.location.origin + url, \"_blank\");\r\n      }\r\n    },\r\n    /* 附件下载 */\r\n    async documentDownload(item) {\r\n      this.loading = true;\r\n      if (item.fileUrl) {\r\n        const urls = item.fileUrl.split(\",\");\r\n        for (const [index, url] of urls.entries()) {\r\n          if (url.indexOf(\"https://\") === -1) {\r\n            setTimeout(async () => {\r\n              await this.downLoadFun(url, index, item.cnTitle || item.title);\r\n            }, index * 500);\r\n          } else {\r\n            this.$message.error(\"附件还没同步到当前系统，暂时无法下载\");\r\n          }\r\n        }\r\n      }\r\n      this.loading = false;\r\n    },\r\n\r\n    async downLoadFun(url, index, title) {\r\n      let formData = new FormData();\r\n      formData.append(\"fileUrl\", url);\r\n\r\n      try {\r\n        const response = await API.downloadFile(formData);\r\n        const isBlob = blobValidate(response);\r\n\r\n        if (isBlob) {\r\n          const blob = new Blob([response]);\r\n          let list = url.split(\"/\");\r\n          let fileName = list[list.length - 1];\r\n          saveAs(blob, fileName);\r\n        } else {\r\n          const resText = await response.text();\r\n          const rspObj = JSON.parse(resText);\r\n          const errMsg =\r\n            errorCode[rspObj.code] || rspObj.msg || errorCode[\"default\"];\r\n          this.$message.error(errMsg);\r\n        }\r\n      } catch (err) {\r\n        // this.$message.error(`Error downloading file: ${err}`);\r\n      } finally {\r\n        // 确保 loading 在每次下载后都设置为 false\r\n        this.loading = false;\r\n      }\r\n\r\n      // 之前的附件下载\r\n      // if (item.annexUrl) {\r\n      //   /* 有文件地址 直接下载 */\r\n      //   let formData = new FormData()\r\n      //   formData.append('id', item.id)\r\n      //   this.loading = true\r\n      //   API.documentDownloadKe(formData).then(res => {\r\n      //     let a = document.createElement('a')\r\n      //     a.href = URL.createObjectURL(res.data)\r\n      //     a.download = res.headers['content-disposition'].split('filename=')[1]\r\n      //     a.click()\r\n      //     this.loading = false\r\n      //   })\r\n      // } else {\r\n      //   /* 没有文件格式 申请下载 */\r\n      //   API.documentDownload(id).then(response => {\r\n      //     if (response.code == 200) {\r\n      //       this.$msgbox({\r\n      //         title: '提示',\r\n      //         message: '附件正在同步中，请稍后下载',\r\n      //         showCancelButton: true,\r\n      //         confirmButtonText: '关闭',\r\n      //         cancelButtonText: '取消',\r\n      //         showCancelButton: false,\r\n      //         beforeClose: (action, instance, done) => {\r\n      //           done()\r\n      //         }\r\n      //       })\r\n      //     } else {\r\n      //       this.$message({ message: '附件下载失败', type: 'error' })\r\n      //     }\r\n      //   }).catch(err => { })\r\n      // }\r\n    },\r\n    // 翻译标题\r\n    translateTitle(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.title,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"title\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnTitle = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    // 翻译文章\r\n    translateEvent(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\",\r\n        spinner: \"el-icon-loading\",\r\n        background: \"rgba(0, 0, 0, 0.7)\",\r\n      });\r\n      API.translationTitle({\r\n        originalText: row.content,\r\n        docId: row.docId,\r\n        id: row.id,\r\n        translationField: \"content\",\r\n        translationType: 1,\r\n      })\r\n        .then((res) => {\r\n          this.drawerInfo.cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].cnContent = res.data;\r\n          this.ArticleList[\r\n            this.ArticleList.findIndex((value) => value.id == row.id)\r\n          ].isTranslated = 1;\r\n          this.translationBtnShow = false;\r\n          loading.close();\r\n        })\r\n        .catch((err) => {\r\n          loading.close();\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      API.AreaInfo(row.id).then((response) => {\r\n        this.form = response.data;\r\n        this.form.sourceType = Number(this.form.sourceType);\r\n        this.form.docId = row.docId;\r\n        // this.fileUrlList = this.form.fileUrl ? this.form.fileUrl.split(\",\").map(item => {\r\n        //   return {\r\n        //     name: item,\r\n        //     url: item\r\n        //   }\r\n        // }) : []\r\n        this.open = true;\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm('是否确认删除该条文章？\"')\r\n        .then(() => {\r\n          return API.monitoringEsRemove({ id: row.id, docId: row.docId });\r\n        })\r\n        .then(() => {\r\n          this.Refresh();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          let queryForm = JSON.parse(JSON.stringify(this.form));\r\n          // let cover = String(this.fileList.map(item => item.path))\r\n          // queryForm.cover = cover\r\n          articleListEdit(queryForm).then((response) => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.Refresh();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /* 自定义上传 */\r\n    async requestLoad(file) {\r\n      let data = new FormData();\r\n      data.append(\"cover\", file.file);\r\n      await uploadCover(data).then((response) => {\r\n        if (response.code == 200) {\r\n          this.fileList.map((item) => {\r\n            if (item.uid == file.file.uid) {\r\n              item.path = response.imgUrl;\r\n            }\r\n          });\r\n          this.$message({ message: \"上传成功\", type: \"success\" });\r\n        } else {\r\n          this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    /* 文件超出限制 */\r\n    exceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传三个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    /* 移除文件 */\r\n    handleRemove(file) {\r\n      this.fileList = this.fileList.filter((item) => item !== file);\r\n    },\r\n    // 文件更改\r\n    handleChange(file, fileList) {\r\n      this.fileList = fileList;\r\n      this.$refs.upload.submit();\r\n    },\r\n    // 上传附件校验\r\n    beforeUploadUrl(file) {\r\n      // 判断文件是否为excel\r\n      let fileName = file.name\r\n          .substring(file.name.lastIndexOf(\".\") + 1)\r\n          .toLowerCase(),\r\n        condition =\r\n          fileName == \"pdf\" ||\r\n          fileName == \"doc\" ||\r\n          fileName == \"xls\" ||\r\n          fileName == \"ppt\" ||\r\n          fileName == \"xlsx\" ||\r\n          fileName == \"pptx\" ||\r\n          fileName == \"docx\";\r\n      let fileSize = file.size / 1024 / 1024 < 10;\r\n      if (!condition) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件必须是pdf,doc,xls,ppt,xlsx,pptx,docx格式\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      /* 文件大小限制 */\r\n      if (!fileSize) {\r\n        this.$notify({\r\n          title: \"警告\",\r\n          message: \"上传文件的大小不能超过 10MB!\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n      return condition && fileSize;\r\n    },\r\n    // 文件上传成功\r\n    uploadUrlSuccess(res, file) {\r\n      this.$message({ message: \"上传成功\", type: \"success\" });\r\n    },\r\n    // 文件上传超出限制\r\n    uploadUrlExceed() {\r\n      this.$message({\r\n        message: \"文件上传超出限制,最多可以上传1个文件\",\r\n        type: \"info\",\r\n      });\r\n    },\r\n    // 文件上传方法\r\n    uploadUrlRequest(file) {\r\n      if (this.form.originalUrl != null && this.form.originalUrl != \"\") {\r\n        if (\r\n          this.form.originalUrl.match(\r\n            /(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/\r\n          )\r\n        ) {\r\n          let data = new FormData();\r\n          data.append(\"file\", file.file);\r\n          data.append(\"originalUrl\", this.form.originalUrl);\r\n\r\n          API.uploadFile(data).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$message({ message: \"上传成功\", type: \"success\" });\r\n              this.form.fileUrl = response.data;\r\n            } else {\r\n              this.$message({ message: \"上传失败,请稍候重试\", type: \"error\" });\r\n              this.form.fileUrl = \"\";\r\n            }\r\n          });\r\n        } else {\r\n          this.$message({ message: \"请填写正确的原文链接\", type: \"warning\" });\r\n          this.fileUrlList = [];\r\n        }\r\n      } else {\r\n        this.$message({ message: \"请填写原文链接\", type: \"warning\" });\r\n        this.fileUrlList = [];\r\n      }\r\n    },\r\n    // 删除附件\r\n    uploadUrlRemove() {\r\n      API.removeFile({ filePath: this.form.fileUrl }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$message({ message: \"删除成功\", type: \"success\" });\r\n          this.fileUrlList = [];\r\n          this.form.fileUrl = \"\";\r\n        } else {\r\n          this.$message({ message: \"删除失败,请稍候重试\", type: \"error\" });\r\n        }\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        articleSn: null,\r\n        title: null,\r\n        cnTitle: null,\r\n        sourceType: null,\r\n        sourceName: null,\r\n        sourceSn: null,\r\n        originalUrl: null,\r\n        shortUrl: null,\r\n        author: null,\r\n        description: null,\r\n        summary: null,\r\n        cnSummary: null,\r\n        cover: null,\r\n        publishType: null,\r\n        publishCode: null,\r\n        publishArea: null,\r\n        publishTime: null,\r\n        numberLikes: null,\r\n        numberReads: null,\r\n        numberCollects: null,\r\n        numberShares: null,\r\n        numberComments: null,\r\n        emotion: null,\r\n        status: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: null,\r\n        deptId: null,\r\n        content: null,\r\n        cnContent: null,\r\n        fileUrl: null,\r\n        industry: null,\r\n        domain: null,\r\n        tmpUrl: null,\r\n        isFinish: null,\r\n        groupId: null,\r\n        appId: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 批量导入相关方法\r\n    // 打开批量导入弹框\r\n    openBatchImportDialog() {\r\n      this.batchImportVisible = true;\r\n      this.batchImportFiles = [];\r\n    },\r\n    // 文件选择处理\r\n    handleFileSelect(file, fileList) {\r\n      // 将新选择的文件添加到列表中\r\n      const newFiles = fileList.map((item) => ({\r\n        fileName: item.name,\r\n        file: item.raw,\r\n        sourceName: \"\",\r\n      }));\r\n      this.batchImportFiles = newFiles;\r\n    },\r\n    // 删除文件\r\n    removeFile(index) {\r\n      this.batchImportFiles.splice(index, 1);\r\n    },\r\n    // 取消批量导入\r\n    cancelBatchImport() {\r\n      this.batchImportVisible = false;\r\n      this.batchImportFiles = [];\r\n      // 清空文件选择器\r\n      this.$refs.batchUpload.clearFiles();\r\n    },\r\n    // 确认批量导入\r\n    async confirmBatchImport() {\r\n      // 验证数据源名称是否都已填写\r\n      const emptySourceNames = this.batchImportFiles.filter(\r\n        (item) => !item.sourceName.trim()\r\n      );\r\n      if (emptySourceNames.length > 0) {\r\n        this.$message({\r\n          message: \"请为所有文件填写数据源名称\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 创建FormData对象\r\n        const formData = new FormData();\r\n\r\n        // 添加文件到FormData\r\n        this.batchImportFiles.forEach((item) => {\r\n          formData.append(\"files\", item.file);\r\n        });\r\n\r\n        // 获取数据源名称数组\r\n        const sourceNames = this.batchImportFiles\r\n          .map((item) => item.sourceName)\r\n          .join(\",\");\r\n\r\n        formData.append(\"sourceNames\", sourceNames);\r\n\r\n        // 调用批量导入API，传递FormData和sourceNames参数\r\n        const response = await API.batchImportReports(formData);\r\n\r\n        if (response.code === 200) {\r\n          this.$message({\r\n            message: \"批量导入成功\",\r\n            type: \"success\",\r\n          });\r\n          this.batchImportVisible = false;\r\n          this.batchImportFiles = [];\r\n          this.$refs.batchUpload.clearFiles();\r\n          // 刷新列表\r\n          this.Refresh();\r\n        } else {\r\n          this.$message({\r\n            message: response.msg || \"批量导入失败\",\r\n            type: \"error\",\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"批量导入错误:\", error);\r\n        this.$message({\r\n          message: \"批量导入失败，请稍后重试\",\r\n          type: \"error\",\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    reportAiChat() {\r\n      this.showDeepseekDialog = true;\r\n      if (this.checkedCities.length === 0) {\r\n        this.$message({ message: \"请选择要解读的文章\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      if (this.checkedCities.length > 1) {\r\n        this.$message({ message: \"请只选择一篇文章进行解读\", type: \"warning\" });\r\n        return;\r\n      }\r\n\r\n      // 获取选中的文章\r\n      const selectedArticleId = this.checkedCities[0];\r\n      const selectedArticle = this.ArticleList.find(\r\n        (item) => item.id === selectedArticleId\r\n      );\r\n\r\n      if (selectedArticle) {\r\n        this.currentArticle = selectedArticle;\r\n        this.showDeepseekDialog = true;\r\n      } else {\r\n        this.$message({ message: \"未找到选中的文章\", type: \"error\" });\r\n      }\r\n    },\r\n    // ai相关\r\n    // dify\r\n    async difyAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await difyAiQa(\r\n          articlesContent,\r\n          \"streaming\",\r\n          \"dify.article.apikey\"\r\n        );\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader;\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let pendingBuffer = \"\"; // 用于存储待处理的不完整数据\r\n        let isInThinkTag = false; // 新增：标记是否在think标签内\r\n\r\n        // 将Unicode转义字符(\\uXXXX)转换为实际字符\r\n        const decodeUnicode = (str) => {\r\n          return str.replace(/\\\\u[\\dA-Fa-f]{4}/g, (match) => {\r\n            return String.fromCharCode(parseInt(match.replace(/\\\\u/g, \"\"), 16));\r\n          });\r\n        };\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          try {\r\n            const renderedContent = marked(newContent, this.markdownOptions);\r\n            aiMessage.content = renderedContent;\r\n\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          } catch (error) {\r\n            console.error(\"渲染内容时出错:\", error);\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        while (true) {\r\n          // 检查是否已中断\r\n          if (this.isAborted) {\r\n            throw new Error(\"AbortError\");\r\n          }\r\n\r\n          const { done, value } = await reader.read();\r\n\r\n          if (done) {\r\n            // 处理最后可能剩余的数据\r\n            if (pendingBuffer) {\r\n              try {\r\n                const lastData = JSON.parse(pendingBuffer);\r\n                if (lastData.answer) {\r\n                  // 解码Unicode转义字符\r\n                  const decodedAnswer = decodeUnicode(lastData.answer);\r\n                  buffer += decodedAnswer;\r\n                  updateContent(buffer);\r\n                }\r\n              } catch (e) {\r\n                console.warn(\"处理最后的数据时出错:\", e);\r\n              }\r\n            }\r\n            break;\r\n          }\r\n\r\n          const chunk = decoder.decode(value);\r\n          pendingBuffer += chunk;\r\n\r\n          // 处理完整的数据行\r\n          while (pendingBuffer.includes(\"\\n\")) {\r\n            const newlineIndex = pendingBuffer.indexOf(\"\\n\");\r\n            const line = pendingBuffer.slice(0, newlineIndex).trim();\r\n            pendingBuffer = pendingBuffer.slice(newlineIndex + 1);\r\n\r\n            if (!line || line === \"data:\" || !line.startsWith(\"data:\")) {\r\n              continue;\r\n            }\r\n\r\n            try {\r\n              const data = line.slice(5).trim();\r\n              if (data === \"[DONE]\") {\r\n                continue;\r\n              }\r\n\r\n              const jsonData = JSON.parse(data);\r\n              if (!jsonData.answer) {\r\n                continue;\r\n              }\r\n\r\n              // 跳过特殊字符\r\n              if (jsonData.answer === \"```\" || jsonData.answer === \"markdown\") {\r\n                continue;\r\n              }\r\n\r\n              // 解码Unicode转义字符\r\n              let answer = decodeUnicode(jsonData.answer);\r\n\r\n              // 检查是否包含<think>开始标签\r\n              if (answer.includes(\"<think>\")) {\r\n                isInThinkTag = true;\r\n                continue; // 跳过包含<think>的部分\r\n              }\r\n\r\n              // 检查是否包含</think>结束标签\r\n              if (answer.includes(\"</think>\")) {\r\n                isInThinkTag = false;\r\n                continue; // 跳过包含</think>的部分\r\n              }\r\n\r\n              // 只有不在think标签内的内容才会被添加到buffer中\r\n              if (!isInThinkTag && answer) {\r\n                buffer += answer;\r\n                updateContent(buffer);\r\n              }\r\n            } catch (parseError) {\r\n              console.warn(\"解析数据行时出错:\", {\r\n                line,\r\n                error: parseError.message,\r\n                pendingBuffer,\r\n              });\r\n              continue;\r\n            }\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null;\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // Ollama\r\n    async ollamaAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      try {\r\n        // 获取选中的文章\r\n        const selectedArticles = this.ArticleList.filter((article) =>\r\n          this.checkedCities.includes(article.id)\r\n        );\r\n        const titles = selectedArticles\r\n          .map((article) => `《${article.cnTitle || article.title}》`)\r\n          .join(\"\\n\");\r\n\r\n        // 获取文章内容\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data?.length) {\r\n          throw new Error(\"获取文章内容失败\");\r\n        }\r\n\r\n        // 格式化文章内容\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n\r\n        // 构建提示词\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        // 调用AI接口\r\n        const response = await ollamaAiQa(prompt, true);\r\n        if (!response.ok) {\r\n          throw new Error(\"AI接口调用失败\");\r\n        }\r\n\r\n        // 处理流式响应\r\n        const reader = response.body.getReader();\r\n        this.currentReader = reader; // 保存当前的 reader\r\n        const decoder = new TextDecoder();\r\n        let buffer = \"\";\r\n        let lastUpdateTime = Date.now();\r\n        let isThinkContent = false;\r\n        let tempBuffer = \"\";\r\n\r\n        // 更新内容的函数\r\n        const updateContent = (newContent) => {\r\n          const currentTime = Date.now();\r\n          // 控制更新频率，避免过于频繁的DOM更新\r\n          if (currentTime - lastUpdateTime >= 50) {\r\n            aiMessage.content = newContent;\r\n            lastUpdateTime = currentTime;\r\n            // 确保消息容器滚动到底部\r\n            this.$nextTick(() => {\r\n              const chatMessages = this.$refs.chatMessages;\r\n              if (chatMessages) {\r\n                chatMessages.scrollTop = chatMessages.scrollHeight;\r\n              }\r\n            });\r\n          }\r\n        };\r\n\r\n        // 处理流式响应\r\n        const processStream = async () => {\r\n          try {\r\n            while (true) {\r\n              // 检查是否已中断\r\n              if (this.isAborted) {\r\n                throw new Error(\"AbortError\");\r\n              }\r\n\r\n              const { done, value } = await reader.read();\r\n              if (done) {\r\n                if (buffer.length > 0) {\r\n                  updateContent(buffer);\r\n                }\r\n                break;\r\n              }\r\n\r\n              const chunk = decoder.decode(value);\r\n              const lines = chunk.split(\"\\n\").filter((line) => line.trim());\r\n\r\n              for (const line of lines) {\r\n                try {\r\n                  const jsonData = JSON.parse(line);\r\n                  if (!jsonData.response) continue;\r\n\r\n                  const response = jsonData.response;\r\n\r\n                  // 跳过特殊字符\r\n                  if (response === \"```\" || response === \"markdown\") {\r\n                    continue;\r\n                  }\r\n\r\n                  tempBuffer += response;\r\n\r\n                  // 检查是否包含完整的think标签对\r\n                  while (true) {\r\n                    const thinkStartIndex = tempBuffer.indexOf(\"<think>\");\r\n                    const thinkEndIndex = tempBuffer.indexOf(\"</think>\");\r\n\r\n                    if (thinkStartIndex === -1 && thinkEndIndex === -1) {\r\n                      // 没有think标签，直接添加到buffer\r\n                      if (!isThinkContent) {\r\n                        buffer += tempBuffer;\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = \"\";\r\n                      break;\r\n                    } else if (thinkStartIndex !== -1 && thinkEndIndex === -1) {\r\n                      // 只有开始标签，等待结束标签\r\n                      isThinkContent = true;\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkStartIndex);\r\n                      break;\r\n                    } else if (thinkStartIndex === -1 && thinkEndIndex !== -1) {\r\n                      // 只有结束标签，移除之前的内容\r\n                      isThinkContent = false;\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      continue;\r\n                    } else {\r\n                      // 有完整的think标签对\r\n                      if (thinkStartIndex > 0) {\r\n                        buffer += tempBuffer.substring(0, thinkStartIndex);\r\n                        // 使用marked渲染markdown内容\r\n                        updateContent(marked(buffer, this.markdownOptions));\r\n                      }\r\n                      tempBuffer = tempBuffer.substring(thinkEndIndex + 8);\r\n                      isThinkContent = false;\r\n                      continue;\r\n                    }\r\n                  }\r\n                } catch (parseError) {\r\n                  console.warn(\"无效的JSON行，已跳过\", {\r\n                    line,\r\n                    error: parseError.message,\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (streamError) {\r\n            if (streamError.message === \"AbortError\") {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n            console.error(\"处理流式响应时出错:\", streamError);\r\n            throw streamError;\r\n          }\r\n        };\r\n\r\n        await processStream();\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI解读出错:\", error);\r\n        this.$message.error(error.message || \"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // deepseek\r\n    async deepseekAiChat() {\r\n      if (this.checkedCities.length === 0) {\r\n        return this.$message({\r\n          message: \"请先选择要解读的文章\",\r\n          type: \"warning\",\r\n        });\r\n      }\r\n\r\n      // 如果有正在进行的请求，中断它\r\n      if (this.isRequesting) {\r\n        this.isAborted = true;\r\n        if (this.currentReader) {\r\n          try {\r\n            await this.currentReader.cancel();\r\n          } catch (e) {\r\n            console.log(\"中断之前的请求失败\", e);\r\n          }\r\n        }\r\n        // 等待之前的请求状态清理完成\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n      }\r\n\r\n      this.isRequesting = true;\r\n      this.isAborted = false;\r\n      this.aiDialogVisible = true;\r\n      this.chatMessages = [];\r\n      this.isThinking = true;\r\n\r\n      const selectedArticles = this.ArticleList.filter((article) =>\r\n        this.checkedCities.includes(article.id)\r\n      );\r\n      const titles = selectedArticles\r\n        .map((article) => `《${article.cnTitle || article.title}》`)\r\n        .join(\"\\n\");\r\n\r\n      try {\r\n        const articlesResponse = await getListByIds(\r\n          this.checkedCities.join(\",\")\r\n        );\r\n        if (!articlesResponse.data || !articlesResponse.data.length) {\r\n          throw new Error(\"Failed to get article contents\");\r\n        }\r\n\r\n        const articlesContent = articlesResponse.data\r\n          .map((article, index) => {\r\n            const title =\r\n              selectedArticles[index]?.cnTitle ||\r\n              selectedArticles[index]?.title ||\r\n              \"\";\r\n            const content = article.content || \"\";\r\n            return `【第 ${index + 1} 篇文章】《${title}》\\n\\n${content}`;\r\n          })\r\n          .join(\"\\n\\n-------------------------------------------\\n\\n\");\r\n\r\n        // 添加用户消息\r\n        this.chatMessages.push({\r\n          role: \"user\",\r\n          content: `帮我深度解读以下${this.checkedCities.length}篇文章：\\n${titles}`,\r\n        });\r\n\r\n        // 创建AI消息并添加到对话中\r\n        const aiMessage = {\r\n          role: \"assistant\",\r\n          content: \"\",\r\n        };\r\n        this.chatMessages.push(aiMessage);\r\n        this.isThinking = true;\r\n\r\n        const prompt =\r\n          this.articleAiPrompt\r\n            .replace(\"articleLength\", this.checkedCities.length)\r\n            .replace(/\\&gt;/g, \">\") +\r\n          `\\n\\n**以下是待处理的文章：**\\n\\n${articlesContent}`;\r\n\r\n        const response = await deepseekAiQa(prompt, true);\r\n\r\n        if (response.ok) {\r\n          const reader = response.body.getReader();\r\n          this.currentReader = reader; // 保存当前的 reader\r\n          const decoder = new TextDecoder();\r\n          let buffer = \"\";\r\n          let lastUpdateTime = Date.now();\r\n\r\n          const updateContent = (newContent) => {\r\n            const currentTime = Date.now();\r\n            if (currentTime - lastUpdateTime >= 50) {\r\n              aiMessage.content = newContent;\r\n              lastUpdateTime = currentTime;\r\n              this.$nextTick(() => {\r\n                const chatMessages = this.$refs.chatMessages;\r\n                if (chatMessages) {\r\n                  chatMessages.scrollTop = chatMessages.scrollHeight;\r\n                }\r\n              });\r\n            }\r\n          };\r\n\r\n          while (true) {\r\n            // 检查是否已中断\r\n            if (this.isAborted) {\r\n              throw new Error(\"AbortError\");\r\n            }\r\n\r\n            const { done, value } = await reader.read();\r\n            if (done) {\r\n              if (buffer.length > 0) {\r\n                updateContent(buffer);\r\n              }\r\n              break;\r\n            }\r\n\r\n            const chunk = decoder.decode(value);\r\n            try {\r\n              const lines = chunk.split(\"\\n\");\r\n\r\n              for (const line of lines) {\r\n                if (!line.trim() || !line.startsWith(\"data: \")) continue;\r\n\r\n                const data = line.slice(5);\r\n                if (data === \"[DONE]\") break;\r\n\r\n                try {\r\n                  const jsonData = JSON.parse(data);\r\n                  if (jsonData.choices?.[0]?.delta?.content) {\r\n                    let content = jsonData.choices[0].delta.content;\r\n\r\n                    // 跳过特殊字符\r\n                    if (content === \"```\" || content === \"markdown\") {\r\n                      continue;\r\n                    }\r\n\r\n                    buffer += content;\r\n                    updateContent(buffer);\r\n                  }\r\n                } catch (parseError) {\r\n                  console.error(\"Error parsing JSON:\", parseError);\r\n                }\r\n              }\r\n            } catch (e) {\r\n              console.error(\"Error processing chunk:\", e);\r\n            }\r\n          }\r\n        } else {\r\n          throw new Error(\"Request failed\");\r\n        }\r\n      } catch (error) {\r\n        // 判断是否是中断导致的错误\r\n        if (error.message === \"AbortError\") {\r\n          console.log(\"请求已被中断\");\r\n          return;\r\n        }\r\n        console.error(\"AI Chat Error:\", error);\r\n        this.$message.error(\"AI解读失败，请稍后重试\");\r\n        if (this.chatMessages[1]) {\r\n          this.chatMessages[1].content = \"抱歉，服务器繁忙，请稍后再试\";\r\n        }\r\n      } finally {\r\n        this.currentReader = null; // 清理当前的 reader\r\n        // 只有在没有被中断的情况下才重置状态\r\n        if (this.aiDialogVisible) {\r\n          this.isThinking = false;\r\n          this.isRequesting = false;\r\n        }\r\n      }\r\n    },\r\n    // 关闭AI对话\r\n    closeAiDialog() {\r\n      this.isAborted = true; // 设置中断标志\r\n      if (this.currentReader) {\r\n        this.currentReader.cancel(); // 中断当前的读取\r\n      }\r\n      this.aiDialogVisible = false;\r\n      this.chatMessages = [];\r\n      this.isThinking = false;\r\n      this.isRequesting = false;\r\n      this.currentReader = null;\r\n    },\r\n    articleAiChat() {\r\n      if (this.aiPlatform === \"dify\") {\r\n        this.difyAiChat();\r\n      } else if (this.aiPlatform === \"ollama\") {\r\n        this.ollamaAiChat();\r\n      } else if (this.aiPlatform === \"deepseek\") {\r\n        this.deepseekAiChat();\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* drawer样式修改 */\r\n.main ::v-deep .el-drawer.drawer_box {\r\n  width: 700px !important;\r\n}\r\n\r\n.MainArticle {\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  // margin-top: 10px;\r\n  user-select: text;\r\n\r\n  .TopBtnGroup {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    align-items: center;\r\n    border-bottom: solid 1px #e2e2e2;\r\n  }\r\n\r\n  .leftBtnGroup {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    gap: 15px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    & > p {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .leftBtnGroup2 {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    gap: 15px;\r\n    flex: 1;\r\n    padding-left: 20px;\r\n\r\n    .toolTitle {\r\n      color: #606266;\r\n      font-size: 14px;\r\n      line-height: 36px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .scollBox {\r\n    overflow-y: auto;\r\n    height: calc(100vh - 389px);\r\n    transition: transform 5s ease;\r\n\r\n    .Articl {\r\n      display: flex;\r\n      width: 100%;\r\n      border-bottom: solid 1px #d4d4d4;\r\n\r\n      .Articl_left {\r\n        width: 85%;\r\n        padding-bottom: 16px;\r\n      }\r\n\r\n      .Articl_kqcb {\r\n        width: 100%;\r\n        padding-bottom: 16px;\r\n        & > div:first-child {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .ArticlTop {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        padding: 0 0 0 20px;\r\n        font-size: 15px;\r\n      }\r\n\r\n      .ArticlMain {\r\n        padding: 0 0 0 30px;\r\n        color: #3f3f3f;\r\n        font-size: 14px;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .ArticlMain > span:hover {\r\n        color: #1889f3;\r\n        border-bottom: solid 1px #0798f8;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .info_flex {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n      }\r\n\r\n      .ArticlBottom {\r\n        padding: 0 0 0 30px;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 0 25px;\r\n        line-height: 24px;\r\n        color: #9b9b9b;\r\n        font-size: 14px;\r\n\r\n        div {\r\n          text-overflow: ellipsis;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .linkStyle:hover {\r\n          border-bottom: solid 1px #1889f3;\r\n        }\r\n\r\n        .infomation {\r\n          color: #464749;\r\n          font-size: 14px;\r\n          margin-left: 8px;\r\n        }\r\n\r\n        p {\r\n          border: solid 1px #f0a147;\r\n          width: 45px;\r\n          height: 25px;\r\n          line-height: 25px;\r\n          font-size: 14px;\r\n          color: #f0a147;\r\n          text-align: center;\r\n        }\r\n      }\r\n\r\n      .imgBox {\r\n        padding: 0 20px 0 30px;\r\n        display: flex;\r\n        gap: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.pagination {\r\n  z-index: 2;\r\n  background-color: rgba(255, 255, 255, 1);\r\n  width: 100%;\r\n  box-shadow: 0 0px 10px 0px rgb(206, 205, 205);\r\n  padding: 10px 0;\r\n}\r\n\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n  line-height: 16px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n\r\n.drawer_Style {\r\n  z-index: 2;\r\n  margin: 0 15px 0 15px;\r\n  width: 661px;\r\n  height: 80vh;\r\n\r\n  .title {\r\n    font-size: 16px;\r\n    font-weight: 500px;\r\n    text-align: center;\r\n  }\r\n\r\n  .source {\r\n    color: #0798f8;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .time {\r\n    font-size: 14px;\r\n    text-align: center;\r\n    margin-left: 10px;\r\n    color: #9b9b9b;\r\n  }\r\n}\r\n\r\n.drawer_Title {\r\n  text-overflow: ellipsis;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n}\r\n\r\n.btnBox {\r\n  z-index: 2;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n\r\n  & > p {\r\n    margin-bottom: 10px;\r\n  }\r\n}\r\n\r\n// 批量导入弹框样式\r\n.batch-import-container {\r\n  .file-select-area {\r\n    text-align: center;\r\n    padding: 20px 0;\r\n    border: 2px dashed #d9d9d9;\r\n    border-radius: 6px;\r\n    background-color: #fafafa;\r\n\r\n    &:hover {\r\n      border-color: #409eff;\r\n    }\r\n  }\r\n\r\n  .file-table-area {\r\n    margin-top: 20px;\r\n\r\n    .el-table {\r\n      border-radius: 4px;\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-icon-chat-dot-round:before {\r\n  color: #1296db;\r\n}\r\n\r\n::v-deep .el-icon-document-add:before {\r\n  color: #1296db;\r\n}\r\n\r\n.deepseek-text {\r\n  color: #1296db; // 使用与图标相同的颜色\r\n  margin-left: 4px;\r\n  font-size: 14px;\r\n  line-height: 24px;\r\n}\r\n\r\n// ai相关\r\n.ai-chat-container {\r\n  height: 550px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #f5f7fa;\r\n\r\n  .chat-messages {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n    padding: 24px;\r\n\r\n    .message {\r\n      margin-bottom: 28px;\r\n      display: flex;\r\n      align-items: flex-start;\r\n\r\n      .avatar {\r\n        width: 42px;\r\n        height: 42px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n        border: 2px solid #fff;\r\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n        background-color: #fff;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n          background-color: #fff;\r\n        }\r\n      }\r\n\r\n      .message-wrapper {\r\n        margin: 0 16px;\r\n        max-width: calc(100% - 100px);\r\n      }\r\n\r\n      .message-content {\r\n        padding: 12px 16px;\r\n        border-radius: 12px;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n        position: relative;\r\n\r\n        &::before {\r\n          content: \"\";\r\n          position: absolute;\r\n          top: 14px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 6px solid transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .user-message {\r\n      flex-direction: row-reverse;\r\n\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-end;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #e6f3ff;\r\n        color: #2d2d2d;\r\n        line-height: 1.8em;\r\n        font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n          Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n          Arial, sans-serif;\r\n\r\n        &::before {\r\n          right: -12px;\r\n          border-left-color: #e6f3ff;\r\n        }\r\n      }\r\n    }\r\n\r\n    .ai-message {\r\n      .message-wrapper {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n      }\r\n\r\n      .message-content {\r\n        background-color: #fff;\r\n        color: #2d2d2d;\r\n\r\n        &::before {\r\n          left: -12px;\r\n          border-right-color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .thinking-animation {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    padding: 12px 16px;\r\n    min-height: 45px;\r\n    background: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n    position: relative;\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 14px;\r\n      left: -12px;\r\n      width: 0;\r\n      height: 0;\r\n      border: 6px solid transparent;\r\n      border-right-color: #fff;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      width: 6px;\r\n      height: 6px;\r\n      margin: 0 3px;\r\n      background-color: #409eff;\r\n      border-radius: 50%;\r\n      opacity: 0.7;\r\n      animation: thinking 1.4s infinite ease-in-out both;\r\n\r\n      &:nth-child(1) {\r\n        animation-delay: -0.32s;\r\n      }\r\n\r\n      &:nth-child(2) {\r\n        animation-delay: -0.16s;\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-content {\r\n    min-height: 45px;\r\n    white-space: pre-wrap;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n\r\n    ::v-deep {\r\n      h1,\r\n      h2,\r\n      h3,\r\n      h4,\r\n      h5,\r\n      h6 {\r\n        margin: 0.05em 0 0.02em 0;\r\n        font-weight: 600;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      h1 {\r\n        font-size: 1.6em;\r\n        margin-top: 0;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h2 {\r\n        font-size: 1.4em;\r\n        padding-bottom: 0.05em;\r\n        margin-bottom: 0.02em;\r\n      }\r\n\r\n      h3 {\r\n        font-size: 1.2em;\r\n      }\r\n\r\n      p {\r\n        margin: 0;\r\n        line-height: 1.8em;\r\n        color: #2d3748;\r\n      }\r\n\r\n      strong {\r\n        font-weight: 600;\r\n        color: #1a1a1a;\r\n      }\r\n\r\n      em {\r\n        font-style: italic;\r\n        color: #2c5282;\r\n      }\r\n\r\n      ul,\r\n      ol {\r\n        margin: 0;\r\n        padding-left: 1em;\r\n        display: flex !important;\r\n        flex-direction: column !important;\r\n        // row-gap: 20px !important;\r\n\r\n        li {\r\n          margin: 0;\r\n          line-height: 1.8em;\r\n          color: #2d3748;\r\n\r\n          // 如果li中包含p标签，则设置行高为1\r\n          &:has(p) {\r\n            line-height: 1;\r\n          }\r\n\r\n          // 如果li中没有p标签，保持默认行高1.8em（已在上面设置）\r\n\r\n          p {\r\n            margin: 0;\r\n            line-height: 1.8em;\r\n          }\r\n        }\r\n      }\r\n\r\n      blockquote {\r\n        margin: 0.05em 0;\r\n        padding: 0.05em 0.4em;\r\n        color: #2c5282;\r\n        background: #ebf8ff;\r\n        border-left: 4px solid #4299e1;\r\n\r\n        p {\r\n          margin: 0.02em 0;\r\n          line-height: 1.8em;\r\n        }\r\n\r\n        > :first-child {\r\n          margin-top: 0;\r\n        }\r\n\r\n        > :last-child {\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n\r\n      code {\r\n        padding: 0.05em 0.1em;\r\n        margin: 0;\r\n        font-size: 0.9em;\r\n        background: #edf2f7;\r\n        border-radius: 3px;\r\n        color: #2d3748;\r\n      }\r\n\r\n      hr {\r\n        height: 1px;\r\n        margin: 0.1em 0;\r\n        border: none;\r\n        background-color: #e2e8f0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.chat-messages {\r\n  &::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n\r\n  &::-webkit-scrollbar-thumb {\r\n    background-color: rgba(192, 196, 204, 0.5);\r\n    border-radius: 3px;\r\n\r\n    &:hover {\r\n      background-color: rgba(192, 196, 204, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes thinking {\r\n  0%,\r\n  80%,\r\n  100% {\r\n    transform: scale(0);\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n// 修改弹窗样式\r\n::v-deep .ai-dialog {\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background-color: #f5f7fa;\r\n  }\r\n\r\n  .el-dialog__footer {\r\n    padding: 15px 20px;\r\n    background: #fff;\r\n    border-top: 1px solid #e4e7ed;\r\n\r\n    .el-button {\r\n      padding: 9px 20px;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style>\r\n.title_Article {\r\n  color: rgb(8, 8, 8);\r\n  font-size: 15px;\r\n}\r\n\r\n.title_Article:hover {\r\n  color: #1889f3;\r\n  border-bottom: solid 1px #0798f8;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm+DA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;AACA,IAAAM,UAAA,GAAAN,OAAA;AACA,IAAAO,MAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,qBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,GAAA,GAAAV,OAAA;AACA,IAAAW,OAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;AACA,IAAAa,eAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAc,KAAA;IACAC,YAAA;MACA,UAAAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACA,UAAAJ,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAE,QAAA;MACA,UAAAC,QAAA;MACAL,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAI,MAAA;MACAN,IAAA,EAAAO,MAAA;MACAL,OAAA;IACA;IACAM,WAAA;MACAH,QAAA;MACAH,OAAA;IACA;IACAO,QAAA;MACAJ,QAAA;MACAH,OAAA;IACA;IACAQ,KAAA;MACAL,QAAA;MACAH,OAAA;IACA;IACAS,WAAA;MACAZ,QAAA;MACAG,OAAA;IACA;IACAU,IAAA;MACAb,QAAA;IACA;IACAc,SAAA;MACAd,QAAA;IACA;IACAe,QAAA;MACAd,IAAA,EAAAe,MAAA;MACAb,OAAA;IACA;IACA;IACAc,UAAA;MACAd,OAAA;IACA;EACA;EACAe,UAAA;IACAC,oBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA;MACAC,QAAA;MACAC,aAAA;MACAC,aAAA;MACAC,aAAA;MACAC,OAAA;MACAC,IAAA;MACAC,IAAA;MACAC,IAAA;MACAC,OAAA;MACAC,eAAA;MACAC,KAAA;MACAC,QAAA;MACA;MACAC,SAAA;MACAC,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,QAAA;MACAL,QAAA;MACAM,GAAA;MACAC,KAAA;MACAC,MAAA;MACAC,UAAA;MACAC,MAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,cAAA;MAAA;MACAC,WAAA;MAAA;MACAC,eAAA;MACAC,IAAA;MAAA;MACAC,KAAA;QACA;QACAC,KAAA;UAAAxD,QAAA;UAAAyD,OAAA;QAAA;QACAC,OAAA;UAAA1D,QAAA;UAAAyD,OAAA;QAAA;QACAE,WAAA;UAAA3D,QAAA;UAAAyD,OAAA;QAAA;QACAG,OAAA;UAAA5D,QAAA;UAAAyD,OAAA;QAAA;QACAxC,UAAA;UAAAjB,QAAA;UAAAyD,OAAA;QAAA;QACAI,WAAA;UAAA7D,QAAA;UAAAyD,OAAA;QAAA;QACAK,OAAA;UAAA9D,QAAA;UAAAyD,OAAA;QAAA;QACA;QACAM,EAAA;UAAA/D,QAAA;UAAAyD,OAAA;QAAA;MACA;MACAO,aAAA;QACAC,WAAA;QACA;QACAC,OAAA;UACAC,aAAA,kBAAAC,cAAA;UACAC,WAAA;QACA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA,EACAL,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAI,WAAA;MACA;MACAC,kBAAA;MACAC,gBAAA;MACA;MACAC,kBAAA;MACAC,cAAA;MACA;MACAC,eAAA;MACAC,YAAA;MACAC,UAAA;MACAC,UAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;QACAC,GAAA;QACAC,MAAA;QACAC,SAAA;QACAC,MAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,aAAA;MAAA;MACAC,UAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;IACA/E,aAAA,WAAAA,cAAAgF,MAAA,EAAAC,MAAA;MAAA,IAAAC,KAAA;MACA,IAAAF,MAAA;QACAG,cAAA,CAAAC,WAAA;UAAA5F,UAAA,OAAAA;QAAA,GAAA6F,IAAA,WAAA1F,IAAA;UACA,IAAAA,IAAA,CAAA2F,IAAA;YACAJ,KAAA,CAAAnF,aAAA,GAAAJ,IAAA,CAAAA,IAAA;UACA;YACAuF,KAAA,CAAAK,QAAA;cAAAvD,OAAA;cAAAxD,IAAA;YAAA;UACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAgH,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAAQ,OAAA;MACA;MACAC,IAAA;IACA;IACA;MACAF,OAAA,WAAAA,QAAAR,MAAA,EAAAC,MAAA;QACA,KAAArD,eAAA,QAAAD,WAAA,CAAAgE,MAAA,WAAAC,IAAA;UACA,OAAAA,IAAA,CAAApH,IAAA,IAAAwG,MAAA;QACA;MACA;MACAU,IAAA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IACA,KAAA3G,IAAA,qBACA,KAAAA,IAAA,4BACA,KAAAA,IAAA,eACA;MACA,KAAA4G,UAAA;IACA;IACA,SAAA5G,IAAA;MACA,IAAA6G,yBAAA,IAAAZ,IAAA,WAAAa,GAAA;QACAH,MAAA,CAAArE,cAAA,GAAAwE,GAAA,CAAAvG,IAAA;MACA;MACAwF,cAAA,CAAAgB,aAAA,GAAAd,IAAA,WAAA1F,IAAA;QACA,IAAAA,IAAA,CAAA2F,IAAA;UACAS,MAAA,CAAApE,WAAA,GAAAhC,IAAA,CAAAA,IAAA;QACA;MACA;IACA;IACA,SAAAyG,MAAA,CAAAC,KAAA,CAAAvF,MAAA;MACA,IAAAwF,oBAAA,qBAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAnB,UAAA,GAAAsB,GAAA,CAAAK,GAAA;QACA;MACA;MACA,IAAAD,oBAAA,6BAAAjB,IAAA,WAAAa,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACAS,MAAA,CAAAlB,eAAA,GAAAqB,GAAA,CAAAK,GAAA;QACA;MACA;MACA;MACA,KAAA3C,UAAA,QAAA4C,MAAA,CAAAC,OAAA,CAAAC,MAAA;IACA;IAEA,KAAAtD,WAAA;EACA;EACAuD,OAAA,WAAAA,QAAA;EACAC,OAAA;EACAC,OAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA5E,WAAA,EAAA6E,iBAAA;MACA;MACA,IAAAC,oBAAA,QAAAC,SAAA,CAAA/E,WAAA;;MAEA;MACA,KAAA6E,iBAAA;QACA,OAAAC,oBAAA;MACA;MAEA,IAAAE,mBAAA;MACA;MACA,IAAAH,iBAAA;QACA;QACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,SAAA,GAAAL,iBAAA,CAAAM,KAAA;UACA,IAAAD,SAAA;YACA,IAAAE,IAAA,GAAAF,SAAA;YACA,IAAAG,KAAA,GAAAH,SAAA;YACA,IAAAI,GAAA,GAAAJ,SAAA;YACAF,mBAAA,MAAAO,MAAA,CAAAH,IAAA,OAAAG,MAAA,CAAAF,KAAA,OAAAE,MAAA,CAAAD,GAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,UAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,8BACA;UACA,IAAAD,UAAA;YACA,IAAAE,KAAA,GAAAF,UAAA;YACA,IAAAG,MAAA,GAAAH,UAAA,IAAAM,QAAA;YACA,IAAAF,IAAA,GAAAJ,UAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,KAAA,OAAAG,MAAA,CAAAF,MAAA,OAAAE,MAAA,CAAAD,IAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IACAA,iBAAA,CAAAI,QAAA,SACAJ,iBAAA,CAAAI,QAAA,OACA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,6BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;QACA;QAAA,KACA,IAAAA,iBAAA,CAAAI,QAAA;UACA,IAAAC,WAAA,GAAAL,iBAAA,CAAAM,KAAA,CACA,+BACA;UACA,IAAAD,WAAA;YACA,IAAAE,MAAA,GAAAF,WAAA;YACA,IAAAG,OAAA,GAAAH,WAAA,IAAAM,QAAA;YACA,IAAAF,KAAA,GAAAJ,WAAA,IAAAM,QAAA;YACAR,mBAAA,MAAAO,MAAA,CAAAH,MAAA,OAAAG,MAAA,CAAAF,OAAA,OAAAE,MAAA,CAAAD,KAAA;UACA;YACAN,mBAAA,GAAAH,iBAAA;UACA;QACA;UACA;UACAG,mBAAA,GAAAH,iBAAA;QACA;MACA;;MAEA;MACA,IAAAC,oBAAA,KAAAE,mBAAA;QACA,OAAAF,oBAAA;MACA;QACA,UAAAS,MAAA,CAAAT,oBAAA,SAAAS,MAAA,CAAAV,iBAAA;MACA;IACA;IAEA;IACAY,gBAAA,WAAAA,iBAAAvH,IAAA;MACA,KAAAA,IAAA;QACA;MACA;MACA;MACA,IAAAwH,kBAAA,GAAAxH,IAAA,CAAAyH,OAAA;MACA;MACA,kCAAAC,IAAA,CAAAF,kBAAA;IACA;IACA;IACAG,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAC,GAAA,GAAAD,GAAA;MACA,IAAAC,GAAA;QACA,IAAA3I,QAAA,QAAAA,QAAA,CAAA4I,KAAA;QACA5I,QAAA,CAAA6I,GAAA,WAAAC,OAAA,EAAAC,QAAA;UACA,IAAAD,OAAA,IAAAA,OAAA,CAAAE,MAAA;YACA;YACA,IAAAC,UAAA,OAAAC,MAAA,CAAAJ,OAAA;YACA;YACA,IAAAK,aAAA,GACA,4BACA,0BACAL,OAAA,GACA;YACAH,GAAA,GAAAA,GAAA,CAAAJ,OAAA,CAAAU,UAAA,EAAAE,aAAA;UACA;QACA;MACA;MACA,OAAAR,GAAA;IACA;IACA,aACAS,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAA,MACAT,MAAA,CAAA1I,aAAA,CAAAqI,MAAA;gBAAAY,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAT,MAAA,CAAApD,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAA0K,QAAA,CAAAG,MAAA;YAAA;cAAA,MAIAV,MAAA,CAAAvJ,IAAA;gBAAA8J,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAjE,cAAA,CAAAuD,aAAA,CAAAC,MAAA,CAAA1I,aAAA,EAAAoF,IAAA,WAAAiE,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAArC,MAAA,KAAAsC,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;cACA;cAAAf,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAEAjE,cAAA,CAAA+E,mBAAA,CAAAvB,MAAA,CAAA1I,aAAA,EAAAoF,IAAA,WAAAiE,QAAA;gBACA,IAAAC,CAAA,GAAAC,QAAA,CAAAC,aAAA;gBACAF,CAAA,CAAAG,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAP,QAAA;gBACAC,CAAA,CAAAO,QAAA,aAAArC,MAAA,KAAAsC,IAAA,GAAAC,OAAA;gBACAT,CAAA,CAAAU,KAAA;;gBAEA;cACA;YAAA;YAAA;cAAA,OAAAf,QAAA,CAAAiB,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IACAqB,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAApK,aAAA,CAAAqI,MAAA;QACA,KAAA/C,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MACA,KAAA8L,QAAA,mBACAjF,IAAA;QACAF,cAAA,CAAAoF,WAAA,CAAAF,MAAA,CAAApK,aAAA,CAAAuK,IAAA,OAAAnF,IAAA,WAAAiE,QAAA;UACAe,MAAA,CAAA9E,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA6L,MAAA,CAAAI,KAAA;UACAJ,MAAA,CAAApK,aAAA;QACA;MACA,GACAyK,KAAA;IACA;IACA,eACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAA3K,aAAA,CAAAqI,MAAA;QACA,KAAA/C,QAAA;UACAvD,OAAA;UACAxD,IAAA;QACA;QACA;MACA;MACA,KAAA8L,QAAA,0BACAjF,IAAA;QACAF,cAAA,CAAA0F,kBAAA,CAAAD,MAAA,CAAA3K,aAAA,CAAAuK,IAAA,OAAAnF,IAAA;UACAuF,MAAA,CAAArF,QAAA;YAAA/G,IAAA;YAAAwD,OAAA;UAAA;UACA4I,MAAA,CAAAH,KAAA;UACAG,MAAA,CAAA3K,aAAA;QACA;MACA,GACAyK,KAAA;IACA;IACA,YACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,SAAAC,KAAA,CAAAC,MAAA,CAAAC,SAAA;MACA,IAAAC,cAAA,GAAAC,WAAA;QACA,IAAAN,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,SAAA;UACAJ,MAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAI,QAAA,IAAAN,UAAA;QACA;UACAO,aAAA,CAAAH,cAAA;QACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACAC,YAAA,MAAAtK,KAAA;MACA,KAAAA,KAAA,GAAAuK,UAAA;QACAF,MAAA,CAAAG,UAAA;MACA;MACA;MACA;IACA;IACAA,UAAA,WAAAA,WAAA;MACA;MACA;IAAA,CACA;IACA,QACAC,QAAA,WAAAA,SAAA;MACA,KAAA7L,aAAA;IACA;IACA,YACA8L,gBAAA,WAAAA,iBAAA3K,GAAA;MACA,KAAAsJ,KAAA,qBAAAtJ,GAAA;MACA,KAAA2J,UAAA;MACA,KAAA5K,OAAA;IACA;IACA,UACA6L,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAvB,KAAA,wBAAAuB,OAAA;MACA,KAAAlB,UAAA;MACA,KAAA5K,OAAA;IACA;IACA,QACA+L,OAAA,WAAAA,QAAArG,IAAA;MAAA,IAAAsG,MAAA;MAAA,WAAAtD,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAqD,SAAA;QAAA,IAAAjG,GAAA,EAAAkG,IAAA;QAAA,WAAAvD,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAqD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,IAAA,GAAAmD,SAAA,CAAAlD,IAAA;YAAA;cACA;cACA,IAAAxD,IAAA,CAAA2G,EAAA;gBACAL,MAAA,CAAAjM,aAAA,IAAA2F,IAAA,CAAA2G,EAAA;cACA;cACA;cAAA,MACAL,MAAA,CAAAjM,aAAA,CAAAqI,MAAA;gBAAAgE,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAA3G,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA,OAAA8N,SAAA,CAAAjD,MAAA;YAAA;cAAA,IAIAzD,IAAA,CAAA4G,SAAA;gBAAAF,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OACAjE,cAAA,CAAAsH,UAAA,EAAA7G,IAAA,CAAA2G,EAAA;YAAA;cAAArG,GAAA,GAAAoG,SAAA,CAAAI,IAAA;cAAA,KACAxG,GAAA,CAAAZ,IAAA;gBAAAgH,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAA3G,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cACA0N,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAAjM,aAAA;cAAA,OAAAqM,SAAA,CAAAjD,MAAA;YAAA;cAGA6C,MAAA,CAAA3G,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cAAA8N,SAAA,CAAAlD,IAAA;cAAA;YAAA;cAAAkD,SAAA,CAAAlD,IAAA;cAAA,OAEAjE,cAAA,CAAAwH,YAAA,EAAA/G,IAAA,CAAA2G,EAAA;YAAA;cAAArG,IAAA,GAAAoG,SAAA,CAAAI,IAAA;cAAA,KACAxG,IAAA,CAAAZ,IAAA;gBAAAgH,SAAA,CAAAlD,IAAA;gBAAA;cAAA;cACA8C,MAAA,CAAA3G,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACA0N,MAAA,CAAAzB,KAAA;cACAyB,MAAA,CAAAjM,aAAA;cAAA,OAAAqM,SAAA,CAAAjD,MAAA;YAAA;cAGA6C,MAAA,CAAA3G,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;YAAA;YAAA;cAAA,OAAA8N,SAAA,CAAAnC,IAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IAEA;IACA,UACAS,QAAA,WAAAA,SAAAhH,IAAA;MAAA,IAAAiH,MAAA;MACAC,SAAA,CAAAC,SAAA,CACAC,SAAA,CAAApH,IAAA,CAAAzD,OAAA,EACAkD,IAAA;QACAwH,MAAA,CAAAtH,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA,GACAkM,KAAA;QACAuC,KAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAvN,IAAA;MACA,IAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA,WAAAA,IAAA;QACA;MACA;IACA;IACA,UACAwN,yBAAA,WAAAA,0BAAAC,KAAA;MACA,KAAAnN,aAAA,GAAAmN,KAAA;IACA;IACA,QACAC,oBAAA,WAAAA,qBAAAC,GAAA;MACA,KAAArN,aAAA,GAAAqN,GAAA,QAAAnO,WAAA,CAAAgJ,GAAA,WAAAvC,IAAA;QAAA,OAAAA,IAAA,CAAA2G,EAAA;MAAA;MACA,KAAAhM,eAAA;IACA;IACA,QACAkF,OAAA,WAAAA,QAAA;MACA,KAAAgF,KAAA;IACA;IACA,YACA8C,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5E,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA2E,SAAA;QAAA,IAAAC,WAAA,EAAAxH,GAAA;QAAA,WAAA2C,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA2E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzE,IAAA,GAAAyE,SAAA,CAAAxE,IAAA;YAAA;cACAoE,OAAA,CAAAxN,aAAA;cACA0N,WAAA;cAAA,IACAF,OAAA,CAAA1N,QAAA;gBAAA8N,SAAA,CAAAxE,IAAA;gBAAA;cAAA;cAAA,OAAAwE,SAAA,CAAAvE,MAAA,WACAmE,OAAA,CAAAjI,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAEAgP,OAAA,CAAA/M,QAAA,CAAA8L,EAAA;gBAAAqB,SAAA,CAAAxE,IAAA;gBAAA;cAAA;cACA;cACAsE,WAAA,CAAAG,IAAA;gBACA/N,QAAA,EAAA0N,OAAA,CAAA1N,QAAA;gBACAgO,MAAA,EAAAN,OAAA,CAAA/M,QAAA,CAAA8L,EAAA;gBACAwB,MAAA,EAAAP,OAAA,CAAA/M,QAAA,CAAA6B;cACA;cAAAsL,SAAA,CAAAxE,IAAA;cAAA;YAAA;cAAA,MAGAoE,OAAA,CAAAvN,aAAA;gBAAA2N,SAAA,CAAAxE,IAAA;gBAAA;cAAA;cAAA,OAAAwE,SAAA,CAAAvE,MAAA,WACAmE,OAAA,CAAAjI,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cACAgP,OAAA,CAAAvN,aAAA,CAAA+N,OAAA,WAAApI,IAAA;gBACA,IAAAqI,OAAA,GAAAT,OAAA,CAAArO,WAAA,CAAAwG,MAAA,WAAAyH,KAAA;kBAAA,OAAAA,KAAA,CAAAb,EAAA,IAAA3G,IAAA;gBAAA;gBACA8H,WAAA,CAAAG,IAAA;kBACA/N,QAAA,EAAA0N,OAAA,CAAA1N,QAAA;kBACAgO,MAAA,EAAAlI,IAAA;kBACAmI,MAAA,EAAAE,OAAA,IAAA3L;gBACA;cACA;YAAA;cAAAsL,SAAA,CAAAxE,IAAA;cAAA,OAEAjE,cAAA,CAAA+I,SAAA,CAAAR,WAAA;YAAA;cAAAxH,GAAA,GAAA0H,SAAA,CAAAlB,IAAA;cACA,IAAAxG,GAAA,CAAAZ,IAAA;gBACAkI,OAAA,CAAAjI,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAgP,OAAA,CAAA/C,KAAA;cACA;gBACA+C,OAAA,CAAAjI,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;cACA;cACAgP,OAAA,CAAA/M,QAAA;cACA+M,OAAA,CAAA1N,QAAA;cACA0N,OAAA,CAAAvN,aAAA;cACAuN,OAAA,CAAAtN,OAAA;YAAA;YAAA;cAAA,OAAA0N,SAAA,CAAAzD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IACA;IACA,YACAU,WAAA,WAAAA,YAAAvI,IAAA;MACA,KAAA5F,aAAA;MACA,KAAAS,QAAA,GAAAmF,IAAA;IACA;IACA,WACAwI,WAAA,WAAAA,YAAAxI,IAAA,EAAAyI,MAAA;MACA,IAAAA,MAAA;QACA,IAAAzI,IAAA,CAAAxD,WAAA;UACAuH,MAAA,CAAAlI,IAAA,CAAAmE,IAAA,CAAAxD,WAAA;UACA;QACA;QACA,KAAAmD,QAAA;UAAAvD,OAAA;QAAA;QACA;MACA;MACA2H,MAAA,CAAAlI,IAAA,uBAAAgG,MAAA,CACA7B,IAAA,CAAA2G,EAAA,aAAA9E,MAAA,CAAA7B,IAAA,CAAA0I,KAAA,kBAAA7G,MAAA,CAAA7B,IAAA,CAAApG,UAAA,GACA,QACA;MACA;MACA;IACA;IACA,WACA+O,UAAA,WAAAA,WAAA3I,IAAA;MACA,KAAAlF,SAAA;MACA,KAAAO,OAAA,GAAA2E,IAAA;MACA,IAAAA,IAAA,CAAA/E,QAAA;QACA,KAAAF,cAAA,CAAAE,QAAA,GAAA+E,IAAA,CAAA/E,QAAA,CACAqH,KAAA,MACAC,GAAA,WAAAxI,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,IAAAiG,IAAA,CAAA9E,MAAA;QACA,KAAAH,cAAA,CAAAG,MAAA,GAAA8E,IAAA,CAAA9E,MAAA,CACAoH,KAAA,MACAC,GAAA,WAAAxI,IAAA;UAAA,OAAAZ,MAAA,CAAAY,IAAA;QAAA;MACA;MACA,KAAAgB,cAAA,CAAAC,GAAA,GAAAgF,IAAA,CAAA4I,IAAA,GAAA5I,IAAA,CAAA4I,IAAA,CAAAtG,KAAA;IACA;IACA,aACAlC,UAAA,WAAAA,WAAA;MAAA,IAAAyI,OAAA;MAAA,WAAA7F,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA4F,SAAA;QAAA,WAAA7F,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA2F,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzF,IAAA,GAAAyF,SAAA,CAAAxF,IAAA;YAAA;cAAAwF,SAAA,CAAAxF,IAAA;cAAA,OACAjE,cAAA,CAAAjE,QAAA,GAAAmE,IAAA,WAAA1F,IAAA;gBACA,IAAAA,IAAA,CAAA2F,IAAA;kBACAmJ,OAAA,CAAAvN,QAAA,GAAAvB,IAAA,CAAAA,IAAA;kBACA8O,OAAA,CAAA1N,OAAA,GAAApB,IAAA,CAAAA,IAAA;kBACAwF,cAAA,CAAAtE,QAAA,GAAAwE,IAAA,WAAA+H,KAAA;oBACAqB,OAAA,CAAA5N,QAAA,GAAAuM,KAAA,CAAAzN,IAAA;oBACA8O,OAAA,CAAAzN,QAAA,GAAAoM,KAAA,CAAAzN,IAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAiP,SAAA,CAAAzE,IAAA;UAAA;QAAA,GAAAuE,QAAA;MAAA;IACA;IACA,UACAG,WAAA,WAAAA,YAAAxI,KAAA;MACA,KAAAtF,OAAA,QAAAG,QAAA,CAAAyE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAkJ,SAAA,IAAAzI,KAAA;MAAA;IACA;IACA,UACA0I,cAAA,WAAAA,eAAA1I,KAAA;MACA,KAAArF,QAAA,QAAAH,QAAA,CAAA8E,MAAA,CACA,UAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAoJ,YAAA,IAAA3I,KAAA;MAAA,CACA;IACA;IACA4I,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtG,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAqG,SAAA;QAAA,IAAAC,MAAA,EAAAlJ,GAAA;QAAA,WAAA2C,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAqG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnG,IAAA,GAAAmG,SAAA,CAAAlG,IAAA;YAAA;cACAgG,MAAA;gBACAtO,MAAA,EAAAvB,MAAA,CAAA2P,OAAA,CAAAvO,cAAA,CAAAG,MAAA;gBACAD,QAAA,EAAAtB,MAAA,CAAA2P,OAAA,CAAAvO,cAAA,CAAAE,QAAA;gBACA2N,IAAA,EAAAjP,MAAA,CAAA2P,OAAA,CAAAvO,cAAA,CAAAC,GAAA;gBACA2O,SAAA,EAAAhQ,MAAA,CAAA2P,OAAA,CAAAjO,OAAA,CAAAsL,EAAA;gBACA+B,KAAA,EAAAY,OAAA,CAAAjO,OAAA,CAAAqN,KAAA,GAAA/O,MAAA,CAAA2P,OAAA,CAAAjO,OAAA,CAAAqN,KAAA;cACA;cAAAgB,SAAA,CAAAlG,IAAA;cAAA,OACAjE,cAAA,CAAAqK,MAAA,CAAAJ,MAAA;YAAA;cAAAlJ,GAAA,GAAAoJ,SAAA,CAAA5C,IAAA;cACA,IAAAxG,GAAA,CAAAZ,IAAA;gBACA4J,OAAA,CAAA3J,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAmN,UAAA;kBACAuD,OAAA,CAAAzJ,OAAA;gBACA;cACA;gBACAyJ,OAAA,CAAA3J,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;cACA0Q,OAAA,CAAAO,QAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAnF,IAAA;UAAA;QAAA,GAAAgF,QAAA;MAAA;IACA;IACAM,QAAA,WAAAA,SAAA;MACA,KAAAxE,KAAA,aAAAyE,WAAA;MACA,KAAA/O,cAAA;QACAC,GAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAJ,SAAA;IACA;IACAiP,WAAA,WAAAA,YAAA/J,IAAA;MAAA,IAAAgK,OAAA;MAAA,WAAAhH,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA+G,SAAA;QAAA,IAAAC,SAAA,EAAA5J,GAAA;QAAA,WAAA2C,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA+G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7G,IAAA,GAAA6G,SAAA,CAAA5G,IAAA;YAAA;cACA0G,SAAA,GAAAG,IAAA,CAAAC,KAAA,CAAAtK,IAAA,CAAAkK,SAAA;cAAAE,SAAA,CAAA5G,IAAA;cAAA,OACAjE,cAAA,CAAAqK,MAAA;gBACAD,SAAA,EAAA3J,IAAA,CAAA2G,EAAA;gBACAuD,SAAA,IAAArR,OAAA,CAAAqR,SAAA;cACA;YAAA;cAHA5J,GAAA,GAAA8J,SAAA,CAAAtD,IAAA;cAIA,IAAAxG,GAAA,CAAAZ,IAAA;gBACAsK,OAAA,CAAArK,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;gBACAoR,OAAA,CAAAnK,OAAA;cACA;gBACAmK,OAAA,CAAArK,QAAA;kBAAAvD,OAAA;kBAAAxD,IAAA;gBAAA;cACA;YAAA;YAAA;cAAA,OAAAwR,SAAA,CAAA7F,IAAA;UAAA;QAAA,GAAA0F,QAAA;MAAA;IACA;IACAM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxH,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAuH,SAAA;QAAA,IAAA/B,KAAA;QAAA,WAAAzF,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAsH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApH,IAAA,GAAAoH,SAAA,CAAAnH,IAAA;YAAA;cACAkF,KAAA,GAAA8B,OAAA,CAAA9O,UAAA,CAAAgN,KAAA;cAAAiC,SAAA,CAAAnH,IAAA;cAAA,OACAjE,cAAA,CAAAqL,QAAA,CAAAJ,OAAA,CAAA9O,UAAA,CAAAiL,EAAA,EAAAlH,IAAA,WAAAa,GAAA;gBACA,IAAAA,GAAA,CAAAZ,IAAA;kBACA8K,OAAA,CAAA9O,UAAA,GAAA4E,GAAA,CAAAvG,IAAA;kBACAyQ,OAAA,CAAA9O,UAAA,CAAAgN,KAAA,GAAAA,KAAA;kBACA;kBACA8B,OAAA,CAAA5O,kBAAA,IAAA4O,OAAA,CAAA9O,UAAA,CAAAmP,SAAA;kBACA,IAAAL,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IAAAL,OAAA,CAAA9O,UAAA,CAAAW,OAAA;oBACAmO,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IACAL,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IAAAL,OAAA,CAAA9O,UAAA,CAAAW,OAAA,EACA4F,OAAA,mBAAA0B,CAAA,EAAAmH,CAAA,EAAAC,CAAA;sBACA;oBACA;oBACAP,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IACAL,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IAAAL,OAAA,CAAA9O,UAAA,CAAAW,OAAA,EACA4F,OAAA;oBACAuI,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IACAL,OAAA,CAAA9O,UAAA,CAAAmP,SAAA,IAAAL,OAAA,CAAA9O,UAAA,CAAAW,OAAA,EACA4F,OAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAA0I,SAAA,CAAApG,IAAA;UAAA;QAAA,GAAAkG,QAAA;MAAA;IACA;IACA,YACAO,cAAA,WAAAA,eAAAhL,IAAA;MAAA,IAAAiL,OAAA;MACA,IAAAC,GAAA;QACA9I,GAAA;MACA,IAAApC,IAAA,CAAA/E,QAAA;QACAiQ,GAAA,GAAAlL,IAAA,CAAA/E,QAAA,CAAAqH,KAAA;MACA;MACA4I,GAAA,CAAA9C,OAAA,WAAArO,IAAA;QACAkR,OAAA,CAAAhQ,QAAA,CAAAsH,GAAA,WAAA4I,GAAA;UACA,IAAAA,GAAA,CAAAxE,EAAA,IAAA5M,IAAA;YACA,IAAAqI,GAAA,IAAAgJ,SAAA;cACAhJ,GAAA;YACA;YACAA,GAAA,IAAA+I,GAAA,CAAA/B,YAAA;UACA;QACA;MACA;MACA,OAAAhH,GAAA;IACA;IACAiJ,YAAA,WAAAA,aAAArL,IAAA;MAAA,IAAAsL,OAAA;MACA,IAAAJ,GAAA;QACA9I,GAAA;MACA,IAAApC,IAAA,CAAA9E,MAAA;QACAgQ,GAAA,GAAAlL,IAAA,CAAA9E,MAAA,CAAAoH,KAAA;MACA;MACA4I,GAAA,CAAA9C,OAAA,WAAArO,IAAA;QACAuR,OAAA,CAAAhQ,QAAA,CAAAiH,GAAA,WAAA4I,GAAA;UACA,IAAAA,GAAA,CAAAxE,EAAA,IAAA5M,IAAA;YACA,IAAAqI,GAAA,IAAAgJ,SAAA;cACAhJ,GAAA;YACA;YACAA,GAAA,IAAA+I,GAAA,CAAAjC,SAAA;UACA;QACA;MACA;MACA,OAAA9G,GAAA;IACA;IACA,UACAmJ,WAAA,WAAAA,YAAAvL,IAAA;MAAA,IAAAwL,OAAA;MACA,IAAAxL,IAAA,8BAAA3F,aAAA,CAAAqI,MAAA;QACA,KAAA/C,QAAA,CAAA8L,OAAA;QACA;MACA;MACA,IAAAP,GAAA;MACA,IAAAQ,SAAA;MACA,IAAAzO,GAAA;MACA,IAAA+C,IAAA;QACAkL,GAAA,SAAAxP,UAAA,CAAAiL,EAAA;QACA,SAAAjL,UAAA,CAAAiQ,WAAA,EAAAD,SAAA;QACAzO,GAAA,QAAAvB,UAAA,CAAAiQ,WAAA;MACA,WAAA3L,IAAA;QACAkL,GAAA,QAAA7Q,aAAA;MACA;QACA6Q,GAAA,IAAAlL,IAAA,CAAA2G,EAAA;QACA,IAAA3G,IAAA,CAAA2L,WAAA,EAAAD,SAAA;QACAzO,GAAA,GAAA+C,IAAA,CAAA2L,WAAA;MACA;MACA,IAAAD,SAAA;QACA,SAAAlS,IAAA;UACA+F,cAAA,CAAAqM,gBAAA,CAAAV,GAAA,EACAzL,IAAA,WAAAiE,QAAA;YACA,IAAAA,QAAA,CAAAhE,IAAA;cACA8L,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAhT,OAAA,MAAAgT,gBAAA,CAAAhT,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACA2P,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAA7L,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACAkM,KAAA,WAAAwH,GAAA;QACA;UACA/M,cAAA,CAAAgN,mBAAA,CAAArB,GAAA,EACAzL,IAAA,WAAAiE,QAAA;YACA,IAAAA,QAAA,CAAAhE,IAAA;cACA8L,OAAA,CAAAK,OAAA,KAAAC,gBAAA,CAAAhT,OAAA,MAAAgT,gBAAA,CAAAhT,OAAA;gBACAqD,KAAA;gBACAC,OAAA;gBACA2P,gBAAA;gBACAC,iBAAA;gBACAC,gBAAA;cAAA,uBACA,uBACA,SAAAC,YAAAC,MAAA,EAAAC,QAAA,EAAAC,IAAA;gBACAA,IAAA;cACA,EACA;YACA;cACAb,OAAA,CAAA7L,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YACA;UACA,GACAkM,KAAA,WAAAwH,GAAA;QACA;MACA;QACArP,GAAA,GAAAA,GAAA,CAAAgF,OAAA,KAAAW,MAAA;QACA3F,GAAA,GAAAA,GAAA,CAAAgF,OAAA,KAAAW,MAAA;QACAmB,MAAA,CAAAlI,IAAA,CAAAkI,MAAA,CAAAyI,QAAA,CAAAC,MAAA,GAAAxP,GAAA;MACA;IACA;IACA,UACAyP,gBAAA,WAAAA,iBAAA1M,IAAA;MAAA,IAAA2M,OAAA;MAAA,WAAA3J,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA0J,SAAA;QAAA,IAAAC,IAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAC,KAAA;QAAA,WAAA/J,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA6J,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3J,IAAA,GAAA2J,UAAA,CAAA1J,IAAA;YAAA;cACAmJ,OAAA,CAAA3S,OAAA;cAAA,KACAgG,IAAA,CAAAmN,OAAA;gBAAAD,UAAA,CAAA1J,IAAA;gBAAA;cAAA;cACAqJ,IAAA,GAAA7M,IAAA,CAAAmN,OAAA,CAAA7K,KAAA;cAAAwK,SAAA,OAAAM,2BAAA,CAAAtU,OAAA,EACA+T,IAAA,CAAAQ,OAAA;cAAAH,UAAA,CAAA3J,IAAA;cAAAyJ,KAAA,oBAAA/J,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA8J,MAAA;gBAAA,IAAAM,WAAA,EAAAC,KAAA,EAAAtQ,GAAA;gBAAA,WAAAgG,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAoK,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAAlK,IAAA,GAAAkK,SAAA,CAAAjK,IAAA;oBAAA;sBAAA8J,WAAA,OAAAI,eAAA,CAAA5U,OAAA,EAAAiU,KAAA,CAAAvF,KAAA,MAAA+F,KAAA,GAAAD,WAAA,KAAArQ,GAAA,GAAAqQ,WAAA;sBACA,IAAArQ,GAAA,CAAA0Q,OAAA;wBACA5H,UAAA,kBAAA/C,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA0K,SAAA;0BAAA,WAAA3K,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAyK,UAAAC,SAAA;4BAAA,kBAAAA,SAAA,CAAAvK,IAAA,GAAAuK,SAAA,CAAAtK,IAAA;8BAAA;gCAAAsK,SAAA,CAAAtK,IAAA;gCAAA,OACAmJ,OAAA,CAAAoB,WAAA,CAAA9Q,GAAA,EAAAsQ,KAAA,EAAAvN,IAAA,CAAAzD,OAAA,IAAAyD,IAAA,CAAA7D,KAAA;8BAAA;8BAAA;gCAAA,OAAA2R,SAAA,CAAAvJ,IAAA;4BAAA;0BAAA,GAAAqJ,QAAA;wBAAA,CACA,IAAAL,KAAA;sBACA;wBACAZ,OAAA,CAAAhN,QAAA,CAAAqO,KAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAAP,SAAA,CAAAlJ,IAAA;kBAAA;gBAAA,GAAAyI,KAAA;cAAA;cAAAF,SAAA,CAAAmB,CAAA;YAAA;cAAA,KAAAlB,KAAA,GAAAD,SAAA,CAAAoB,CAAA,IAAA7B,IAAA;gBAAAa,UAAA,CAAA1J,IAAA;gBAAA;cAAA;cAAA,OAAA0J,UAAA,CAAAiB,aAAA,CAAAnB,KAAA;YAAA;cAAAE,UAAA,CAAA1J,IAAA;cAAA;YAAA;cAAA0J,UAAA,CAAA1J,IAAA;cAAA;YAAA;cAAA0J,UAAA,CAAA3J,IAAA;cAAA2J,UAAA,CAAAkB,EAAA,GAAAlB,UAAA;cAAAJ,SAAA,CAAAuB,CAAA,CAAAnB,UAAA,CAAAkB,EAAA;YAAA;cAAAlB,UAAA,CAAA3J,IAAA;cAAAuJ,SAAA,CAAAwB,CAAA;cAAA,OAAApB,UAAA,CAAAqB,MAAA;YAAA;cAGA5B,OAAA,CAAA3S,OAAA;YAAA;YAAA;cAAA,OAAAkT,UAAA,CAAA3I,IAAA;UAAA;QAAA,GAAAqI,QAAA;MAAA;IACA;IAEAmB,WAAA,WAAAA,YAAA9Q,GAAA,EAAAsQ,KAAA,EAAApR,KAAA;MAAA,IAAAqS,OAAA;MAAA,WAAAxL,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAuL,UAAA;QAAA,IAAAC,QAAA,EAAAhL,QAAA,EAAAiL,MAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;QAAA,WAAAhM,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA8L,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5L,IAAA,GAAA4L,UAAA,CAAA3L,IAAA;YAAA;cACAkL,QAAA,OAAAU,QAAA;cACAV,QAAA,CAAAW,MAAA,YAAApS,GAAA;cAAAkS,UAAA,CAAA5L,IAAA;cAAA4L,UAAA,CAAA3L,IAAA;cAAA,OAGAjE,cAAA,CAAA+P,YAAA,CAAAZ,QAAA;YAAA;cAAAhL,QAAA,GAAAyL,UAAA,CAAArI,IAAA;cACA6H,MAAA,OAAAY,mBAAA,EAAA7L,QAAA;cAAA,KAEAiL,MAAA;gBAAAQ,UAAA,CAAA3L,IAAA;gBAAA;cAAA;cACAoL,IAAA,OAAAY,IAAA,EAAA9L,QAAA;cACAmL,IAAA,GAAA5R,GAAA,CAAAqF,KAAA;cACAwM,QAAA,GAAAD,IAAA,CAAAA,IAAA,CAAAnM,MAAA;cACA,IAAA+M,iBAAA,EAAAb,IAAA,EAAAE,QAAA;cAAAK,UAAA,CAAA3L,IAAA;cAAA;YAAA;cAAA2L,UAAA,CAAA3L,IAAA;cAAA,OAEAE,QAAA,CAAAlJ,IAAA;YAAA;cAAAuU,OAAA,GAAAI,UAAA,CAAArI,IAAA;cACAkI,MAAA,GAAA3E,IAAA,CAAAC,KAAA,CAAAyE,OAAA;cACAE,MAAA,GACAS,SAAA,CAAAV,MAAA,CAAAtP,IAAA,KAAAsP,MAAA,CAAArO,GAAA,IAAA+O,SAAA;cACAlB,OAAA,CAAA7O,QAAA,CAAAqO,KAAA,CAAAiB,MAAA;YAAA;cAAAE,UAAA,CAAA3L,IAAA;cAAA;YAAA;cAAA2L,UAAA,CAAA5L,IAAA;cAAA4L,UAAA,CAAAQ,EAAA,GAAAR,UAAA;YAAA;cAAAA,UAAA,CAAA5L,IAAA;cAKA;cACAiL,OAAA,CAAAxU,OAAA;cAAA,OAAAmV,UAAA,CAAAZ,MAAA;YAAA;YAAA;cAAA,OAAAY,UAAA,CAAA5K,IAAA;UAAA;QAAA,GAAAkK,SAAA;MAAA;IAoCA;IACA;IACAmB,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,IAAA9V,OAAA,QAAA+V,QAAA;QACAC,IAAA;QACAxV,IAAA;QACAyV,OAAA;QACAC,UAAA;MACA;MACA3Q,cAAA,CAAA4Q,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAA1T,KAAA;QACAuM,KAAA,EAAAmH,GAAA,CAAAnH,KAAA;QACA/B,EAAA,EAAAkJ,GAAA,CAAAlJ,EAAA;QACA0J,gBAAA;QACAC,eAAA;MACA,GACA7Q,IAAA,WAAAa,GAAA;QACAwP,OAAA,CAAApU,UAAA,CAAAa,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACA+V,OAAA,CAAAvW,WAAA,CACAuW,OAAA,CAAAvW,WAAA,CAAAgX,SAAA,WAAA/I,KAAA;UAAA,OAAAA,KAAA,CAAAb,EAAA,IAAAkJ,GAAA,CAAAlJ,EAAA;QAAA,GACA,CAAApK,OAAA,GAAA+D,GAAA,CAAAvG,IAAA;QACA+V,OAAA,CAAAvW,WAAA,CACAuW,OAAA,CAAAvW,WAAA,CAAAgX,SAAA,WAAA/I,KAAA;UAAA,OAAAA,KAAA,CAAAb,EAAA,IAAAkJ,GAAA,CAAAlJ,EAAA;QAAA,GACA,CAAA6J,YAAA;QACAxW,OAAA,CAAAyW,KAAA;MACA,GACA3L,KAAA,WAAAwH,GAAA;QACAtS,OAAA,CAAAyW,KAAA;MACA;IACA;IACA;IACAC,cAAA,WAAAA,eAAAb,GAAA;MAAA,IAAAc,OAAA;MACA,IAAA3W,OAAA,QAAA+V,QAAA;QACAC,IAAA;QACAxV,IAAA;QACAyV,OAAA;QACAC,UAAA;MACA;MACA3Q,cAAA,CAAA4Q,gBAAA;QACAC,YAAA,EAAAP,GAAA,CAAAxT,OAAA;QACAqM,KAAA,EAAAmH,GAAA,CAAAnH,KAAA;QACA/B,EAAA,EAAAkJ,GAAA,CAAAlJ,EAAA;QACA0J,gBAAA;QACAC,eAAA;MACA,GACA7Q,IAAA,WAAAa,GAAA;QACAqQ,OAAA,CAAAjV,UAAA,CAAAmP,SAAA,GAAAvK,GAAA,CAAAvG,IAAA;QACA4W,OAAA,CAAApX,WAAA,CACAoX,OAAA,CAAApX,WAAA,CAAAgX,SAAA,WAAA/I,KAAA;UAAA,OAAAA,KAAA,CAAAb,EAAA,IAAAkJ,GAAA,CAAAlJ,EAAA;QAAA,GACA,CAAAkE,SAAA,GAAAvK,GAAA,CAAAvG,IAAA;QACA4W,OAAA,CAAApX,WAAA,CACAoX,OAAA,CAAApX,WAAA,CAAAgX,SAAA,WAAA/I,KAAA;UAAA,OAAAA,KAAA,CAAAb,EAAA,IAAAkJ,GAAA,CAAAlJ,EAAA;QAAA,GACA,CAAA6J,YAAA;QACAG,OAAA,CAAA/U,kBAAA;QACA5B,OAAA,CAAAyW,KAAA;MACA,GACA3L,KAAA,WAAAwH,GAAA;QACAtS,OAAA,CAAAyW,KAAA;MACA;IACA;IACA,aACAG,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,OAAA;MACA,KAAAC,KAAA;MACAvR,cAAA,CAAAqL,QAAA,CAAAiF,GAAA,CAAAlJ,EAAA,EAAAlH,IAAA,WAAAiE,QAAA;QACAmN,OAAA,CAAA5U,IAAA,GAAAyH,QAAA,CAAA3J,IAAA;QACA8W,OAAA,CAAA5U,IAAA,CAAArC,UAAA,GAAAT,MAAA,CAAA0X,OAAA,CAAA5U,IAAA,CAAArC,UAAA;QACAiX,OAAA,CAAA5U,IAAA,CAAAyM,KAAA,GAAAmH,GAAA,CAAAnH,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAmI,OAAA,CAAAhV,IAAA;MACA;IACA;IACA,aACAkV,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,OAAA;MACA,KAAAC,MAAA,CACAC,OAAA,iBACAzR,IAAA;QACA,OAAAF,cAAA,CAAA4R,kBAAA;UAAAxK,EAAA,EAAAkJ,GAAA,CAAAlJ,EAAA;UAAA+B,KAAA,EAAAmH,GAAA,CAAAnH;QAAA;MACA,GACAjJ,IAAA;QACAuR,OAAA,CAAAnR,OAAA;QACAmR,OAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GACAtM,KAAA;IACA;IACA,WACAuM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAjM,KAAA,SAAAkM,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,SAAA,GAAApH,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAqH,SAAA,CAAAJ,OAAA,CAAArV,IAAA;UACA;UACA;UACA,IAAA0V,qBAAA,EAAAF,SAAA,EAAAhS,IAAA,WAAAiE,QAAA;YACA4N,OAAA,CAAAL,MAAA,CAAAG,UAAA;YACAE,OAAA,CAAAzV,IAAA;YACAyV,OAAA,CAAAzR,OAAA;UACA;QACA;MACA;IACA;IACA,WACA+R,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9O,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAA6O,UAAA;QAAA,IAAAhY,IAAA;QAAA,WAAAkJ,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA4O,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1O,IAAA,GAAA0O,UAAA,CAAAzO,IAAA;YAAA;cACAzJ,IAAA,OAAAqV,QAAA;cACArV,IAAA,CAAAsV,MAAA,UAAAwC,IAAA,CAAAA,IAAA;cAAAI,UAAA,CAAAzO,IAAA;cAAA,OACA,IAAA0O,iBAAA,EAAAnY,IAAA,EAAA0F,IAAA,WAAAiE,QAAA;gBACA,IAAAA,QAAA,CAAAhE,IAAA;kBACAoS,OAAA,CAAAzU,QAAA,CAAAkF,GAAA,WAAAvC,IAAA;oBACA,IAAAA,IAAA,CAAAmS,GAAA,IAAAN,IAAA,CAAAA,IAAA,CAAAM,GAAA;sBACAnS,IAAA,CAAAoS,IAAA,GAAA1O,QAAA,CAAA2O,MAAA;oBACA;kBACA;kBACAP,OAAA,CAAAnS,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;kBACAkZ,OAAA,CAAAnS,QAAA;oBAAAvD,OAAA;oBAAAxD,IAAA;kBAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAqZ,UAAA,CAAA1N,IAAA;UAAA;QAAA,GAAAwN,SAAA;MAAA;IACA;IACA,YACAO,MAAA,WAAAA,OAAA;MACA,KAAA3S,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA,UACA2Z,YAAA,WAAAA,aAAAV,IAAA;MACA,KAAAxU,QAAA,QAAAA,QAAA,CAAA0C,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAA6R,IAAA;MAAA;IACA;IACA;IACAW,YAAA,WAAAA,aAAAX,IAAA,EAAAxU,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAgI,KAAA,CAAAoN,MAAA,CAAAC,MAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAd,IAAA;MACA;MACA,IAAA/C,QAAA,GAAA+C,IAAA,CAAAe,IAAA,CACAC,SAAA,CAAAhB,IAAA,CAAAe,IAAA,CAAAE,WAAA,WACAC,WAAA;QACAC,SAAA,GACAlE,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,aACAA,QAAA,cACAA,QAAA,cACAA,QAAA;MACA,IAAAmE,QAAA,GAAApB,IAAA,CAAAqB,IAAA;MACA,KAAAF,SAAA;QACA,KAAAG,OAAA;UACAhX,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA;MACA,KAAAqa,QAAA;QACA,KAAAE,OAAA;UACAhX,KAAA;UACAC,OAAA;UACAxD,IAAA;QACA;MACA;MACA,OAAAoa,SAAA,IAAAC,QAAA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAA9S,GAAA,EAAAuR,IAAA;MACA,KAAAlS,QAAA;QAAAvD,OAAA;QAAAxD,IAAA;MAAA;IACA;IACA;IACAya,eAAA,WAAAA,gBAAA;MACA,KAAA1T,QAAA;QACAvD,OAAA;QACAxD,IAAA;MACA;IACA;IACA;IACA0a,gBAAA,WAAAA,iBAAAzB,IAAA;MAAA,IAAA0B,OAAA;MACA,SAAAtX,IAAA,CAAAO,WAAA,iBAAAP,IAAA,CAAAO,WAAA;QACA,IACA,KAAAP,IAAA,CAAAO,WAAA,CAAAiF,KAAA,CACA,mFACA,GACA;UACA,IAAA1H,IAAA,OAAAqV,QAAA;UACArV,IAAA,CAAAsV,MAAA,SAAAwC,IAAA,CAAAA,IAAA;UACA9X,IAAA,CAAAsV,MAAA,qBAAApT,IAAA,CAAAO,WAAA;UAEA+C,cAAA,CAAAiU,UAAA,CAAAzZ,IAAA,EAAA0F,IAAA,WAAAiE,QAAA;YACA,IAAAA,QAAA,CAAAhE,IAAA;cACA6T,OAAA,CAAA5T,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACA2a,OAAA,CAAAtX,IAAA,CAAAkR,OAAA,GAAAzJ,QAAA,CAAA3J,IAAA;YACA;cACAwZ,OAAA,CAAA5T,QAAA;gBAAAvD,OAAA;gBAAAxD,IAAA;cAAA;cACA2a,OAAA,CAAAtX,IAAA,CAAAkR,OAAA;YACA;UACA;QACA;UACA,KAAAxN,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA,KAAA0E,WAAA;QACA;MACA;QACA,KAAAqC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA,KAAA0E,WAAA;MACA;IACA;IACA;IACAmW,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACAnU,cAAA,CAAAoU,UAAA;QAAAC,QAAA,OAAA3X,IAAA,CAAAkR;MAAA,GAAA1N,IAAA,WAAAiE,QAAA;QACA,IAAAA,QAAA,CAAAhE,IAAA;UACAgU,OAAA,CAAA/T,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;UACA8a,OAAA,CAAApW,WAAA;UACAoW,OAAA,CAAAzX,IAAA,CAAAkR,OAAA;QACA;UACAuG,OAAA,CAAA/T,QAAA;YAAAvD,OAAA;YAAAxD,IAAA;UAAA;QACA;MACA;IACA;IACA;IACAib,MAAA,WAAAA,OAAA;MACA,KAAAhY,IAAA;MACA,KAAAiV,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7U,IAAA;QACA0K,EAAA;QACAmN,SAAA;QACA3X,KAAA;QACAI,OAAA;QACA3C,UAAA;QACAma,UAAA;QACAC,QAAA;QACAxX,WAAA;QACAyX,QAAA;QACAC,MAAA;QACAC,WAAA;QACA1X,OAAA;QACA2X,SAAA;QACAC,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAlY,WAAA;QACAmY,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,cAAA;QACAC,OAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAjZ,OAAA;QACAwO,SAAA;QACAsC,OAAA;QACAlS,QAAA;QACAC,MAAA;QACAqa,MAAA;QACAC,QAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAnY,kBAAA;MACA,KAAAC,gBAAA;IACA;IACA;IACAmY,gBAAA,WAAAA,iBAAAhE,IAAA,EAAAxU,QAAA;MACA;MACA,IAAAyY,QAAA,GAAAzY,QAAA,CAAAkF,GAAA,WAAAvC,IAAA;QAAA;UACA8O,QAAA,EAAA9O,IAAA,CAAA4S,IAAA;UACAf,IAAA,EAAA7R,IAAA,CAAA+V,GAAA;UACAhC,UAAA;QACA;MAAA;MACA,KAAArW,gBAAA,GAAAoY,QAAA;IACA;IACA;IACAnC,UAAA,WAAAA,WAAApG,KAAA;MACA,KAAA7P,gBAAA,CAAAsY,MAAA,CAAAzI,KAAA;IACA;IACA;IACA0I,iBAAA,WAAAA,kBAAA;MACA,KAAAxY,kBAAA;MACA,KAAAC,gBAAA;MACA;MACA,KAAA2H,KAAA,CAAA6Q,WAAA,CAAAC,UAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAArT,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAoT,UAAA;QAAA,IAAAC,gBAAA,EAAA7H,QAAA,EAAA8H,WAAA,EAAA9S,QAAA;QAAA,WAAAT,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAqT,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnT,IAAA,GAAAmT,UAAA,CAAAlT,IAAA;YAAA;cACA;cACA+S,gBAAA,GAAAF,OAAA,CAAA3Y,gBAAA,CAAAqC,MAAA,CACA,UAAAC,IAAA;gBAAA,QAAAA,IAAA,CAAA+T,UAAA,CAAA4C,IAAA;cAAA,CACA;cAAA,MACAJ,gBAAA,CAAA7T,MAAA;gBAAAgU,UAAA,CAAAlT,IAAA;gBAAA;cAAA;cACA6S,OAAA,CAAA1W,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;cAAA,OAAA8d,UAAA,CAAAjT,MAAA;YAAA;cAAAiT,UAAA,CAAAnT,IAAA;cAKA8S,OAAA,CAAArc,OAAA;;cAEA;cACA0U,QAAA,OAAAU,QAAA,IAEA;cACAiH,OAAA,CAAA3Y,gBAAA,CAAA0K,OAAA,WAAApI,IAAA;gBACA0O,QAAA,CAAAW,MAAA,UAAArP,IAAA,CAAA6R,IAAA;cACA;;cAEA;cACA2E,WAAA,GAAAH,OAAA,CAAA3Y,gBAAA,CACA6E,GAAA,WAAAvC,IAAA;gBAAA,OAAAA,IAAA,CAAA+T,UAAA;cAAA,GACAnP,IAAA;cAEA8J,QAAA,CAAAW,MAAA,gBAAAmH,WAAA;;cAEA;cAAAE,UAAA,CAAAlT,IAAA;cAAA,OACAjE,cAAA,CAAAqX,kBAAA,CAAAlI,QAAA;YAAA;cAAAhL,QAAA,GAAAgT,UAAA,CAAA5P,IAAA;cAEA,IAAApD,QAAA,CAAAhE,IAAA;gBACA2W,OAAA,CAAA1W,QAAA;kBACAvD,OAAA;kBACAxD,IAAA;gBACA;gBACAyd,OAAA,CAAA5Y,kBAAA;gBACA4Y,OAAA,CAAA3Y,gBAAA;gBACA2Y,OAAA,CAAAhR,KAAA,CAAA6Q,WAAA,CAAAC,UAAA;gBACA;gBACAE,OAAA,CAAAxW,OAAA;cACA;gBACAwW,OAAA,CAAA1W,QAAA;kBACAvD,OAAA,EAAAsH,QAAA,CAAA/C,GAAA;kBACA/H,IAAA;gBACA;cACA;cAAA8d,UAAA,CAAAlT,IAAA;cAAA;YAAA;cAAAkT,UAAA,CAAAnT,IAAA;cAAAmT,UAAA,CAAA/G,EAAA,GAAA+G,UAAA;cAEAG,OAAA,CAAA7I,KAAA,YAAA0I,UAAA,CAAA/G,EAAA;cACA0G,OAAA,CAAA1W,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA8d,UAAA,CAAAnT,IAAA;cAEA8S,OAAA,CAAArc,OAAA;cAAA,OAAA0c,UAAA,CAAAnI,MAAA;YAAA;YAAA;cAAA,OAAAmI,UAAA,CAAAnS,IAAA;UAAA;QAAA,GAAA+R,SAAA;MAAA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MACA,KAAAnZ,kBAAA;MACA,SAAAtD,aAAA,CAAAqI,MAAA;QACA,KAAA/C,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;MAEA,SAAAyB,aAAA,CAAAqI,MAAA;QACA,KAAA/C,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;QACA;MACA;;MAEA;MACA,IAAAme,iBAAA,QAAA1c,aAAA;MACA,IAAA2c,eAAA,QAAAzd,WAAA,CAAA0d,IAAA,CACA,UAAAjX,IAAA;QAAA,OAAAA,IAAA,CAAA2G,EAAA,KAAAoQ,iBAAA;MAAA,CACA;MAEA,IAAAC,eAAA;QACA,KAAApZ,cAAA,GAAAoZ,eAAA;QACA,KAAArZ,kBAAA;MACA;QACA,KAAAgC,QAAA;UAAAvD,OAAA;UAAAxD,IAAA;QAAA;MACA;IACA;IACA;IACA;IACAse,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnU,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAkU,UAAA;QAAA,IAAAC,qBAAA,EAAAC,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,MAAA,EAAAjU,QAAA,EAAAkU,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,aAAA,EAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,kBAAA,EAAA9L,IAAA,EAAA7E,KAAA,EAAA4Q,QAAA,EAAAC,aAAA,EAAAC,KAAA,EAAAC,YAAA,EAAAC,IAAA,EAAAze,IAAA,EAAA0e,QAAA,EAAAC,MAAA;QAAA,WAAAzV,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAuV,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArV,IAAA,GAAAqV,UAAA,CAAApV,IAAA;YAAA;cAAA,MACA2T,OAAA,CAAA9c,aAAA,CAAAqI,MAAA;gBAAAkW,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,OAAAoV,UAAA,CAAAnV,MAAA,WACA0T,OAAA,CAAAxX,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAue,OAAA,CAAAtY,YAAA;gBAAA+Z,UAAA,CAAApV,IAAA;gBAAA;cAAA;cACA2T,OAAA,CAAArY,SAAA;cAAA,KACAqY,OAAA,CAAApY,aAAA;gBAAA6Z,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAAoV,UAAA,CAAArV,IAAA;cAAAqV,UAAA,CAAApV,IAAA;cAAA,OAEA2T,OAAA,CAAApY,aAAA,CAAA8U,MAAA;YAAA;cAAA+E,UAAA,CAAApV,IAAA;cAAA;YAAA;cAAAoV,UAAA,CAAArV,IAAA;cAAAqV,UAAA,CAAAjJ,EAAA,GAAAiJ,UAAA;cAEA/B,OAAA,CAAAgC,GAAA,sOAAAD,UAAA,CAAAjJ,EAAA;YAAA;cAAAiJ,UAAA,CAAApV,IAAA;cAAA,OAGA,IAAAsV,OAAA,WAAAC,OAAA;gBAAA,OAAAhT,UAAA,CAAAgT,OAAA;cAAA;YAAA;cAGA5B,OAAA,CAAAtY,YAAA;cACAsY,OAAA,CAAArY,SAAA;cACAqY,OAAA,CAAAtZ,eAAA;cACAsZ,OAAA,CAAArZ,YAAA;cACAqZ,OAAA,CAAApZ,UAAA;cAAA6a,UAAA,CAAArV,IAAA;cAGA;cACA+T,gBAAA,GAAAH,OAAA,CAAA5d,WAAA,CAAAwG,MAAA,WAAAsI,OAAA;gBAAA,OACA8O,OAAA,CAAA9c,aAAA,CAAAkH,QAAA,CAAA8G,OAAA,CAAA1B,EAAA;cAAA,CACA;cACA4Q,MAAA,GAAAD,gBAAA,CACA/U,GAAA,WAAA8F,OAAA;gBAAA,gBAAAxG,MAAA,CAAAwG,OAAA,CAAA9L,OAAA,IAAA8L,OAAA,CAAAlM,KAAA;cAAA,GACAyI,IAAA,QAEA;cAAAgU,UAAA,CAAApV,IAAA;cAAA,OACA,IAAAwV,4BAAA,EACA7B,OAAA,CAAA9c,aAAA,CAAAuK,IAAA,KACA;YAAA;cAFA4S,gBAAA,GAAAoB,UAAA,CAAA9R,IAAA;cAAA,KAAAuQ,qBAAA,GAGAG,gBAAA,CAAAzd,IAAA,cAAAsd,qBAAA,eAAAA,qBAAA,CAAA3U,MAAA;gBAAAkW,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAzd,IAAA,CACAwI,GAAA,WAAA8F,OAAA,EAAAkF,KAAA;gBAAA,IAAA2L,qBAAA,EAAAC,sBAAA;gBACA,IAAAhd,KAAA,GACA,EAAA+c,qBAAA,GAAA5B,gBAAA,CAAA/J,KAAA,eAAA2L,qBAAA,uBAAAA,qBAAA,CAAA3c,OAAA,OAAA4c,sBAAA,GACA7B,gBAAA,CAAA/J,KAAA,eAAA4L,sBAAA,uBAAAA,sBAAA,CAAAhd,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgM,OAAA,CAAAhM,OAAA;gBACA,uBAAAwF,MAAA,CAAA0L,KAAA,yCAAA1L,MAAA,CAAA1F,KAAA,gBAAA0F,MAAA,CAAAxF,OAAA;cACA,GACAuI,IAAA,yDAEA;cACAuS,OAAA,CAAArZ,YAAA,CAAAmK,IAAA;gBACAmR,IAAA;gBACA/c,OAAA,qDAAAwF,MAAA,CAAAsV,OAAA,CAAA9c,aAAA,CAAAqI,MAAA,gCAAAb,MAAA,CAAA0V,MAAA;cACA;;cAEA;cACAG,SAAA;gBACA0B,IAAA;gBACA/c,OAAA;cACA;cACA8a,OAAA,CAAArZ,YAAA,CAAAmK,IAAA,CAAAyP,SAAA;;cAEA;cACAC,MAAA,GACAR,OAAA,CAAAlY,eAAA,CACAgD,OAAA,kBAAAkV,OAAA,CAAA9c,aAAA,CAAAqI,MAAA,EACAT,OAAA,yFAAAJ,MAAA,CACA4V,eAAA,GAEA;cAAAmB,UAAA,CAAApV,IAAA;cAAA,OACA,IAAA6V,YAAA,EACA5B,eAAA,EACA,aACA,qBACA;YAAA;cAJA/T,QAAA,GAAAkV,UAAA,CAAA9R,IAAA;cAAA,IAKApD,QAAA,CAAA4V,EAAA;gBAAAV,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAAlU,QAAA,CAAA6V,IAAA,CAAAC,SAAA;cACArC,OAAA,CAAApY,aAAA,GAAA6Y,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACAC,aAAA;cACAC,YAAA;cAEA;cACAC,aAAA,YAAAA,cAAA7V,GAAA;gBACA,OAAAA,GAAA,CAAAH,OAAA,gCAAAR,KAAA;kBACA,OAAA9H,MAAA,CAAA+f,YAAA,CAAAC,QAAA,CAAAlY,KAAA,CAAAQ,OAAA;gBACA;cACA,GAEA;cACAiW,aAAA,YAAAA,cAAA0B,UAAA;gBACA;kBACA,IAAAC,eAAA,OAAAC,cAAA,EAAAF,UAAA,EAAAzC,OAAA,CAAAjZ,eAAA;kBACAwZ,SAAA,CAAArb,OAAA,GAAAwd,eAAA;;kBAEA;kBACA1C,OAAA,CAAA4C,SAAA;oBACA,IAAAjc,YAAA,GAAAqZ,OAAA,CAAA9R,KAAA,CAAAvH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAyH,SAAA,GAAAzH,YAAA,CAAAkc,YAAA;oBACA;kBACA;gBACA,SAAAhM,KAAA;kBACA6I,OAAA,CAAA7I,KAAA,aAAAA,KAAA;gBACA;cACA,GAEA;YAAA;cAAA,KACA;gBAAA4K,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,KAEA2T,OAAA,CAAArY,SAAA;gBAAA8Z,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAAAL,UAAA,CAAApV,IAAA;cAAA,OAGAoU,MAAA,CAAAqC,IAAA;YAAA;cAAA9B,kBAAA,GAAAS,UAAA,CAAA9R,IAAA;cAAAuF,IAAA,GAAA8L,kBAAA,CAAA9L,IAAA;cAAA7E,KAAA,GAAA2Q,kBAAA,CAAA3Q,KAAA;cAAA,KAEA6E,IAAA;gBAAAuM,UAAA,CAAApV,IAAA;gBAAA;cAAA;cACA;cACA,IAAAuU,aAAA;gBACA;kBACAK,QAAA,GAAA/N,IAAA,CAAAC,KAAA,CAAAyN,aAAA;kBACA,IAAAK,QAAA,CAAAM,MAAA;oBACA;oBACAL,aAAA,GAAAJ,aAAA,CAAAG,QAAA,CAAAM,MAAA;oBACAZ,MAAA,IAAAO,aAAA;oBACAH,aAAA,CAAAJ,MAAA;kBACA;gBACA,SAAAzJ,CAAA;kBACAwI,OAAA,CAAAqD,IAAA,gBAAA7L,CAAA;gBACA;cACA;cAAA,OAAAuK,UAAA,CAAAnV,MAAA;YAAA;cAIA6U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA3S,KAAA;cACAuQ,aAAA,IAAAO,KAAA;;cAEA;YAAA;cAAA,KACAP,aAAA,CAAAxW,QAAA;gBAAAqX,UAAA,CAAApV,IAAA;gBAAA;cAAA;cACA+U,YAAA,GAAAR,aAAA,CAAApK,OAAA;cACA6K,IAAA,GAAAT,aAAA,CAAAqC,KAAA,IAAA7B,YAAA,EAAA5B,IAAA;cACAoB,aAAA,GAAAA,aAAA,CAAAqC,KAAA,CAAA7B,YAAA;cAAA,MAEA,CAAAC,IAAA,IAAAA,IAAA,iBAAAA,IAAA,CAAA6B,UAAA;gBAAAzB,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,OAAAoV,UAAA,CAAAnV,MAAA;YAAA;cAAAmV,UAAA,CAAArV,IAAA;cAKAxJ,IAAA,GAAAye,IAAA,CAAA4B,KAAA,IAAAzD,IAAA;cAAA,MACA5c,IAAA;gBAAA6e,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,OAAAoV,UAAA,CAAAnV,MAAA;YAAA;cAIAgV,QAAA,GAAApO,IAAA,CAAAC,KAAA,CAAAvQ,IAAA;cAAA,IACA0e,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,OAAAoV,UAAA,CAAAnV,MAAA;YAAA;cAAA,MAKAgV,QAAA,CAAAC,MAAA,cAAAD,QAAA,CAAAC,MAAA;gBAAAE,UAAA,CAAApV,IAAA;gBAAA;cAAA;cAAA,OAAAoV,UAAA,CAAAnV,MAAA;YAAA;cAIA;cACAiV,MAAA,GAAAT,aAAA,CAAAQ,QAAA,CAAAC,MAAA,GAEA;cAAA,KACAA,MAAA,CAAAnX,QAAA;gBAAAqX,UAAA,CAAApV,IAAA;gBAAA;cAAA;cACAwU,YAAA;cAAA,OAAAY,UAAA,CAAAnV,MAAA;YAAA;cAAA,KAKAiV,MAAA,CAAAnX,QAAA;gBAAAqX,UAAA,CAAApV,IAAA;gBAAA;cAAA;cACAwU,YAAA;cAAA,OAAAY,UAAA,CAAAnV,MAAA;YAAA;cAIA;cACA,KAAAuU,YAAA,IAAAU,MAAA;gBACAZ,MAAA,IAAAY,MAAA;gBACAR,aAAA,CAAAJ,MAAA;cACA;cAAAc,UAAA,CAAApV,IAAA;cAAA;YAAA;cAAAoV,UAAA,CAAArV,IAAA;cAAAqV,UAAA,CAAAxK,EAAA,GAAAwK,UAAA;cAEA/B,OAAA,CAAAqD,IAAA;gBACA1B,IAAA,EAAAA,IAAA;gBACAxK,KAAA,EAAA4K,UAAA,CAAAxK,EAAA,CAAAhS,OAAA;gBACA2b,aAAA,EAAAA;cACA;cAAA,OAAAa,UAAA,CAAAnV,MAAA;YAAA;cAAAmV,UAAA,CAAApV,IAAA;cAAA;YAAA;cAAAoV,UAAA,CAAApV,IAAA;cAAA;YAAA;cAAAoV,UAAA,CAAApV,IAAA;cAAA;YAAA;cAAAoV,UAAA,CAAArV,IAAA;cAAAqV,UAAA,CAAA0B,EAAA,GAAA1B,UAAA;cAMA/B,OAAA,CAAA7I,KAAA,YAAA4K,UAAA,CAAA0B,EAAA;cACAnD,OAAA,CAAAxX,QAAA,CAAAqO,KAAA,CAAA4K,UAAA,CAAA0B,EAAA,CAAAle,OAAA;cACA,IAAA+a,OAAA,CAAArZ,YAAA;gBACAqZ,OAAA,CAAArZ,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAuc,UAAA,CAAArV,IAAA;cAEA4T,OAAA,CAAApY,aAAA;cACA,IAAAoY,OAAA,CAAAtZ,eAAA;gBACAsZ,OAAA,CAAApZ,UAAA;gBACAoZ,OAAA,CAAAtY,YAAA;cACA;cAAA,OAAA+Z,UAAA,CAAArK,MAAA;YAAA;YAAA;cAAA,OAAAqK,UAAA,CAAArU,IAAA;UAAA;QAAA,GAAA6S,SAAA;MAAA;IAEA;IACA;IACAmD,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxX,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAuX,UAAA;QAAA,IAAAC,sBAAA,EAAApD,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAkD,UAAA,EAAAhD,MAAA,EAAAjU,QAAA,EAAAkU,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA8C,cAAA,EAAAC,cAAA,EAAAC,UAAA,EAAA5C,aAAA,EAAA6C,aAAA;QAAA,WAAA9X,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA4X,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1X,IAAA,GAAA0X,UAAA,CAAAzX,IAAA;YAAA;cAAA,MACAgX,OAAA,CAAAngB,aAAA,CAAAqI,MAAA;gBAAAuY,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAA,OAAAyX,UAAA,CAAAxX,MAAA,WACA+W,OAAA,CAAA7a,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIA4hB,OAAA,CAAA3b,YAAA;gBAAAoc,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACAgX,OAAA,CAAA1b,SAAA;cAAA,KACA0b,OAAA,CAAAzb,aAAA;gBAAAkc,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAAzX,IAAA;cAAA,OAEAgX,OAAA,CAAAzb,aAAA,CAAA8U,MAAA;YAAA;cAAAoH,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAAtL,EAAA,GAAAsL,UAAA;cAEApE,OAAA,CAAAgC,GAAA,sOAAAoC,UAAA,CAAAtL,EAAA;YAAA;cAAAsL,UAAA,CAAAzX,IAAA;cAAA,OAIA,IAAAsV,OAAA,WAAAC,OAAA;gBAAA,OAAAhT,UAAA,CAAAgT,OAAA;cAAA;YAAA;cAGAyB,OAAA,CAAA3b,YAAA;cACA2b,OAAA,CAAA1b,SAAA;cACA0b,OAAA,CAAA3c,eAAA;cACA2c,OAAA,CAAA1c,YAAA;cACA0c,OAAA,CAAAzc,UAAA;cAAAkd,UAAA,CAAA1X,IAAA;cAGA;cACA+T,gBAAA,GAAAkD,OAAA,CAAAjhB,WAAA,CAAAwG,MAAA,WAAAsI,OAAA;gBAAA,OACAmS,OAAA,CAAAngB,aAAA,CAAAkH,QAAA,CAAA8G,OAAA,CAAA1B,EAAA;cAAA,CACA;cACA4Q,MAAA,GAAAD,gBAAA,CACA/U,GAAA,WAAA8F,OAAA;gBAAA,gBAAAxG,MAAA,CAAAwG,OAAA,CAAA9L,OAAA,IAAA8L,OAAA,CAAAlM,KAAA;cAAA,GACAyI,IAAA,QAEA;cAAAqW,UAAA,CAAAzX,IAAA;cAAA,OACA,IAAAwV,4BAAA,EACAwB,OAAA,CAAAngB,aAAA,CAAAuK,IAAA,KACA;YAAA;cAFA4S,gBAAA,GAAAyD,UAAA,CAAAnU,IAAA;cAAA,KAAA4T,sBAAA,GAGAlD,gBAAA,CAAAzd,IAAA,cAAA2gB,sBAAA,eAAAA,sBAAA,CAAAhY,MAAA;gBAAAuY,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAGA;cACAxB,eAAA,GAAAD,gBAAA,CAAAzd,IAAA,CACAwI,GAAA,WAAA8F,OAAA,EAAAkF,KAAA;gBAAA,IAAA2N,sBAAA,EAAAC,sBAAA;gBACA,IAAAhf,KAAA,GACA,EAAA+e,sBAAA,GAAA5D,gBAAA,CAAA/J,KAAA,eAAA2N,sBAAA,uBAAAA,sBAAA,CAAA3e,OAAA,OAAA4e,sBAAA,GACA7D,gBAAA,CAAA/J,KAAA,eAAA4N,sBAAA,uBAAAA,sBAAA,CAAAhf,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgM,OAAA,CAAAhM,OAAA;gBACA,uBAAAwF,MAAA,CAAA0L,KAAA,yCAAA1L,MAAA,CAAA1F,KAAA,gBAAA0F,MAAA,CAAAxF,OAAA;cACA,GACAuI,IAAA,yDAEA;cACA4V,OAAA,CAAA1c,YAAA,CAAAmK,IAAA;gBACAmR,IAAA;gBACA/c,OAAA,qDAAAwF,MAAA,CAAA2Y,OAAA,CAAAngB,aAAA,CAAAqI,MAAA,gCAAAb,MAAA,CAAA0V,MAAA;cACA;;cAEA;cACAG,UAAA;gBACA0B,IAAA;gBACA/c,OAAA;cACA;cACAme,OAAA,CAAA1c,YAAA,CAAAmK,IAAA,CAAAyP,UAAA;;cAEA;cACAC,MAAA,GACA6C,OAAA,CAAAvb,eAAA,CACAgD,OAAA,kBAAAuY,OAAA,CAAAngB,aAAA,CAAAqI,MAAA,EACAT,OAAA,yFAAAJ,MAAA,CACA4V,eAAA,GAEA;cAAAwD,UAAA,CAAAzX,IAAA;cAAA,OACA,IAAA4X,cAAA,EAAAzD,MAAA;YAAA;cAAAjU,QAAA,GAAAuX,UAAA,CAAAnU,IAAA;cAAA,IACApD,QAAA,CAAA4V,EAAA;gBAAA2B,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAGA;cACArB,MAAA,GAAAlU,QAAA,CAAA6V,IAAA,CAAAC,SAAA;cACAgB,OAAA,CAAAzb,aAAA,GAAA6Y,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,cAAA,GAAAzW,IAAA,CAAAkX,GAAA;cACAR,cAAA;cACAC,UAAA,OAEA;cACA5C,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAAnX,IAAA,CAAAkX,GAAA;gBACA;gBACA,IAAAC,WAAA,GAAAV,cAAA;kBACAlD,UAAA,CAAArb,OAAA,GAAAud,UAAA;kBACAgB,cAAA,GAAAU,WAAA;kBACA;kBACAd,OAAA,CAAAT,SAAA;oBACA,IAAAjc,YAAA,GAAA0c,OAAA,CAAAnV,KAAA,CAAAvH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAyH,SAAA,GAAAzH,YAAA,CAAAkc,YAAA;oBACA;kBACA;gBACA;cACA,GAEA;cACAe,aAAA;gBAAA,IAAAQ,KAAA,OAAAvY,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAsY,UAAA;kBAAA,IAAAC,mBAAA,EAAApP,IAAA,EAAA7E,KAAA,EAAA8Q,KAAA,EAAAoD,KAAA,EAAAC,UAAA,EAAAC,MAAA,EAAApD,IAAA,EAAAC,QAAA,EAAAoD,SAAA,EAAAC,eAAA,EAAAC,aAAA;kBAAA,WAAA9Y,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAA4Y,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAA1Y,IAAA,GAAA0Y,UAAA,CAAAzY,IAAA;sBAAA;wBAAAyY,UAAA,CAAA1Y,IAAA;sBAAA;wBAAA,KAEA;0BAAA0Y,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAA,KAEAgX,OAAA,CAAA1b,SAAA;0BAAAmd,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAyV,KAAA;sBAAA;wBAAAgD,UAAA,CAAAzY,IAAA;wBAAA,OAGAoU,MAAA,CAAAqC,IAAA;sBAAA;wBAAAwB,mBAAA,GAAAQ,UAAA,CAAAnV,IAAA;wBAAAuF,IAAA,GAAAoP,mBAAA,CAAApP,IAAA;wBAAA7E,KAAA,GAAAiU,mBAAA,CAAAjU,KAAA;wBAAA,KACA6E,IAAA;0BAAA4P,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBACA,IAAAsU,MAAA,CAAApV,MAAA;0BACAwV,aAAA,CAAAJ,MAAA;wBACA;wBAAA,OAAAmE,UAAA,CAAAxY,MAAA;sBAAA;wBAIA6U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA3S,KAAA;wBACAkU,KAAA,GAAApD,KAAA,CAAAhW,KAAA,OAAAvC,MAAA,WAAAyY,IAAA;0BAAA,OAAAA,IAAA,CAAA7B,IAAA;wBAAA;wBAAAgF,UAAA,OAAAvO,2BAAA,CAAAtU,OAAA,EAEA4iB,KAAA;wBAAAO,UAAA,CAAA1Y,IAAA;wBAAAoY,UAAA,CAAA1N,CAAA;sBAAA;wBAAA,KAAA2N,MAAA,GAAAD,UAAA,CAAAzN,CAAA,IAAA7B,IAAA;0BAAA4P,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAAgV,IAAA,GAAAoD,MAAA,CAAApU,KAAA;wBAAAyU,UAAA,CAAA1Y,IAAA;wBAEAkV,QAAA,GAAApO,IAAA,CAAAC,KAAA,CAAAkO,IAAA;wBAAA,IACAC,QAAA,CAAA/U,QAAA;0BAAAuY,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAA,OAAAyY,UAAA,CAAAxY,MAAA;sBAAA;wBAEAC,SAAA,GAAA+U,QAAA,CAAA/U,QAAA,EAEA;wBAAA,MACAA,SAAA,cAAAA,SAAA;0BAAAuY,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAA,OAAAyY,UAAA,CAAAxY,MAAA;sBAAA;wBAIAqX,UAAA,IAAApX,SAAA;;wBAEA;sBAAA;wBAAA,KACA;0BAAAuY,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBACAsY,eAAA,GAAAhB,UAAA,CAAAnN,OAAA;wBACAoO,aAAA,GAAAjB,UAAA,CAAAnN,OAAA;wBAAA,MAEAmO,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBACA;wBACA,KAAAqX,cAAA;0BACA/C,MAAA,IAAAgD,UAAA;0BACA;0BACA5C,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAtc,eAAA;wBACA;wBACA4c,UAAA;wBAAA,OAAAmB,UAAA,CAAAxY,MAAA;sBAAA;wBAAA,MAEAqY,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBACA;wBACAqX,cAAA;wBACA,IAAAiB,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAAjI,SAAA,IAAAiJ,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAtc,eAAA;wBACA;wBACA4c,UAAA,GAAAA,UAAA,CAAAjI,SAAA,CAAAiJ,eAAA;wBAAA,OAAAG,UAAA,CAAAxY,MAAA;sBAAA;wBAAA,MAEAqY,eAAA,WAAAC,aAAA;0BAAAE,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBACA;wBACAqX,cAAA;wBACAC,UAAA,GAAAA,UAAA,CAAAjI,SAAA,CAAAkJ,aAAA;wBAAA,OAAAE,UAAA,CAAAxY,MAAA;sBAAA;wBAGA;wBACA,IAAAqY,eAAA;0BACAhE,MAAA,IAAAgD,UAAA,CAAAjI,SAAA,IAAAiJ,eAAA;0BACA;0BACA5D,aAAA,KAAA4B,cAAA,EAAAhC,MAAA,EAAA0C,OAAA,CAAAtc,eAAA;wBACA;wBACA4c,UAAA,GAAAA,UAAA,CAAAjI,SAAA,CAAAkJ,aAAA;wBACAlB,cAAA;wBAAA,OAAAoB,UAAA,CAAAxY,MAAA;sBAAA;wBAAAwY,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAA1Y,IAAA;wBAAA0Y,UAAA,CAAAtM,EAAA,GAAAsM,UAAA;wBAKApF,OAAA,CAAAqD,IAAA;0BACA1B,IAAA,EAAAA,IAAA;0BACAxK,KAAA,EAAAiO,UAAA,CAAAtM,EAAA,CAAAvT;wBACA;sBAAA;wBAAA6f,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAA1Y,IAAA;wBAAA0Y,UAAA,CAAA7N,EAAA,GAAA6N,UAAA;wBAAAN,UAAA,CAAAtN,CAAA,CAAA4N,UAAA,CAAA7N,EAAA;sBAAA;wBAAA6N,UAAA,CAAA1Y,IAAA;wBAAAoY,UAAA,CAAArN,CAAA;wBAAA,OAAA2N,UAAA,CAAA1N,MAAA;sBAAA;wBAAA0N,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAAzY,IAAA;wBAAA;sBAAA;wBAAAyY,UAAA,CAAA1Y,IAAA;wBAAA0Y,UAAA,CAAA3B,EAAA,GAAA2B,UAAA;wBAAA,MAKAA,UAAA,CAAA3B,EAAA,CAAAle,OAAA;0BAAA6f,UAAA,CAAAzY,IAAA;0BAAA;wBAAA;wBAAA,MACA,IAAAyV,KAAA;sBAAA;wBAEApC,OAAA,CAAA7I,KAAA,eAAAiO,UAAA,CAAA3B,EAAA;wBAAA,MAAA2B,UAAA,CAAA3B,EAAA;sBAAA;sBAAA;wBAAA,OAAA2B,UAAA,CAAA1X,IAAA;oBAAA;kBAAA,GAAAiX,SAAA;gBAAA,CAGA;gBAAA,gBAzFAT,cAAA;kBAAA,OAAAQ,KAAA,CAAAW,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAAlB,UAAA,CAAAzX,IAAA;cAAA,OA2FAuX,aAAA;YAAA;cAAAE,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAA7M,EAAA,GAAA6M,UAAA;cAAA,MAGAA,UAAA,CAAA7M,EAAA,CAAAhS,OAAA;gBAAA6e,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACAqT,OAAA,CAAAgC,GAAA;cAAA,OAAAoC,UAAA,CAAAxX,MAAA;YAAA;cAGAoT,OAAA,CAAA7I,KAAA,YAAAiN,UAAA,CAAA7M,EAAA;cACAoM,OAAA,CAAA7a,QAAA,CAAAqO,KAAA,CAAAiN,UAAA,CAAA7M,EAAA,CAAAhS,OAAA;cACA,IAAAoe,OAAA,CAAA1c,YAAA;gBACA0c,OAAA,CAAA1c,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAA4e,UAAA,CAAA1X,IAAA;cAEAiX,OAAA,CAAAzb,aAAA;cACA;cACA,IAAAyb,OAAA,CAAA3c,eAAA;gBACA2c,OAAA,CAAAzc,UAAA;gBACAyc,OAAA,CAAA3b,YAAA;cACA;cAAA,OAAAoc,UAAA,CAAA1M,MAAA;YAAA;YAAA;cAAA,OAAA0M,UAAA,CAAA1W,IAAA;UAAA;QAAA,GAAAkW,SAAA;MAAA;IAEA;IACA;IACA2B,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAArZ,kBAAA,CAAAlK,OAAA,mBAAAmK,oBAAA,CAAAnK,OAAA,IAAAoK,IAAA,UAAAoZ,UAAA;QAAA,IAAAhF,gBAAA,EAAAC,MAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAA8E,WAAA,EAAA5E,MAAA,EAAAjU,QAAA,EAAAkU,MAAA,EAAAC,OAAA,EAAAC,MAAA,EAAA0E,eAAA,EAAAtE,aAAA,EAAAuE,mBAAA,EAAApQ,IAAA,EAAA7E,KAAA,EAAA8Q,KAAA,EAAAoD,KAAA,EAAAgB,UAAA,EAAAC,MAAA,EAAAnE,IAAA,EAAAze,IAAA,EAAA6iB,iBAAA,EAAAnE,QAAA,EAAApc,OAAA;QAAA,WAAA4G,oBAAA,CAAAnK,OAAA,IAAAsK,IAAA,UAAAyZ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvZ,IAAA,GAAAuZ,UAAA,CAAAtZ,IAAA;YAAA;cAAA,MACA6Y,OAAA,CAAAhiB,aAAA,CAAAqI,MAAA;gBAAAoa,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,OAAAsZ,UAAA,CAAArZ,MAAA,WACA4Y,OAAA,CAAA1c,QAAA;gBACAvD,OAAA;gBACAxD,IAAA;cACA;YAAA;cAAA,KAIAyjB,OAAA,CAAAxd,YAAA;gBAAAie,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cACA6Y,OAAA,CAAAvd,SAAA;cAAA,KACAud,OAAA,CAAAtd,aAAA;gBAAA+d,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAtZ,IAAA;cAAA,OAEA6Y,OAAA,CAAAtd,aAAA,CAAA8U,MAAA;YAAA;cAAAiJ,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAnN,EAAA,GAAAmN,UAAA;cAEAjG,OAAA,CAAAgC,GAAA,sOAAAiE,UAAA,CAAAnN,EAAA;YAAA;cAAAmN,UAAA,CAAAtZ,IAAA;cAAA,OAIA,IAAAsV,OAAA,WAAAC,OAAA;gBAAA,OAAAhT,UAAA,CAAAgT,OAAA;cAAA;YAAA;cAGAsD,OAAA,CAAAxd,YAAA;cACAwd,OAAA,CAAAvd,SAAA;cACAud,OAAA,CAAAxe,eAAA;cACAwe,OAAA,CAAAve,YAAA;cACAue,OAAA,CAAAte,UAAA;cAEAuZ,gBAAA,GAAA+E,OAAA,CAAA9iB,WAAA,CAAAwG,MAAA,WAAAsI,OAAA;gBAAA,OACAgU,OAAA,CAAAhiB,aAAA,CAAAkH,QAAA,CAAA8G,OAAA,CAAA1B,EAAA;cAAA,CACA;cACA4Q,MAAA,GAAAD,gBAAA,CACA/U,GAAA,WAAA8F,OAAA;gBAAA,gBAAAxG,MAAA,CAAAwG,OAAA,CAAA9L,OAAA,IAAA8L,OAAA,CAAAlM,KAAA;cAAA,GACAyI,IAAA;cAAAkY,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAtZ,IAAA;cAAA,OAGA,IAAAwV,4BAAA,EACAqD,OAAA,CAAAhiB,aAAA,CAAAuK,IAAA,KACA;YAAA;cAFA4S,gBAAA,GAAAsF,UAAA,CAAAhW,IAAA;cAAA,MAGA,CAAA0Q,gBAAA,CAAAzd,IAAA,KAAAyd,gBAAA,CAAAzd,IAAA,CAAA2I,MAAA;gBAAAoa,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAGAxB,eAAA,GAAAD,gBAAA,CAAAzd,IAAA,CACAwI,GAAA,WAAA8F,OAAA,EAAAkF,KAAA;gBAAA,IAAAwP,sBAAA,EAAAC,sBAAA;gBACA,IAAA7gB,KAAA,GACA,EAAA4gB,sBAAA,GAAAzF,gBAAA,CAAA/J,KAAA,eAAAwP,sBAAA,uBAAAA,sBAAA,CAAAxgB,OAAA,OAAAygB,sBAAA,GACA1F,gBAAA,CAAA/J,KAAA,eAAAyP,sBAAA,uBAAAA,sBAAA,CAAA7gB,KAAA,KACA;gBACA,IAAAE,OAAA,GAAAgM,OAAA,CAAAhM,OAAA;gBACA,uBAAAwF,MAAA,CAAA0L,KAAA,yCAAA1L,MAAA,CAAA1F,KAAA,gBAAA0F,MAAA,CAAAxF,OAAA;cACA,GACAuI,IAAA,yDAEA;cACAyX,OAAA,CAAAve,YAAA,CAAAmK,IAAA;gBACAmR,IAAA;gBACA/c,OAAA,qDAAAwF,MAAA,CAAAwa,OAAA,CAAAhiB,aAAA,CAAAqI,MAAA,gCAAAb,MAAA,CAAA0V,MAAA;cACA;;cAEA;cACAG,WAAA;gBACA0B,IAAA;gBACA/c,OAAA;cACA;cACAggB,OAAA,CAAAve,YAAA,CAAAmK,IAAA,CAAAyP,WAAA;cACA2E,OAAA,CAAAte,UAAA;cAEA4Z,MAAA,GACA0E,OAAA,CAAApd,eAAA,CACAgD,OAAA,kBAAAoa,OAAA,CAAAhiB,aAAA,CAAAqI,MAAA,EACAT,OAAA,6FAAAJ,MAAA,CACA4V,eAAA;cAAAqF,UAAA,CAAAtZ,IAAA;cAAA,OAEA,IAAAyZ,gBAAA,EAAAtF,MAAA;YAAA;cAAAjU,QAAA,GAAAoZ,UAAA,CAAAhW,IAAA;cAAA,KAEApD,QAAA,CAAA4V,EAAA;gBAAAwD,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cACAoU,MAAA,GAAAlU,QAAA,CAAA6V,IAAA,CAAAC,SAAA;cACA6C,OAAA,CAAAtd,aAAA,GAAA6Y,MAAA;cACAC,OAAA,OAAA4B,WAAA;cACA3B,MAAA;cACA8C,eAAA,GAAAzW,IAAA,CAAAkX,GAAA;cAEAnD,aAAA,YAAAA,cAAA0B,UAAA;gBACA,IAAA0B,WAAA,GAAAnX,IAAA,CAAAkX,GAAA;gBACA,IAAAC,WAAA,GAAAV,eAAA;kBACAlD,WAAA,CAAArb,OAAA,GAAAud,UAAA;kBACAgB,eAAA,GAAAU,WAAA;kBACAe,OAAA,CAAAtC,SAAA;oBACA,IAAAjc,YAAA,GAAAue,OAAA,CAAAhX,KAAA,CAAAvH,YAAA;oBACA,IAAAA,YAAA;sBACAA,YAAA,CAAAyH,SAAA,GAAAzH,YAAA,CAAAkc,YAAA;oBACA;kBACA;gBACA;cACA;YAAA;cAAA,KAEA;gBAAA8C,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,KAEA6Y,OAAA,CAAAvd,SAAA;gBAAAge,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAyV,KAAA;YAAA;cAAA6D,UAAA,CAAAtZ,IAAA;cAAA,OAGAoU,MAAA,CAAAqC,IAAA;YAAA;cAAAwC,mBAAA,GAAAK,UAAA,CAAAhW,IAAA;cAAAuF,IAAA,GAAAoQ,mBAAA,CAAApQ,IAAA;cAAA7E,KAAA,GAAAiV,mBAAA,CAAAjV,KAAA;cAAA,KACA6E,IAAA;gBAAAyQ,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cACA,IAAAsU,MAAA,CAAApV,MAAA;gBACAwV,aAAA,CAAAJ,MAAA;cACA;cAAA,OAAAgF,UAAA,CAAArZ,MAAA;YAAA;cAIA6U,KAAA,GAAAT,OAAA,CAAAsC,MAAA,CAAA3S,KAAA;cAAAsV,UAAA,CAAAvZ,IAAA;cAEAmY,KAAA,GAAApD,KAAA,CAAAhW,KAAA;cAAAoa,UAAA,OAAAtP,2BAAA,CAAAtU,OAAA,EAEA4iB,KAAA;cAAAoB,UAAA,CAAAvZ,IAAA;cAAAmZ,UAAA,CAAAzO,CAAA;YAAA;cAAA,KAAA0O,MAAA,GAAAD,UAAA,CAAAxO,CAAA,IAAA7B,IAAA;gBAAAyQ,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAAgV,IAAA,GAAAmE,MAAA,CAAAnV,KAAA;cAAA,MACA,CAAAgR,IAAA,CAAA7B,IAAA,OAAA6B,IAAA,CAAA6B,UAAA;gBAAAyC,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,OAAAsZ,UAAA,CAAArZ,MAAA;YAAA;cAEA1J,IAAA,GAAAye,IAAA,CAAA4B,KAAA;cAAA,MACArgB,IAAA;gBAAA+iB,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,OAAAsZ,UAAA,CAAArZ,MAAA;YAAA;cAAAqZ,UAAA,CAAAvZ,IAAA;cAGAkV,QAAA,GAAApO,IAAA,CAAAC,KAAA,CAAAvQ,IAAA;cAAA,OAAA6iB,iBAAA,GACAnE,QAAA,CAAAyE,OAAA,cAAAN,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,iBAAAA,iBAAA,gBAAAA,iBAAA,GAAAA,iBAAA,CAAAO,KAAA,cAAAP,iBAAA,eAAAA,iBAAA,CAAAvgB,OAAA;gBAAAygB,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cACAnH,OAAA,GAAAoc,QAAA,CAAAyE,OAAA,IAAAC,KAAA,CAAA9gB,OAAA,EAEA;cAAA,MACAA,OAAA,cAAAA,OAAA;gBAAAygB,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cAAA,OAAAsZ,UAAA,CAAArZ,MAAA;YAAA;cAIAqU,MAAA,IAAAzb,OAAA;cACA6b,aAAA,CAAAJ,MAAA;YAAA;cAAAgF,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAA1O,EAAA,GAAA0O,UAAA;cAGAjG,OAAA,CAAA7I,KAAA,wBAAA8O,UAAA,CAAA1O,EAAA;YAAA;cAAA0O,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAxC,EAAA,GAAAwC,UAAA;cAAAJ,UAAA,CAAArO,CAAA,CAAAyO,UAAA,CAAAxC,EAAA;YAAA;cAAAwC,UAAA,CAAAvZ,IAAA;cAAAmZ,UAAA,CAAApO,CAAA;cAAA,OAAAwO,UAAA,CAAAvO,MAAA;YAAA;cAAAuO,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAM,EAAA,GAAAN,UAAA;cAIAjG,OAAA,CAAA7I,KAAA,4BAAA8O,UAAA,CAAAM,EAAA;YAAA;cAAAN,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAA,MAIA,IAAAyV,KAAA;YAAA;cAAA6D,UAAA,CAAAtZ,IAAA;cAAA;YAAA;cAAAsZ,UAAA,CAAAvZ,IAAA;cAAAuZ,UAAA,CAAAO,EAAA,GAAAP,UAAA;cAAA,MAIAA,UAAA,CAAAO,EAAA,CAAAjhB,OAAA;gBAAA0gB,UAAA,CAAAtZ,IAAA;gBAAA;cAAA;cACAqT,OAAA,CAAAgC,GAAA;cAAA,OAAAiE,UAAA,CAAArZ,MAAA;YAAA;cAGAoT,OAAA,CAAA7I,KAAA,mBAAA8O,UAAA,CAAAO,EAAA;cACAhB,OAAA,CAAA1c,QAAA,CAAAqO,KAAA;cACA,IAAAqO,OAAA,CAAAve,YAAA;gBACAue,OAAA,CAAAve,YAAA,IAAAzB,OAAA;cACA;YAAA;cAAAygB,UAAA,CAAAvZ,IAAA;cAEA8Y,OAAA,CAAAtd,aAAA;cACA;cACA,IAAAsd,OAAA,CAAAxe,eAAA;gBACAwe,OAAA,CAAAte,UAAA;gBACAse,OAAA,CAAAxd,YAAA;cACA;cAAA,OAAAie,UAAA,CAAAvO,MAAA;YAAA;YAAA;cAAA,OAAAuO,UAAA,CAAAvY,IAAA;UAAA;QAAA,GAAA+X,SAAA;MAAA;IAEA;IACA;IACAgB,aAAA,WAAAA,cAAA;MACA,KAAAxe,SAAA;MACA,SAAAC,aAAA;QACA,KAAAA,aAAA,CAAA8U,MAAA;MACA;MACA,KAAAhW,eAAA;MACA,KAAAC,YAAA;MACA,KAAAC,UAAA;MACA,KAAAc,YAAA;MACA,KAAAE,aAAA;IACA;IACAwe,aAAA,WAAAA,cAAA;MACA,SAAAve,UAAA;QACA,KAAAkY,UAAA;MACA,gBAAAlY,UAAA;QACA,KAAAub,YAAA;MACA,gBAAAvb,UAAA;QACA,KAAAod,cAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}